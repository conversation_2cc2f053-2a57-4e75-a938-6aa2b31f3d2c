import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../components/stores/settingsStore';
import { ProviderBridge } from '../utils/cepIntegration';

export const OpenRouterProvider: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { updateProviderKey } = useSettingsStore();
  const [key, setKey] = useState('');
  const [models, setModels] = useState<{ id: string; name: string }[]>([]);
  const [selectedModel, setSelectedModel] = useState('');

  useEffect(() => {
    ProviderBridge.listModels('openrouter', 'https://openrouter.ai/api/v1', key)
      .then((models: any) => setModels(models as { id: string; name: string }[]));
  }, [key]);

  return (
    <div className="space-y-4">
      {/* Select Model Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Select Model
        </label>
        {models.length ? (
          <select
            className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
            onChange={(e) => setSelectedModel(e.target.value)}
          >
            <option value="">Select a model...</option>
            {models.map((m) => (
              <option key={m.id} value={m.id}>{m.name}</option>
            ))}
          </select>
        ) : (
          <p className="text-xs text-adobe-text-secondary">No models found</p>
        )}
      </div>

      {/* Input API Key Section */}
      <div>
        <label className="block text-sm font-semibold text-adobe-text-primary mb-2">
          Input API Key
        </label>
        <input
          type="password"
          placeholder="sk-or-..."
          value={key}
          onChange={(e) => setKey(e.target.value)}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-2 py-1"
        />
      </div>

      {/* Save & Close Button */}
      <button
        onClick={() => { updateProviderKey('openrouter', key, selectedModel); onClose(); }}
        className="w-full bg-adobe-accent text-white rounded py-1"
      >
        Save & Close
      </button>
    </div>
  );
};
