import React, { useEffect, useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { ProviderStatusChecker } from '../../utils/cepIntegration';
import { Wifi, WifiOff, Loader2, Alert<PERSON>ircle } from 'lucide-react';

export const ProviderStatusIndicator: React.FC = () => {
  const { getActiveProvider } = useSettingsStore();
  const [status, setStatus] = useState<{
    isOnline: boolean | null;
    latency?: number;
    isChecking: boolean;
    error?: string;
  }>({
    isOnline: null,
    isChecking: false,
  });

  const activeProvider = getActiveProvider();

  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    const checkStatus = async () => {
      if (!activeProvider?.isConfigured) {
        setStatus({ isOnline: null, isChecking: false });
        return;
      }

      setStatus(prev => ({ ...prev, isChecking: true, error: undefined }));
      try {
        const result = await ProviderStatusChecker.checkProviderStatus(
          activeProvider.id,
          { apiKey: activeProvider.apiKey, baseURL: activeProvider.baseURL }
        );
        setStatus({
          isOnline: result.isOnline,
          latency: result.latency,
          isChecking: false,
        });
      } catch (error: any) {
        setStatus({
          isOnline: false,
          isChecking: false,
          error: error.message,
        });
      }
    };

    if (activeProvider?.isConfigured) {
      checkStatus();
      timeoutId = setInterval(checkStatus, 30_000); // 30-second poll
    }

    return () => {
      if (timeoutId) clearInterval(timeoutId);
    };
  }, [activeProvider]);

  /* ---------- helpers ---------- */
  const getIcon = () => {
    if (status.isChecking) return <Loader2 size={12} className="animate-spin" />;
    if (status.isOnline === true) return <Wifi size={12} />;
    if (status.isOnline === false) return <WifiOff size={12} />;
    return <AlertCircle size={12} />;
  };

  const getColor = () => {
    if (status.isChecking) return 'text-yellow-500';
    if (status.isOnline === true) return 'text-green-500';
    return 'text-red-500';
  };

  const getTooltip = () => {
    if (!activeProvider) return 'No provider selected';
    if (status.isChecking) return 'Checking connection…';
    if (status.isOnline === true) {
      return `${activeProvider.name} online${status.latency ? ` (${status.latency} ms)` : ''}`;
    }
    if (status.error) return `${activeProvider.name} error: ${status.error}`;
    return `${activeProvider.name} offline`;
  };

  /* ---------- render ---------- */
  return (
    <div
      className={`flex items-center space-x-1.5 ${getColor()}`}
      title={getTooltip()}
    >
      {getIcon()}
      <span className="text-xs font-medium hidden sm:inline">
        {status.isOnline === true
          ? 'Online'
          : status.isOnline === false
          ? 'Offline'
          : 'Unknown'}
      </span>
    </div>
  );
};
