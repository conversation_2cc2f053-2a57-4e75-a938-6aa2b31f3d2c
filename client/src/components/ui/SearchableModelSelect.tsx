import React, { useState, useRef, useEffect } from 'react';
import { Search } from 'lucide-react';

interface ModelOption {
  id: string;
  name: string;
}

interface SearchableModelSelectProps {
  models: ModelOption[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SearchableModelSelect: React.FC<SearchableModelSelectProps> = ({
  models,
  value,
  onChange,
  placeholder = "Search models..."
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    model.id.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedModel = models.find(m => m.id === value);

  const handleModelSelect = (modelId: string) => {
    onChange(modelId);
    setShowDropdown(false);
    setSearchQuery('');
  };

  const handleSearch = () => {
    if (filteredModels.length > 0) {
      handleModelSelect(filteredModels[0].id);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={showDropdown ? searchQuery : (selectedModel?.name || '')}
          onChange={(e) => {
            setSearchQuery(e.target.value);
            setShowDropdown(true);
          }}
          onFocus={() => {
            setShowDropdown(true);
            setSearchQuery('');
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded px-3 py-2 text-adobe-text-primary focus-within:border-adobe-accent outline-none pr-10"
          readOnly={!showDropdown}
        />
        <button
          onClick={handleSearch}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary"
        >
          <Search size={18} />
        </button>
      </div>

      {showDropdown && (
        <div className="absolute z-[9999] mt-1 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-lg max-h-52 overflow-auto">
          {filteredModels.length > 0 ? (
            filteredModels.map((model) => (
              <div
                key={model.id}
                className={`px-4 py-2 cursor-pointer ${
                  value === model.id
                    ? 'bg-adobe-accent text-white'
                    : 'hover:bg-adobe-bg-tertiary text-adobe-text-primary'
                }`}
                onClick={() => handleModelSelect(model.id)}
              >
                {model.name}
              </div>
            ))
          ) : (
            <div className="px-4 py-2 text-adobe-text-secondary">
              No models found
            </div>
          )}
        </div>
      )}
    </div>
  );
};
