import React, { useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { CEPSettings } from '../../utils/cepIntegration';
import { Download, Upload, Trash2, Save } from 'lucide-react';

interface SettingsData {
  theme: 'light' | 'dark' | 'auto';
  autoSave: boolean;
  showNotifications: boolean;
  maxHistoryItems: number;
  debugMode: boolean;
}

export const SettingsModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const [settings, setSettings] = useState<SettingsData>({
    theme: 'auto',
    autoSave: true,
    showNotifications: true,
    maxHistoryItems: 100,
    debugMode: false,
  });
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save settings to CEP storage
      await CEPSettings.save({ appSettings: settings });
      closeModal();
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const allSettings = await CEPSettings.exportSettings();
      const blob = new Blob([allSettings], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sahai-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export settings:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      setIsImporting(true);
      try {
        const text = await file.text();
        await CEPSettings.importSettings(text);
        // Reload the page or refresh settings
        window.location.reload();
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('Failed to import settings. Please check the file format.');
      } finally {
        setIsImporting(false);
      }
    };
    input.click();
  };

  const handleClearData = async () => {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      try {
        await CEPSettings.clearSettings();
        window.location.reload();
      } catch (error) {
        console.error('Failed to clear settings:', error);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Settings
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6 overflow-y-auto max-h-[60vh]">
          {/* Theme Settings */}
          <div>
            <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Appearance</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-adobe-text-secondary mb-2">Theme</label>
                <select
                  value={settings.theme}
                  onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                  className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"
                >
                  <option value="auto">Auto (System)</option>
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </div>
            </div>
          </div>

          {/* General Settings */}
          <div>
            <h3 className="text-sm font-medium text-adobe-text-primary mb-3">General</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.autoSave}
                  onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                  className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                />
                <span className="text-sm text-adobe-text-primary">Auto-save conversations</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.showNotifications}
                  onChange={(e) => setSettings(prev => ({ ...prev, showNotifications: e.target.checked }))}
                  className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                />
                <span className="text-sm text-adobe-text-primary">Show notifications</span>
              </label>

              <div>
                <label className="block text-sm text-adobe-text-secondary mb-2">
                  Max history items ({settings.maxHistoryItems})
                </label>
                <input
                  type="range"
                  min="10"
                  max="500"
                  step="10"
                  value={settings.maxHistoryItems}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxHistoryItems: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Advanced Settings */}
          <div>
            <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Advanced</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.debugMode}
                  onChange={(e) => setSettings(prev => ({ ...prev, debugMode: e.target.checked }))}
                  className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                />
                <span className="text-sm text-adobe-text-primary">Debug mode</span>
              </label>
            </div>
          </div>

          {/* Data Management */}
          <div>
            <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Data Management</h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleExport}
                disabled={isExporting}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-adobe-bg-secondary border border-adobe-border rounded-md text-adobe-text-primary hover:bg-adobe-bg-tertiary transition-colors disabled:opacity-50"
              >
                <Download size={16} />
                <span>{isExporting ? 'Exporting...' : 'Export'}</span>
              </button>

              <button
                onClick={handleImport}
                disabled={isImporting}
                className="flex items-center justify-center space-x-2 px-4 py-2 bg-adobe-bg-secondary border border-adobe-border rounded-md text-adobe-text-primary hover:bg-adobe-bg-tertiary transition-colors disabled:opacity-50"
              >
                <Upload size={16} />
                <span>{isImporting ? 'Importing...' : 'Import'}</span>
              </button>

              <button
                onClick={handleClearData}
                className="col-span-2 flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 border border-red-500 rounded-md text-white hover:bg-red-700 transition-colors"
              >
                <Trash2 size={16} />
                <span>Clear All Data</span>
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between">
          <button
            onClick={closeModal}
            className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center space-x-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
          >
            <Save size={16} />
            <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};
