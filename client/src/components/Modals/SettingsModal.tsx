import React, { useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { CEPSettings } from '../../utils/cepIntegration';
import { Download, Upload, Trash2, Save, X, ChevronDown, Info, HelpCircle, BarChart2 } from 'lucide-react';

interface SettingsData {
  theme: 'light' | 'dark' | 'auto';
  autoSave: boolean;
  showNotifications: boolean;
  maxHistoryItems: number;
  debugMode: boolean;
}

interface SettingLink {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export const SettingsModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const [settings, setSettings] = useState<SettingsData>({
    theme: 'auto',
    autoSave: true,
    showNotifications: true,
    maxHistoryItems: 100,
    debugMode: false,
  });
  const [activeTab, setActiveTab] = useState<'settings' | 'help' | 'about' | 'analytics'>('settings');
  const [isSaving, setIsSaving] = useState(false);

  const settingLinks: SettingLink[] = [
    {
      id: 'settings',
      title: 'General Settings',
      description: 'Configure application preferences',
      icon: <Save size={16} className="text-adobe-accent" />
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View usage statistics and performance metrics',
      icon: <BarChart2 size={16} className="text-adobe-accent" />
    },
    {
      id: 'help',
      title: 'Help & Support',
      description: 'Get help and find answers to common questions',
      icon: <HelpCircle size={16} className="text-adobe-accent" />
    },
    {
      id: 'about',
      title: 'About',
      description: 'Learn more about SahAI Extension',
      icon: <Info size={16} className="text-adobe-accent" />
    }
  ];

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await CEPSettings.save({ appSettings: settings });
      closeModal();
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Settings
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar Navigation */}
          <div className="w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto">
            <div className="space-y-1">
              {settingLinks.map((link) => (
                <button
                  key={link.id}
                  onClick={() => setActiveTab(link.id as any)}
                  className={`w-full text-left p-3 rounded-md transition-colors ${
                    activeTab === link.id
                      ? 'bg-adobe-accent/10 text-adobe-text-primary'
                      : 'text-adobe-text-secondary hover:bg-adobe-bg-tertiary'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 rounded-md bg-adobe-bg-tertiary">
                      {link.icon}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{link.title}</div>
                      <div className="text-xs mt-1">{link.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Content Area */}
          <div className="w-2/3 p-6 overflow-y-auto">
            {activeTab === 'settings' && (
              <div className="space-y-6">
                {/* Theme Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Appearance</h3>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm text-adobe-text-secondary mb-2">Theme</label>
                      <div className="relative">
                        <select
                          value={settings.theme}
                          onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                          className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none"
                        >
                          <option value="auto">Auto (System)</option>
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                        </select>
                        <ChevronDown 
                          size={16} 
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none" 
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* General Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">General</h3>
                  <div className="space-y-3">
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.autoSave}
                        onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Auto-save conversations</span>
                    </label>

                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.showNotifications}
                        onChange={(e) => setSettings(prev => ({ ...prev, showNotifications: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Show notifications</span>
                    </label>

                    <div>
                      <label className="block text-sm text-adobe-text-secondary mb-2">
                        Max history items ({settings.maxHistoryItems})
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="500"
                        step="10"
                        value={settings.maxHistoryItems}
                        onChange={(e) => setSettings(prev => ({ ...prev, maxHistoryItems: parseInt(e.target.value) }))}
                        className="w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"
                      />
                    </div>
                  </div>
                </div>

                {/* Advanced Settings */}
                <div>
                  <h3 className="text-sm font-medium text-adobe-text-primary mb-3">Advanced</h3>
                  <div className="space-y-3">
                    <label className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={settings.debugMode}
                        onChange={(e) => setSettings(prev => ({ ...prev, debugMode: e.target.checked }))}
                        className="w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"
                      />
                      <span className="text-sm text-adobe-text-primary">Debug mode</span>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div className="flex items-center justify-center h-full text-adobe-text-secondary">
                <div className="text-center">
                  <BarChart2 size={48} className="mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium text-adobe-text-primary mb-2">Analytics</h3>
                  <p className="text-sm">Usage statistics and performance metrics will appear here</p>
                </div>
              </div>
            )}

            {activeTab === 'help' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-adobe-text-primary mb-2">Help & Support</h3>
                <div className="space-y-3">
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">Documentation</h4>
                    <p className="text-sm text-adobe-text-secondary">Read our comprehensive documentation for detailed guides.</p>
                  </div>
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">FAQ</h4>
                    <p className="text-sm text-adobe-text-secondary">Find answers to frequently asked questions.</p>
                  </div>
                  <div className="p-4 bg-adobe-bg-secondary rounded-md">
                    <h4 className="font-medium text-adobe-text-primary mb-2">Contact Support</h4>
                    <p className="text-sm text-adobe-text-secondary">Email <NAME_EMAIL> for assistance.</p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'about' && (
              <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary">
                <div className="text-center">
                  <Info size={48} className="mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium text-adobe-text-primary mb-2">About SahAI</h3>
                  <p className="text-sm mb-4">Version 2.0.0</p>
                  <p className="text-sm">AI-powered assistant for Adobe Creative Suite</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between">
          <div className="text-xs text-adobe-text-secondary">
            {activeTab === 'settings' && 'Application Preferences'}
            {activeTab === 'analytics' && 'Usage Statistics'}
            {activeTab === 'help' && 'Help Resources'}
            {activeTab === 'about' && 'About SahAI'}
          </div>
          {activeTab === 'settings' && (
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
            >
              <Save size={16} />
              <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};