import React, { useEffect, useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useHistoryStore, ChatSession } from '../stores/historyStore';
import { useChatStore } from '../stores/chatStore';
import { Search, Trash2, MessageSquare, Calendar, X, ChevronDown } from 'lucide-react';

export const ChatHistoryModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { 
    sessions, 
    isLoading, 
    error, 
    loadHistory, 
    deleteSession, 
    clearHistory,
    setCurrentSession,
    getSortedSessions 
  } = useHistoryStore();
  const { messages: currentMessages } = useChatStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'alphabetical'>('recent');

  // Load history on mount
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  const filteredSessions = getSortedSessions()
    .filter(session =>
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.messages.some(msg => 
        msg.content.toLowerCase().includes(searchQuery.toLowerCase())
      )
    )
    .sort((a, b) => {
      if (sortBy === 'alphabetical') {
        return a.title.localeCompare(b.title);
      } else if (sortBy === 'oldest') {
        return a.createdAt - b.createdAt;
      } else {
        // Default to recent first
        return b.createdAt - a.createdAt;
      }
    });

  const handleSessionSelect = (session: ChatSession) => {
    setCurrentSession(session.id);
    closeModal();
  };

  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this chat session?')) {
      await deleteSession(sessionId);
    }
  };

  const handleClearAll = async () => {
    if (confirm('Are you sure you want to delete all chat history? This action cannot be undone.')) {
      await clearHistory();
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });
    }
  };

  const getMessagePreview = (session: ChatSession) => {
    const lastMessage = session.messages[session.messages.length - 1];
    if (!lastMessage) return 'No messages';
    
    const preview = lastMessage.content.slice(0, 100);
    return preview.length < lastMessage.content.length ? `${preview}...` : preview;
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Chat History
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="p-4 border-b border-adobe-border">
          <div className="flex items-center gap-3">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search chat history..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"
              />
              <button 
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary"
                onClick={() => setSearchQuery('')}
              >
                {searchQuery ? (
                  <X size={16} />
                ) : (
                  <Search size={16} />
                )}
              </button>
            </div>
            
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer"
              >
                <option value="recent">Most Recent</option>
                <option value="oldest">Oldest First</option>
                <option value="alphabetical">Alphabetical</option>
              </select>
              <ChevronDown 
                size={16} 
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none" 
              />
            </div>
            
            <button
              onClick={handleClearAll}
              disabled={sessions.length === 0}
              className="px-3 py-2 bg-adobe-error text-white rounded-md hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
            >
              <Trash2 size={16} />
              <span>Clear All</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"></div>
            </div>
          ) : error ? (
            <div className="p-4 text-center text-adobe-error">
              <p>Error loading history:</p>
              <p className="text-sm">{error}</p>
            </div>
          ) : filteredSessions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2">
              <MessageSquare size={48} className="opacity-50" />
              <h3 className="text-lg font-medium text-adobe-text-primary">
                {searchQuery ? 'No matching sessions found' : 'No chat history yet'}
              </h3>
              <p className="text-sm">
                {searchQuery ? 'Try a different search term' : 'Start a new conversation to see it here'}
              </p>
            </div>
          ) : (
            <div className="h-full overflow-y-auto p-2 space-y-2">
              {filteredSessions.map((session) => (
                <div
                  key={session.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    selectedSession?.id === session.id 
                      ? 'bg-adobe-accent/10 border-l-2 border-adobe-accent' 
                      : 'bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary'
                  }`}
                  onClick={() => setSelectedSession(session)}
                >
                  <div className="flex justify-between items-start gap-2">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-adobe-text-primary truncate">
                        {session.title}
                      </h3>
                      <p className="text-sm text-adobe-text-secondary mt-1 line-clamp-2">
                        {getMessagePreview(session)}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1 text-xs text-adobe-text-secondary">
                        <MessageSquare size={12} />
                        <span>{session.messages.length}</span>
                      </div>
                      <button
                        onClick={(e) => handleDeleteSession(session.id, e)}
                        className="text-adobe-text-secondary hover:text-adobe-error transition-colors p-1"
                        title="Delete session"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mt-2">
                    <div className="flex items-center gap-1 text-xs text-adobe-text-secondary">
                      <Calendar size={12} />
                      <span>{formatDate(session.createdAt)}</span>
                    </div>
                    {session.provider && (
                      <span className="text-xs px-2 py-1 rounded-full bg-adobe-success/10 text-adobe-success">
                        {session.provider}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-right">
          Showing {filteredSessions.length} of {sessions.length} chat sessions
        </div>
      </div>
    </div>
  );
};