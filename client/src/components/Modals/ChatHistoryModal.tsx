import React, { useEffect, useState } from 'react';
import { useModalStore } from '../stores/modalStore';
import { useHistoryStore, ChatSession } from '../stores/historyStore';
import { useChatStore } from '../stores/chatStore';
import { Search, Trash2, MessageSquare, Calendar, Loader2 } from 'lucide-react';

export const ChatHistoryModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { 
    sessions, 
    isLoading, 
    error, 
    loadHistory, 
    deleteSession, 
    clearHistory,
    setCurrentSession,
    getSortedSessions 
  } = useHistoryStore();
  const { messages: currentMessages } = useChatStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null);

  // Load history on mount
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  const filteredSessions = getSortedSessions().filter(session =>
    session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    session.messages.some(msg => 
      msg.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const handleSessionSelect = (session: ChatSession) => {
    setCurrentSession(session.id);
    // Load the session messages into the chat
    // This would typically involve updating the chat store with the session messages
    closeModal();
  };

  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this chat session?')) {
      await deleteSession(sessionId);
    }
  };

  const handleClearAll = async () => {
    if (confirm('Are you sure you want to delete all chat history? This action cannot be undone.')) {
      await clearHistory();
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' });
    }
  };

  const getMessagePreview = (session: ChatSession) => {
    const lastMessage = session.messages[session.messages.length - 1];
    if (!lastMessage) return 'No messages';
    
    const preview = lastMessage.content.slice(0, 100);
    return preview.length < lastMessage.content.length ? `${preview}...` : preview;
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              Chat History
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="p-4 border-b border-adobe-border">
          <div className="flex items-center space-x-3">
            <div className="relative flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search chat history..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-2 pl-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" size={16} />
            </div>
            <button
              onClick={handleClearAll}
              disabled={sessions.length === 0}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Trash2 size={16} />
              <span>Clear All</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Session List */}
          <div className="w-1/2 border-r border-adobe-border overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="animate-spin text-adobe-text-secondary" size={24} />
              </div>
            ) : error ? (
              <div className="p-4 text-center text-red-500">
                <p>Error loading history:</p>
                <p className="text-sm">{error}</p>
              </div>
            ) : filteredSessions.length === 0 ? (
              <div className="p-4 text-center text-adobe-text-secondary">
                {searchQuery ? 'No matching sessions found' : 'No chat history yet'}
              </div>
            ) : (
              <div className="divide-y divide-adobe-border">
                {filteredSessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-4 cursor-pointer hover:bg-adobe-bg-tertiary transition-colors ${
                      selectedSession?.id === session.id ? 'bg-adobe-bg-tertiary' : ''
                    }`}
                    onClick={() => setSelectedSession(session)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-adobe-text-primary truncate">
                          {session.title}
                        </h3>
                        <p className="text-sm text-adobe-text-secondary mt-1 line-clamp-2">
                          {getMessagePreview(session)}
                        </p>
                        <div className="flex items-center space-x-3 mt-2 text-xs text-adobe-text-secondary">
                          <div className="flex items-center space-x-1">
                            <MessageSquare size={12} />
                            <span>{session.messages.length} messages</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar size={12} />
                            <span>{formatDate(session.updatedAt)}</span>
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={(e) => handleDeleteSession(session.id, e)}
                        className="ml-2 p-1 text-adobe-text-secondary hover:text-red-500 transition-colors"
                        title="Delete session"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Session Preview */}
          <div className="w-1/2 flex flex-col">
            {selectedSession ? (
              <>
                <div className="p-4 border-b border-adobe-border">
                  <h3 className="font-medium text-adobe-text-primary">{selectedSession.title}</h3>
                  <p className="text-sm text-adobe-text-secondary mt-1">
                    {selectedSession.messages.length} messages • {formatDate(selectedSession.createdAt)}
                  </p>
                  {selectedSession.provider && (
                    <p className="text-xs text-adobe-text-secondary mt-1">
                      {selectedSession.provider} • {selectedSession.model}
                    </p>
                  )}
                </div>
                <div className="flex-1 overflow-y-auto p-4 space-y-3">
                  {selectedSession.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-3 rounded-lg ${
                        message.role === 'user'
                          ? 'bg-adobe-accent/10 ml-8'
                          : 'bg-adobe-bg-secondary mr-8'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-adobe-text-secondary uppercase">
                          {message.role}
                        </span>
                        <span className="text-xs text-adobe-text-secondary">
                          {formatDate(message.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm text-adobe-text-primary whitespace-pre-wrap">
                        {message.content}
                      </p>
                    </div>
                  ))}
                </div>
                <div className="p-4 border-t border-adobe-border">
                  <button
                    onClick={() => handleSessionSelect(selectedSession)}
                    className="w-full px-4 py-2 bg-adobe-accent text-white rounded-md hover:bg-adobe-accent/90 transition-colors"
                  >
                    Load This Conversation
                  </button>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center text-adobe-text-secondary">
                <div className="text-center">
                  <MessageSquare size={48} className="mx-auto mb-4 opacity-50" />
                  <p>Select a session to preview</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
