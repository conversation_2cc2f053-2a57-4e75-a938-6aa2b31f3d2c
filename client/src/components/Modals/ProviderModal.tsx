import React, { useState, useEffect, useRef } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { Search, Loader2 } from 'lucide-react';
import { ProviderBridge } from '../../utils/cepIntegration';
import { ProviderLogo } from '../ui/ProviderLogo';
import type { Model } from '../stores/settingsStore';



const providerOptions = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'anthropic', label: 'Anthropic' },
  { value: 'gemini', label: 'Google Gemini' },
  { value: 'groq', label: 'Groq' },
  { value: 'deepseek', label: 'DeepSeek' },
  { value: 'mistral', label: 'Mistral' },
  { value: 'moonshot', label: 'Moonshot AI' },
  { value: 'openrouter', label: 'OpenRouter' },
  { value: 'perplexity', label: 'Perplexity' },
  { value: 'qwen', label: '<PERSON><PERSON><PERSON> Qwen' },
  { value: 'together', label: 'Together AI' },
  { value: 'vertex', label: 'Google Vertex AI' },
  { value: 'xai', label: 'xAI' },
  { value: 'ollama', label: 'Ollama' },
  { value: 'lmstudio', label: 'LM Studio' },
];



export const ProviderModal: React.FC = () => {
  const { closeModal } = useModalStore();
  const { providers, saveProviderSelection, loadModelsForProvider } = useSettingsStore();
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [providerSearch, setProviderSearch] = useState('');
  const [modelSearch, setModelSearch] = useState('');
  const [showProviderDropdown, setShowProviderDropdown] = useState(false);
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const providerRef = useRef<HTMLDivElement>(null);
  const modelRef = useRef<HTMLDivElement>(null);

  const filteredProviders = providerOptions.filter(provider =>
    provider.label.toLowerCase().includes(providerSearch.toLowerCase())
  );

  // Get provider state from the store
  const providerState = providers.find(p => p.id === selectedProvider);
  const models = providerState?.models || [];

  const filteredModels = models.filter(model =>
    model.name.toLowerCase().includes(modelSearch.toLowerCase())
  );

  const selectedProviderData = providerOptions.find(p => p.value === selectedProvider);

  // Trigger model loading when provider is selected and has API key
  useEffect(() => {
    if (selectedProvider && apiKey) {
      // Update the provider with the API key first, then load models
      const provider = providers.find(p => p.id === selectedProvider);
      if (provider) {
        // Update the provider config with the API key
        saveProviderSelection(selectedProvider, { apiKey });
        // Then load models
        loadModelsForProvider(selectedProvider);
      }
    }
  }, [selectedProvider, apiKey]);

  const handleProviderSelect = (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedModel('');
    setModelSearch('');
    setProviderSearch(providerOptions.find(p => p.value === providerId)?.label || '');
    setShowProviderDropdown(false);

    // Trigger model loading from the store if API key is available
    if (apiKey) {
      loadModelsForProvider(providerId);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
    const model = models.find(m => m.id === modelId);
    setModelSearch(model?.name || '');
    setShowModelDropdown(false);
  };

  const handleSave = () => {
    if (selectedProvider && selectedModel && apiKey) {
      saveProviderSelection(selectedProvider, {
        apiKey: apiKey,
        selectedModelId: selectedModel
      });
      closeModal();
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (providerRef.current && !providerRef.current.contains(event.target as Node)) {
        setShowProviderDropdown(false);
      }
      if (modelRef.current && !modelRef.current.contains(event.target as Node)) {
        setShowModelDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl">
        {/* Header */}
        <div className="bg-adobe-bg-secondary border-b border-adobe-border p-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-adobe-text-primary">
              AI Provider Configuration
            </h2>
            <button
              onClick={closeModal}
              className="text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Provider Selector */}
          <div className="relative" ref={providerRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Provider
            </label>
            <div className="relative">
              <input
                type="text"
                value={providerSearch}
                onChange={(e) => {
                  setProviderSearch(e.target.value);
                  setShowProviderDropdown(true);
                }}
                onFocus={() => setShowProviderDropdown(true)}
                placeholder="Search providers..."
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" size={18} />
            </div>

            {showProviderDropdown && (
              <div className="absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
                {filteredProviders.map((provider) => (
                  <div
                    key={provider.value}
                    className="px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary text-adobe-text-primary"
                    onClick={() => handleProviderSelect(provider.value)}
                  >
                    <ProviderLogo provider={provider.value} size={16} />
                    <span className="font-medium">{provider.label}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Model Selector */}
          <div className="relative" ref={modelRef}>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              Model
            </label>
            <div className="relative">
              <input
                type="text"
                value={modelSearch}
                onChange={(e) => {
                  setModelSearch(e.target.value);
                  setShowModelDropdown(true);
                }}
                onFocus={() => selectedProvider && setShowModelDropdown(true)}
                placeholder={selectedProvider ? "Search models..." : "Select a provider first"}
                disabled={!selectedProvider}
                className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary" size={18} />
              {providerState?.isLoading && (
                <Loader2 className="absolute right-10 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary animate-spin" size={16} />
              )}
            </div>

            {showModelDropdown && selectedProvider && (
              <div className="absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto">
                {providerState?.isLoading && models.length === 0 && (
                  <div className="px-4 py-3 text-adobe-text-secondary flex items-center space-x-2">
                    <Loader2 className="animate-spin" size={16} />
                    <span>Loading models...</span>
                  </div>
                )}

                {providerState?.error && models.length === 0 && (
                  <div className="px-4 py-3 text-adobe-text-secondary">
                    {providerState.error}
                  </div>
                )}
                
                {filteredModels.map((model) => (
                  <div
                    key={model.id}
                    className="px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary text-adobe-text-primary"
                    onClick={() => handleModelSelect(model.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{model.name}</span>
                      {model.isRecommended && (
                        <span className="text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded">
                          Recommended
                        </span>
                      )}
                    </div>
                    {model.description && (
                      <p className="text-xs text-adobe-text-secondary mt-1">{model.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* API Key Input */}
          <div>
            <label className="block text-sm font-medium text-adobe-text-primary mb-2">
              API Key
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Enter your API key"
              className="w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"
            />
          </div>
        </div>

        {/* Footer */}
        <div className="bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between">
          <button
            onClick={closeModal}
            className="px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!selectedProvider || !selectedModel || !apiKey}
            className="px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors"
          >
            Save & Close
          </button>
        </div>
      </div>
    </div>
  );
};
