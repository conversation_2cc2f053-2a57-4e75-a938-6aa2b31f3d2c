import { create } from 'zustand';
import { CEPSettings } from '../../utils/cepIntegration';

export interface Model {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  capabilities?: string[];
  isRecommended?: boolean;
}

interface Provider {
  id: string;
  name: string;
  isConfigured: boolean;
  apiKey?: string;
  baseURL?: string;
  models: Model[];
  selectedModelId?: string;
  isLoading?: boolean;
  error?: string;
  settings?: Record<string, unknown>;
}

interface SettingsState {
  providers: Provider[];
  activeProviderId: string | undefined;
  isLoadingModels: boolean;

  // Provider actions
  setActiveProvider: (providerId: string) => void;
  updateProviderConfig: (providerId: string, config: Partial<Provider>) => void;
  setProviderModels: (providerId: string, models: Model[]) => void;
  setSelectedModel: (providerId: string, modelId: string) => void;
  updateProviderKey: (providerId: string, apiKey: string, selectedModelId?: string) => void;
  saveProviderSelection: (providerId: string, config: Partial<Provider>) => void;

  // Model actions
  loadModelsForProvider: (providerId: string) => Promise<void>;

  // CEP integration
  persistSettings: () => void;
  loadSettings: () => Promise<void>;

  // Computed getters
  getActiveProvider: () => Provider | null;
  getActiveModel: () => Model | null;
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  providers: [
    { id: 'openai', name: 'OpenAI', isConfigured: false, models: [] },
    { id: 'anthropic', name: 'Anthropic', isConfigured: false, models: [] },
    { id: 'gemini', name: 'Google Gemini', isConfigured: false, models: [] },
    { id: 'groq', name: 'Groq', isConfigured: false, models: [] },
    { id: 'deepseek', name: 'DeepSeek', isConfigured: false, models: [] },
    { id: 'mistral', name: 'Mistral', isConfigured: false, models: [] },
    { id: 'moonshot', name: 'Moonshot AI', isConfigured: false, models: [] },
    { id: 'openrouter', name: 'OpenRouter', isConfigured: false, models: [] },
    { id: 'perplexity', name: 'Perplexity', isConfigured: false, models: [] },
    { id: 'qwen', name: 'Alibaba Qwen', isConfigured: false, models: [] },
    { id: 'together', name: 'Together AI', isConfigured: false, models: [] },
    { id: 'vertex', name: 'Google Vertex AI', isConfigured: false, models: [] },
    { id: 'xai', name: 'xAI', isConfigured: false, models: [] },
    { id: 'ollama', name: 'Ollama', isConfigured: false, models: [] },
    { id: 'lmstudio', name: 'LM Studio', isConfigured: false, models: [] },
  ],
  activeProviderId: undefined,
  isLoadingModels: false,

  setActiveProvider: (providerId) => {
    set({ activeProviderId: providerId });
    get().persistSettings();
  },

  updateProviderConfig: (providerId, config) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, ...config, isConfigured: !!config.apiKey } : p
      )
    }));
    get().persistSettings();
  },

  setProviderModels: (providerId, models) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, models, isLoading: false, error: undefined } : p
      )
    }));
  },

  setSelectedModel: (providerId, modelId) => {
    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, selectedModelId: modelId } : p
      )
    }));
    get().persistSettings();
  },

  updateProviderKey: (providerId, apiKey, selectedModelId) => {
    set(state => ({
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, apiKey, isConfigured: !!apiKey, selectedModelId: selectedModelId || p.selectedModelId }
          : p
      )
    }));
    get().persistSettings();
  },

  saveProviderSelection: (providerId, config) => {
    set(state => ({
      activeProviderId: providerId, // The missing piece - update active provider
      providers: state.providers.map(p =>
        p.id === providerId
          ? { ...p, ...config, isConfigured: !!(config.apiKey || p.baseURL) }
          : p
      )
    }));
    get().persistSettings(); // Persist all changes at once
  },

  loadModelsForProvider: async (providerId) => {
    const provider = get().providers.find(p => p.id === providerId);
    if (!provider?.isConfigured) return;

    set(state => ({
      providers: state.providers.map(p => 
        p.id === providerId ? { ...p, isLoading: true, error: undefined } : p
      )
    }));

    try {
      // Use CEP bridge to get models
      const { ProviderBridge } = await import('../../utils/cepIntegration');
      const models = await ProviderBridge.listModels(
        providerId, 
        provider.baseURL, 
        provider.apiKey
      );
      
      // Transform to Model interface
      const transformedModels: Model[] = (models as any[]).map((m: any) => ({
        id: m.id,
        name: m.name,
        description: m.description,
        contextLength: m.contextLength,
        isRecommended: m.isRecommended
      }));
      
      get().setProviderModels(providerId, transformedModels);
    } catch (error: any) {
      set(state => ({
        providers: state.providers.map(p => 
          p.id === providerId ? { ...p, isLoading: false, error: (error as Error).message || String(error) } : p
        )
      }));
    }
  },

  persistSettings: () => {
    const { activeProviderId, providers } = get();
    CEPSettings.save({
      activeProviderId,
      providers: providers.map(p => ({
        id: p.id,
        isConfigured: p.isConfigured,
        apiKey: p.apiKey,
        baseURL: p.baseURL,
        selectedModelId: p.selectedModelId,
        settings: p.settings
      }))
    });
  },

  loadSettings: async () => {
    try {
      const settings = await CEPSettings.load();
      if (settings.activeProviderId) {
        set({ activeProviderId: settings.activeProviderId });
      }
      if (settings.providers && Array.isArray(settings.providers)) {
        set(state => ({
          providers: state.providers.map(p => {
            const saved = settings.providers?.find(sp => sp.id === p.id);
            return saved ? { ...p, ...saved } : p;
          })
        }));
      }
    } catch (error) {
      console.error('Failed to load CEP settings:', error);
    }
  },

  getActiveProvider: () => {
    const { providers, activeProviderId } = get();
    return providers.find(p => p.id === activeProviderId) || null;
  },

  getActiveModel: () => {
    const activeProvider = get().getActiveProvider();
    if (!activeProvider?.selectedModelId) return null;
    return activeProvider.models.find(m => m.id === activeProvider.selectedModelId) || null;
  }
}));
