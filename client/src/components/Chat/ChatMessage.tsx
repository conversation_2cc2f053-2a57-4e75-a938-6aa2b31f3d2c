import React from 'react';
import { Message } from '../stores/chatStore';
import { ShikiCodeBlock } from './ShikiCodeBlock';

interface ChatMessageProps {
  message: Message;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div
        className={`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${
          isUser
            ? 'bg-[#404040] text-adobe-text-primary ml-8 rounded-br-md'
            : 'bg-[#2a2a2a] text-adobe-text-primary mr-8 rounded-bl-md border border-[#3a3a3a]'
        }`}
      >
        <div className="whitespace-pre-wrap">
          <ShikiCodeBlock content={message.content} />
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
