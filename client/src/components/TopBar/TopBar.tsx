import React, { useMemo, useEffect } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { useModalStore } from '../stores/modalStore';
import { useChatStore } from '../stores/chatStore';
import { ProviderStatusIndicator } from '../ui/ProviderStatusIndicator';
import { Plus, History, Settings, ChevronDown } from 'lucide-react';

export const TopBar: React.FC = () => {
  const { getActiveProvider, getActiveModel, loadSettings } = useSettingsStore();
  const { openModal } = useModalStore();
  const { createNewSession } = useChatStore();

  const activeProvider = getActiveProvider();
  const activeModel = getActiveModel();

  const display = useMemo(() => {
    if (!activeProvider) return 'Select AI Provider & Model';
    if (!activeModel) return `${activeProvider.name} • Select Model`;
    return `${activeProvider.name} • ${activeModel.name}`;
  }, [activeProvider, activeModel]);

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return (
    <header className="flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm">
      <div className="flex items-center space-x-3">
        <ProviderStatusIndicator />
        <div className="h-4 w-px bg-adobe-border" />
        <button
          onClick={() => openModal('provider')}
          className="flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group"
          title="Select AI Provider & Model"
        >
          <span className="max-w-[300px] truncate">{display}</span>
          <ChevronDown 
            size={14} 
            className="text-adobe-text-secondary group-hover:text-adobe-accent transition-colors" 
          />
        </button>
      </div>

      <div className="flex items-center space-x-2">
        <button 
          onClick={() => openModal('chat-history')} 
          className="p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all"
          title="Chat History"
        >
          <History size={16} />
        </button>
        <button 
          onClick={() => openModal('settings')} 
          className="p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all"
          title="Settings"
        >
          <Settings size={16} />
        </button>
        <div className="h-4 w-px bg-adobe-border" />
        <button
          onClick={createNewSession}
          className="p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all"
          title="New Chat"
        >
          <Plus size={16} />
        </button>
      </div>
    </header>
  );
};

export default TopBar;
