import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { initializeCEP } from './utils/cepIntegration';
import { useSettingsStore } from './components/stores/settingsStore';

// Initialize CEP environment if running in CEP
initializeCEP();

// Load settings on app start
useSettingsStore.getState().loadSettings();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
