/**
 * CEP Integration Utilities
 * Provides a wrapper around the CSInterface API for Adobe CEP extensions
 */

// Global CSInterface instance
let csInterface: any = null;

// Check if we're running in a CEP environment
export const isCEPEnvironment = (): boolean => {
  return typeof window !== 'undefined' && !!(window as any).CSInterface;
};

// Get or create CSInterface instance
export const getCSInterface = (): any => {
  if (!csInterface && isCEPEnvironment()) {
    try {
      csInterface = new (window as any).CSInterface();
      console.log('CSInterface initialized successfully');
    } catch (error) {
      console.error('Failed to initialize CSInterface:', error);
    }
  }
  return csInterface;
};

// Initialize CEP environment
export const initializeCEP = (): void => {
  if (!isCEPEnvironment()) {
    console.warn('Not running in CEP environment');
    return;
  }

  const cs = getCSInterface();
  if (!cs) return;

  // Set up theme change listener
  cs.addEventListener('com.adobe.csxs.events.ThemeColorChanged', (event: any) => {
    console.log('Theme changed:', event);
    // Handle theme changes here
  });

  // Log host environment
  const hostEnv = cs.getHostEnvironment();
  console.log('Host environment:', hostEnv);

  // Test ExtendScript communication
  cs.evalScript('SahAI.getAppInfo()', (result: string) => {
    try {
      if (!result || result.trim() === '') {
        console.warn('Empty response from ExtendScript');
        return;
      }
      const parsed = JSON.parse(result);
      console.log('ExtendScript response:', parsed);
    } catch (error) {
      console.error('Failed to parse ExtendScript response:', error, 'Raw result:', result);
    }
  });
};

// Execute ExtendScript code with enhanced error handling and timeout
export const executeExtendScript = (code: string, timeout: number = 30000): Promise<any> => {
  return new Promise((resolve, reject) => {
    const cs = getCSInterface();
    if (!cs) {
      reject(new Error('CSInterface not available - not running in CEP environment'));
      return;
    }

    // Set up timeout
    const timeoutId = setTimeout(() => {
      reject(new Error(`ExtendScript execution timed out after ${timeout}ms`));
    }, timeout);

    try {
      cs.evalScript(code, (result: string) => {
        clearTimeout(timeoutId);

        try {
          // Handle error responses from ExtendScript
          if (typeof result === 'string' && result.startsWith('EvalScript error')) {
            reject(new Error(`ExtendScript Error: ${result}`));
            return;
          }

          if (!result || result.trim() === '') {
            reject(new Error('Empty response from ExtendScript'));
            return;
          }

          // Handle both JSON and plain string responses
          let parsed;
          try {
            parsed = JSON.parse(result);
          } catch (parseError) {
            // If it's not JSON, treat as plain string
            parsed = { success: true, data: result };
          }

          if (typeof parsed === 'object' && parsed !== null) {
            if (parsed.success === false) {
              reject(new Error(parsed.message || 'ExtendScript execution failed'));
            } else {
              resolve(parsed);
            }
          } else {
            resolve({ success: true, data: parsed });
          }
        } catch (error) {
          reject(new Error(`Failed to process ExtendScript response: ${error}`));
        }
      });
    } catch (error) {
      clearTimeout(timeoutId);
      reject(new Error(`Failed to execute ExtendScript: ${error}`));
    }
  });
};

// Get application information
export const getAppInfo = async (): Promise<any> => {
  return executeExtendScript('SahAI.getAppInfo()');
};

// Get document information
export const getDocumentInfo = async (): Promise<any> => {
  return executeExtendScript('SahAI.getDocumentInfo()');
};

// Execute code in host application
export const executeCode = async (code: string, language: string): Promise<any> => {
  return executeExtendScript(`SahAI.executeCode(${JSON.stringify(code)}, ${JSON.stringify(language)})`);
};

// Show alert in host application
export const showAlert = async (message: string, title?: string): Promise<any> => {
  return executeExtendScript(`SahAI.showAlert(${JSON.stringify(message)}, ${JSON.stringify(title || 'SahAI')})`);
};

// Log message to ExtendScript console
export const logToExtendScript = async (message: string, level?: string): Promise<any> => {
  return executeExtendScript(`SahAI.log(${JSON.stringify(message)}, ${JSON.stringify(level || 'info')})`);
};

// Get system information
export const getSystemInfo = async (): Promise<any> => {
  return executeExtendScript('SahAI.getSystemInfo()');
};

// Close the extension panel
export const closeExtension = (): void => {
  const cs = getCSInterface();
  if (cs) {
    cs.closeExtension();
  }
};

// Get extension ID
export const getExtensionId = (): string | null => {
  const cs = getCSInterface();
  return cs ? cs.getExtensionID() : null;
};

// Get system path
export const getSystemPath = (pathType: string): string | null => {
  const cs = getCSInterface();
  return cs ? cs.getSystemPath(pathType) : null;
};

// Open URL in default browser
export const openURLInBrowser = (url: string): void => {
  const cs = getCSInterface();
  if (cs) {
    cs.openURLInDefaultBrowser(url);
  }
};

interface CEPSettingsData {
  activeProviderId?: string;
  providers?: Array<{
    id: string;
    isConfigured: boolean;
    apiKey?: string;
    baseURL?: string;
    selectedModelId?: string;
    settings?: Record<string, unknown>;
  }>;
}

export class CEPSettings {
  private static readonly SETTINGS_KEY = 'sahAI_settings';

  static async save(data: CEPSettingsData): Promise<void> {
    const settingsString = JSON.stringify(data);

    try {
      // Try to save to CEP persistent storage first
      if (isCEPEnvironment()) {
        try {
          const result = await executeExtendScript(`saveSettings(${JSON.stringify(data)})`, 10000);
          if (result.success) {
            console.log('Settings saved to CEP storage successfully');
          } else {
            throw new Error(result.message || 'CEP save failed');
          }
        } catch (cepError) {
          console.warn('CEP storage save failed, falling back to localStorage:', cepError);
          // Continue to localStorage fallback
        }
      }

      // Always save to localStorage as backup
      localStorage.setItem(this.SETTINGS_KEY, settingsString);
      console.log('Settings saved to localStorage successfully');

    } catch (error) {
      console.error('All settings save methods failed:', error);
      // Last resort: try localStorage one more time
      try {
        localStorage.setItem(this.SETTINGS_KEY, settingsString);
      } catch (localError) {
        throw new Error(`Failed to save settings: ${error}. LocalStorage also failed: ${localError}`);
      }
    }
  }

  static async load(): Promise<CEPSettingsData> {
    try {
      // Try to load from CEP first
      if (isCEPEnvironment()) {
        try {
          const result = await executeExtendScript('loadSettings()', 10000);
          if (result.success && result.data) {
            console.log('Settings loaded from CEP storage successfully');
            return result.data;
          }
        } catch (cepError) {
          console.warn('CEP storage load failed, falling back to localStorage:', cepError);
        }
      }

      // Fallback to localStorage
      const localData = localStorage.getItem(this.SETTINGS_KEY);
      if (localData) {
        const parsed = JSON.parse(localData);
        console.log('Settings loaded from localStorage successfully');
        return parsed;
      }

      console.log('No existing settings found, returning defaults');
      return { providers: [] };

    } catch (error) {
      console.error('All settings load methods failed:', error);
      // Return empty settings as last resort
      return { providers: [] };
    }
  }

  static async exportSettings(): Promise<string> {
    const settings = await this.load();
    return JSON.stringify(settings, null, 2);
  }

  static async importSettings(settingsJson: string): Promise<void> {
    try {
      const settings = JSON.parse(settingsJson);
      await this.save(settings);
    } catch (error) {
      throw new Error('Invalid settings format');
    }
  }

  static async clearSettings(): Promise<void> {
    try {
      // Clear CEP storage
      if (isCEPEnvironment()) {
        try {
          await executeExtendScript('saveSettings({})', 10000);
        } catch (cepError) {
          console.warn('Failed to clear CEP storage:', cepError);
        }
      }

      // Clear localStorage
      localStorage.removeItem(this.SETTINGS_KEY);
      console.log('Settings cleared successfully');

    } catch (error) {
      throw new Error(`Failed to clear settings: ${error}`);
    }
  }
}

// Provider status checker
export class ProviderStatusChecker {
  static async checkProviderStatus(providerId: string, config: { apiKey?: string; baseURL?: string }): Promise<{
    isOnline: boolean;
    latency?: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    try {
      // Use CEP bridge to check status
      const { ProviderBridge } = await import('./cepIntegration');
      const models = await ProviderBridge.listModels(
        providerId,
        config.baseURL,
        config.apiKey
      );
      
      return {
        isOnline: (models as any[]).length > 0,
        latency: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        isOnline: false,
        error: (error as Error).message || String(error),
        latency: Date.now() - startTime
      };
    }
  }
}

// Provider Bridge for model listing
export const ProviderBridge = {
  async listModels(providerId: string, baseUrl?: string, apiKey?: string): Promise<any[]> {
    try {
      // Use the enhanced executeExtendScript function
      const script = `listModels('${providerId}', '${baseUrl || ''}', '${apiKey || ''}')`;
      const result = await executeExtendScript(script, 15000); // 15 second timeout for model fetching

      if (result && typeof result === 'object') {
        // Handle direct result from executeExtendScript
        if (result.success && result.data) {
          const parsed = typeof result.data === 'string' ? JSON.parse(result.data) : result.data;
          return parsed.ok ? parsed.models : this.getFallbackModels(providerId);
        }
        // Handle result that's already parsed
        if (result.ok) {
          return result.models || [];
        }
      }

      // Fallback to local models if API fails
      console.warn(`Failed to fetch models for ${providerId}, using fallback`);
      return this.getFallbackModels(providerId);

    } catch (error) {
      console.error(`Error fetching models for ${providerId}:`, error);
      // Return fallback models on error
      return this.getFallbackModels(providerId);
    }
  },

  getFallbackModels(providerId: string) {
    // Fallback model lists for development/testing
    const fallbackModels: Record<string, any[]> = {
      openai: [
        { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable OpenAI model', contextLength: 128000, isRecommended: true },
        { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster, more affordable', contextLength: 128000 },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Legacy model', contextLength: 16384 }
      ],
      anthropic: [
        { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Anthropic\'s most capable model', contextLength: 200000, isRecommended: true },
        { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', description: 'Fast and efficient', contextLength: 200000 },
        { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Powerful reasoning', contextLength: 200000 }
      ],
      gemini: [
        { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Google\'s most capable model', contextLength: 2000000, isRecommended: true },
        { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast and efficient', contextLength: 1000000 }
      ],
      groq: [
        { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B', description: 'Fast inference', contextLength: 131072 },
        { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B', description: 'Balanced performance', contextLength: 131072, isRecommended: true },
        { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', description: 'Large context', contextLength: 32768 }
      ],
      deepseek: [
        { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose', contextLength: 128000, isRecommended: true },
        { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Code-focused', contextLength: 128000 }
      ],
      mistral: [
        { id: 'mistral-large-latest', name: 'Mistral Large', description: 'Most capable', contextLength: 128000, isRecommended: true },
        { id: 'mistral-medium-latest', name: 'Mistral Medium', description: 'Balanced', contextLength: 32000 },
        { id: 'mistral-small-latest', name: 'Mistral Small', description: 'Fast and efficient', contextLength: 32000 }
      ],
      ollama: [
        { id: 'llama2', name: 'Llama 2', description: 'Open source LLM', contextLength: 4096 },
        { id: 'mistral', name: 'Mistral', description: 'Efficient transformer', contextLength: 8192 },
        { id: 'codellama', name: 'Code Llama', description: 'Code-focused', contextLength: 16384 }
      ],
      lmstudio: [
        { id: 'local-model', name: 'Local Model', description: 'Your local model', contextLength: 4096 }
      ]
    };
    
    return fallbackModels[providerId] || [];
  }
};
