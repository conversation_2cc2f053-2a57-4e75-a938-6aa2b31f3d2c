# **SahAI V2: Masterplan for CEP Extension**

## **1\. Overview**

This document presents a unified and comprehensive masterplan to resolve all outstanding issues in the SahAI V2 CEP extension. This plan provides a single, actionable roadmap. It addresses critical bugs in provider and model selection, completes the user interface by implementing missing modals and actions, corrects visual discrepancies with provider logos, and fortifies the underlying communication bridge between the React UI and Adobe's ExtendScript environment. The solutions are designed to align with the project's architectural goals, drawing inspiration from Cline and Lobe Icons, while ensuring a stable and seamless user experience within the Adobe Creative Cloud suite.

## **2\. Core Functionality: Provider & Model Selection**

This is the most critical issue, causing a disconnect between user selections in the modal and the application's global state reflected in the UI.

### **2.1. Problem: State Desynchronization and Unreliable Model Loading**

* **Root Cause 1: Incomplete State Updates:** When a user saves a provider configuration in the ProviderModal, the action updates the API key and model ID but crucially fails to update the activeProviderId in the settingsStore. The TopBar component, which relies on this ID to display the current provider, is never notified of the change.  
* **Root Cause 2: Unreliable Model Fetching:** The logic for fetching models is scattered. It's triggered from within the ProviderModal component via a useEffect hook that is improperly dependent on the API key. This approach mixes UI and data-fetching logic, lacks robust error handling, and doesn't efficiently cache results.  
* **Root Cause 3: Incomplete Backend Logic:** The listModels function in host/ae-integration.jsx is incomplete and may not contain the necessary logic to fetch models for all 15 supported providers (e.g., OpenAI, Anthropic, Gemini, Groq).

### **2.2. Action Plan: Centralize State Management and Refine Data Fetching**

#### **Step 1: Implement Atomic State Updates in settingsStore**

Centralize the entire provider selection logic into a single, atomic action within the state store to ensure all necessary state variables are updated in one transaction.

* **File:** client/src/components/stores/settingsStore.ts  
* **Action:** Create a new saveProviderSelection action.

// Add to the SettingsState interface  
saveProviderSelection: (providerId: string, config: Partial\<Provider\>) \=\> void;

// Implement the action within the create() block  
saveProviderSelection: (providerId, config) \=\> {  
  set(state \=\> ({  
    activeProviderId: providerId, // The missing piece  
    providers: state.providers.map(p \=\>  
      p.id \=== providerId ? { ...p, ...config, isConfigured: \!\!(config.apiKey || p.baseURL) } : p  
    )  
  }));  
  get().persistSettings(); // Persist all changes at once  
},

#### **Step 2: Refactor ProviderModal.tsx to Use the New Action**

Update the modal to be a "dumb" component that calls the new centralized action.

* **File:** client/src/components/Modals/ProviderModal.tsx  
* **Action:** Replace the logic in handleSave with a single call to saveProviderSelection.

// Import the new action from the store  
const { saveProviderSelection } \= useSettingsStore();

const handleSave \= () \=\> {  
  if (selectedProvider && selectedModel) {  
    saveProviderSelection(selectedProvider, {  
      apiKey: apiKey,  
      selectedModelId: selectedModel  
    });  
    closeModal();  
  }  
};

#### **Step 3: Centralize and Trigger Model Loading from the Store**

Move the responsibility of fetching models from the component to the state store.

* **File:** client/src/components/Modals/ProviderModal.tsx  
* **Action:** Remove the useEffect that calls ProviderBridge.listModels. Instead, trigger the loading process from the store when a provider is selected in the UI.

// In ProviderModal.tsx  
const { providers, loadModelsForProvider } \= useSettingsStore();

const handleProviderSelect \= (providerId: string) \=\> {  
  setSelectedProvider(providerId);  
  setSelectedModel('');  
  // ... other UI state updates

  // Trigger the model loading action from the store  
  loadModelsForProvider(providerId);  
};

// In the JSX, read loading/error/model state from the global store  
const providerState \= providers.find(p \=\> p.id \=== selectedProvider);

{providerState?.isLoading && \<Loader /\>}  
{providerState?.error && \<div className="text-adobe-error"\>{providerState.error}\</div\>}  
{providerState?.models.map(model \=\> (  
  // ... render model  
))}

#### **Step 4: Complete the listModels ExtendScript Function**

Ensure the backend can handle requests for all providers.

* **File:** host/ae-integration.jsx  
* **Action:** Add case blocks for all 15 providers within the listModels function. Each case must implement the logic to call the provider's API endpoint, parse the response, and format it into the required Model interface: { id: string, name: string, ... }.

// Example for OpenAI in host/ae-integration.jsx  
case 'openai':  
    var openaiUrl \= baseURL || "https://api.openai.com/v1/models";  
    var headers \= { "Authorization": "Bearer " \+ apiKey };  
    var openaiRes \= getURL(openaiUrl, headers);  
    if (openaiRes.success) {  
        var data \= JSON.parse(openaiRes.data);  
        result \= { ok: true, models: data.data.map(function(m) {  
            return { id: m.id, name: m.id, description: m.description };  
        })};  
    } else {  
        result \= { ok: false, models: \[\], error: "Failed to fetch from OpenAI" };  
    }  
    break;  
// ... add cases for Anthropic, Gemini, Groq, Ollama, etc.

#### **Step 5: Update TopBar.tsx to Display the Active Model**

Ensure the top bar correctly reflects the globally selected provider and model.

* **File:** client/src/components/TopBar/TopBar.tsx  
* **Action:** Read the active provider and selected model directly from the settingsStore.

import { useSettingsStore } from '../stores/settingsStore';

export const TopBar: React.FC \= () \=\> {  
  const { getActiveProvider } \= useSettingsStore();  
  const activeProvider \= getActiveProvider();  
  const activeModel \= activeProvider?.models.find(m \=\> m.id \=== activeProvider.selectedModelId);

  return (  
    \<div\>  
      \<span\>{activeProvider?.name || 'No Provider'}\</span\>  
      \<span\>{activeModel?.name || 'No Model'}\</span\>  
      {/\* ... other buttons \*/}  
    \</div\>  
  );  
};

## **3\. UI/UX: Completing Modals and Correcting Logos**

The UI is incomplete, with several modals and actions being unimplemented placeholders, and provider logos are not displayed correctly.

### **3.1. Problem: Missing Modals and Mismatched Logos**

* **Root Cause 1: Incomplete Modal System:** The infrastructure in ModalRoot.tsx exists, but the components for **Settings**, **Chat History**, and **Status Indicator** have not been created or are empty placeholders. Actions like "New Chat" are not wired up.  
* **Root Cause 2: Incorrect Logo Implementation:** The provider logos are either using generic placeholders or are incorrectly mapped. The project intends to use the specific, branded SVGs from the lobe-icons library.

### **3.2. Action Plan: Build Out UI Components and Implement Correct Logo Mapping**

#### **Step 1: Create and Integrate Missing Modals**

* **Action:** Create the following new files with their respective UI and logic:  
  * client/src/components/Modals/SettingsModal.tsx: For global settings like theme.  
  * client/src/components/Modals/ChatHistoryModal.tsx: To list past chat sessions, allowing users to view, select, or delete them. This will require a new historyStore.ts to manage state and corresponding loadHistory/saveHistory functions in ExtendScript.  
  * client/src/components/ui/ProviderStatusIndicator.tsx: To display the connection status (e.g., Connected, Loading, Error) based on the provider's state in settingsStore.  
* **File:** client/src/components/ModalRoot.tsx  
* **Action:** Import and render the new modals based on the active modal type.

// In ModalRoot.tsx  
import { SettingsModal } from './SettingsModal';  
import { ChatHistoryModal } from './ChatHistoryModal';

// ...  
{modal.type \=== 'settings' && \<SettingsModal /\>}  
{modal.type \=== 'chat-history' && \<ChatHistoryModal /\>}

* **File:** client/src/components/stores/modalStore.ts  
* **Action:** Update the ModalType to include the new modals.

export type ModalType \= 'provider' | 'settings' | 'chat-history' | 'status';

#### **Step 2: Wire Up UI Actions**

* **File:** client/src/components/TopBar/TopBar.tsx  
* **Action:** Connect the "New Chat" button to the appropriate action in chatStore.ts.

// In TopBar.tsx  
import { useChatStore } from '../stores/chatStore';

const { createNewSession } \= useChatStore();  
// ...  
\<button onClick={createNewSession} title="New Chat"\>  
  \<Plus size={16} /\>  
\</button\>

#### **Step 3: Implement True-to-Brand Provider Logos**

Create a dedicated component that renders the correct SVG based on the provider ID.

* **Action:** Create a new file: client/src/components/ui/ProviderLogo.tsx.  
* **Details:** This component will contain a switch statement that returns the correct SVG path data for each provider, copied from the Lobe Icons library.

// In client/src/components/ui/ProviderLogo.tsx  
import React from 'react';

interface ProviderLogoProps extends React.SVGProps\<SVGSVGElement\> {  
  provider: string;  
}

export const ProviderLogo: React.FC\<ProviderLogoProps\> \= ({ provider, ...props }) \=\> {  
  switch (provider) {  
    case 'openai':  
      return (  
        \<svg viewBox="0 0 1024 1024" {...props}\>  
          {/\* Paste the \<path\> data for the OpenAI logo here \*/}  
        \</svg\>  
      );  
    case 'anthropic':  
      return (  
        \<svg viewBox="0 0 24 24" {...props}\>  
           {/\* Paste the \<path\> data for the Anthropic logo here \*/}  
        \</svg\>  
      );  
    // ... add cases for all other providers  
    default:  
      return ( /\* A generic fallback icon \*/ );  
  }  
};

* **Action:** Use this new ProviderLogo component in ProviderModal.tsx and TopBar.tsx to display logos dynamically.

// In ProviderModal.tsx  
import { ProviderLogo } from '../ui/ProviderLogo';

// Inside the provider list mapping:  
\<ProviderLogo provider={provider.value} className="w-5 h-5" /\>

## **4\. CEP Bridge: Fortifying the Communication Layer**

The communication between the React front-end and the ExtendScript back-end is brittle, using synchronous calls that can block the UI and lack proper error handling.

### **4.1. Problem: A Brittle and Dispersed Bridge**

* **Root Cause:** evalScript calls are made synchronously from various places in the codebase. There is no centralized, asynchronous, and error-handled communication layer, making the extension prone to freezing and difficult to debug.

### **4.2. Action Plan: Create a Robust, Centralized CEP Integration Utility**

Refactor cepIntegration.ts to be the definitive, asynchronous communication layer.

* **File:** client/src/utils/cepIntegration.ts  
* **Action:** Create a robust, asynchronous wrapper for evalScript and centralize all bridge calls.

// In client/src/utils/cepIntegration.ts  
import { CSInterface } from './cs-interface'; // Assuming type definitions exist

const cs \= new CSInterface();

/\*\*  
 \* A robust, asynchronous wrapper for evalScript.  
 \* @param script The ExtendScript code to execute.  
 \* @returns A promise that resolves with the script's result.  
 \*/  
function evalScriptPromise\<T\>(script: string): Promise\<T\> {  
  return new Promise((resolve, reject) \=\> {  
    cs.evalScript(script, (result: any) \=\> {  
      if (typeof result \=== 'string' && result.startsWith('EvalScript error')) {  
        reject(new Error(result));  
        return;  
      }  
      try {  
        // Attempt to parse JSON results automatically  
        resolve(JSON.parse(result));  
      } catch (e) {  
        // Return as-is if not JSON  
        resolve(result as T);  
      }  
    });  
  });  
}

// \--- Centralized Provider Bridge \---  
export const ProviderBridge \= {  
  listModels: async (providerId: string, baseURL?: string, apiKey?: string): Promise\<{ id: string; name: string }\[\]\> \=\> {  
    const script \= \`listModels("${providerId}", "${baseURL || ''}", "${apiKey || ''}")\`;  
    const result \= await evalScriptPromise\<{ ok: boolean; models: any\[\]; error?: string }\>(script);  
    if (result && result.ok) {  
      return result.models;  
    }  
    // Handle error or return fallback models  
    console.error(result.error || 'Failed to list models.');  
    return \[\]; // Or return fallback models  
  },  
};

// \--- Centralized Settings Bridge \---  
export const CEPSettings \= {  
  save: (settings: object): void \=\> {  
    const settingsString \= JSON.stringify(settings).replace(/"/g, '\\\\\\\\"');  
    cs.evalScript(\`saveSettings("${settingsString}")\`);  
  },  
  load: (): Promise\<any\> \=\> {  
    return evalScriptPromise('loadSettings()');  
  }  
};

## **5\. Summary of Files to Modify or Create**

| File | Path | Purpose |
| :---- | :---- | :---- |
| settingsStore.ts | client/src/components/stores/settingsStore.ts | Implement atomic state updates for provider selection. |
| ProviderModal.tsx | client/src/components/Modals/ProviderModal.tsx | Simplify to use centralized state actions. |
| TopBar.tsx | client/src/components/TopBar/TopBar.tsx | Display the globally selected provider and model. |
| ae-integration.jsx | host/ae-integration.jsx | Complete listModels logic for all 15 providers. |
| **SettingsModal.tsx** | client/src/components/Modals/SettingsModal.tsx | **New File:** Create settings configuration modal. |
| **ChatHistoryModal.tsx** | client/src/components/Modals/ChatHistoryModal.tsx | **New File:** Create chat history display modal. |
| **historyStore.ts** | client/src/components/stores/historyStore.ts | **New File:** Manage state for chat history. |
| ProviderStatusIndicator.tsx | client/src/components/ui/ProviderStatusIndicator.tsx | Verify and implement provider status display. |
| **ProviderLogo.tsx** | client/src/components/ui/ProviderLogo.tsx | **New File:** Create component for dynamic, branded logos. |
| cepIntegration.ts | client/src/utils/cepIntegration.ts | Refactor into a robust, asynchronous communication bridge. |
| ModalRoot.tsx | client/src/components/ModalRoot.tsx | Integrate the new modals. |

## **6\. Validation Steps**

1. Run npm run dev and launch the extension in a host Adobe application (e.g., Photoshop, After Effects).  
2. **Provider Selection:** Open the Provider Modal, select a provider (e.g., OpenAI), enter credentials, and select a model. On saving, verify the TopBar updates immediately to reflect the selection.  
3. **Modal Functionality:** Open the Settings, History, and Status modals to ensure they render correctly and are functional.  
4. **Logo Verification:** Confirm that the correct, high-fidelity Lobe Icons appear in the ProviderModal and TopBar.  
5. **New Chat:** Test the "New Chat" button to ensure it clears the current session and starts a new one.  
6. **Persistence:** Close and reopen the extension to confirm that the selected provider, model, and other settings have been persisted correctly.