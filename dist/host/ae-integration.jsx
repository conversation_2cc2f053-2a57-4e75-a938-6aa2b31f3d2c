/**
 * SahAI CEP Extension V2 - ExtendScript Main File
 * This file handles communication between the CEP panel and Adobe applications
 */

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging
        
        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");
        
        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        return {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        return {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString(),
            language: language
        };
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            return {
                success: false,
                message: "No active document"
            };
        }
        
        var doc = app.activeDocument;
        return {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        return {
            success: true,
            message: "Alert displayed"
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;
        
        $.writeln(logMessage);
        
        return {
            success: true,
            message: "Logged: " + message
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        return {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
    } catch (error) {
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Load settings from file
 */
function loadSettings() {
    try {
        var settingsFile = new File("~/Adobe/CEP/extensions/SahAI/settings.json");
        if (settingsFile.exists) {
            settingsFile.open("r");
            var content = settingsFile.read();
            settingsFile.close();
            return content;
        }
        return "{}";
    } catch (error) {
        $.writeln("Error loading settings: " + error.toString());
        return "{}";
    }
}

/**
 * Save settings to file
 * @param {Object} settings - Settings object to save
 */
function saveSettings(settings) {
    try {
        var settingsDir = new Folder("~/Adobe/CEP/extensions/SahAI");
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var settingsFile = new File("~/Adobe/CEP/extensions/SahAI/settings.json");
        settingsFile.open("w");
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Load chat history from file
 */
function loadHistory() {
    try {
        var historyFile = new File("~/Adobe/CEP/extensions/SahAI/history.json");
        if (historyFile.exists) {
            historyFile.open("r");
            var content = historyFile.read();
            historyFile.close();
            return JSON.stringify({ success: true, data: JSON.parse(content) });
        }
        return JSON.stringify({ success: true, data: [] });
    } catch (error) {
        $.writeln("Error loading history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: [] });
    }
}

/**
 * Save chat history to file
 * @param {Array} history - Array of chat sessions to save
 */
function saveHistory(history) {
    try {
        var settingsDir = new Folder("~/Adobe/CEP/extensions/SahAI");
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var historyFile = new File("~/Adobe/CEP/extensions/SahAI/history.json");
        historyFile.open("w");
        historyFile.write(JSON.stringify(history));
        historyFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * List models for different providers
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function listModels(providerId, baseURL, apiKey) {
    var result = { ok: false, models: [] };
    
    try {
        switch (providerId) {
            case 'ollama':
                var ollamaUrl = baseURL || "http://localhost:11434";
                var ollamaRes = getURL(ollamaUrl + "/api/tags");
                if (ollamaRes.success) {
                    var data = JSON.parse(ollamaRes.data);
                    result = { ok: true, models: data.models.map(function(m) {
                        return { id: m.name, name: m.name };
                    })};
                }
                break;

            case 'lmstudio':
                var lmstudioUrl = baseURL || "http://localhost:1234";
                var lmstudioRes = getURL(lmstudioUrl + "/v1/models");
                if (lmstudioRes.success) {
                    var data = JSON.parse(lmstudioRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'anthropic':
                var anthropicRes = getURL("https://api.anthropic.com/v1/models", {
                    "x-api-key": apiKey || ""
                });
                if (anthropicRes.success) {
                    var data = JSON.parse(anthropicRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.display_name || m.id };
                    })};
                }
                break;

            case 'openai':
                var headers = {};
                if (apiKey && apiKey.trim() !== '') {
                    headers["Authorization"] = "Bearer " + apiKey;
                }
                var openaiRes = getURL("https://api.openai.com/v1/models", headers);
                if (openaiRes.success) {
                    var data = JSON.parse(openaiRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'gemini':
                result = { ok: true, models: [
                    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
                    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' }
                ]};
                break;

            case 'groq':
                var groqHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    groqHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var groqRes = getURL("https://api.groq.com/openai/v1/models", groqHeaders);
                if (groqRes.success) {
                    var data = JSON.parse(groqRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'deepseek':
                var deepseekHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    deepseekHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var deepseekRes = getURL("https://api.deepseek.com/v1/models", deepseekHeaders);
                if (deepseekRes.success) {
                    var data = JSON.parse(deepseekRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'mistral':
                var mistralHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    mistralHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var mistralRes = getURL("https://api.mistral.ai/v1/models", mistralHeaders);
                if (mistralRes.success) {
                    var data = JSON.parse(mistralRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'moonshot':
                var moonshotHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    moonshotHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var moonshotRes = getURL("https://api.moonshot.cn/v1/models", moonshotHeaders);
                if (moonshotRes.success) {
                    var data = JSON.parse(moonshotRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                }
                break;

            case 'openrouter':
                var openrouterHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    openrouterHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var openrouterRes = getURL("https://openrouter.ai/api/v1/models", openrouterHeaders);
                if (openrouterRes.success) {
                    var data = JSON.parse(openrouterRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.name || m.id };
                    })};
                }
                break;

            case 'perplexity':
                var perplexityHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    perplexityHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var perplexityRes = getURL("https://api.perplexity.ai/models", perplexityHeaders);
                if (perplexityRes.success) {
                    var data = JSON.parse(perplexityRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                } else {
                    // Fallback to known Perplexity models
                    result = { ok: true, models: [
                        { id: 'llama-3.1-sonar-small-128k-online', name: 'Llama 3.1 Sonar Small 128K Online' },
                        { id: 'llama-3.1-sonar-large-128k-online', name: 'Llama 3.1 Sonar Large 128K Online' },
                        { id: 'llama-3.1-sonar-huge-128k-online', name: 'Llama 3.1 Sonar Huge 128K Online' }
                    ]};
                }
                break;

            case 'qwen':
                var qwenHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    qwenHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var qwenRes = getURL("https://dashscope.aliyuncs.com/api/v1/models", qwenHeaders);
                if (qwenRes.success) {
                    var data = JSON.parse(qwenRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                } else {
                    // Fallback to known Qwen models
                    result = { ok: true, models: [
                        { id: 'qwen-turbo', name: 'Qwen Turbo' },
                        { id: 'qwen-plus', name: 'Qwen Plus' },
                        { id: 'qwen-max', name: 'Qwen Max' }
                    ]};
                }
                break;

            case 'together':
                var togetherHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    togetherHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var togetherRes = getURL("https://api.together.xyz/v1/models", togetherHeaders);
                if (togetherRes.success) {
                    var data = JSON.parse(togetherRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.display_name || m.id };
                    })};
                }
                break;

            case 'vertex':
                // Vertex AI requires more complex authentication, fallback to known models
                result = { ok: true, models: [
                    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
                    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
                    { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }
                ]};
                break;

            case 'xai':
                var xaiHeaders = {};
                if (apiKey && apiKey.trim() !== '') {
                    xaiHeaders["Authorization"] = "Bearer " + apiKey;
                }
                var xaiRes = getURL("https://api.x.ai/v1/models", xaiHeaders);
                if (xaiRes.success) {
                    var data = JSON.parse(xaiRes.data);
                    result = { ok: true, models: data.data.map(function(m) {
                        return { id: m.id, name: m.id };
                    })};
                } else {
                    // Fallback to known xAI models
                    result = { ok: true, models: [
                        { id: 'grok-beta', name: 'Grok Beta' },
                        { id: 'grok-vision-beta', name: 'Grok Vision Beta' }
                    ]};
                }
                break;

            default:
                result = { ok: true, models: [] };
        }
    } catch (e) {
        $.writeln("Error listing models for " + providerId + ": " + e.toString());

        // Provide fallback models for each provider when API fails
        var fallbackModels = getFallbackModels(providerId);
        if (fallbackModels.length > 0) {
            result = { ok: true, models: fallbackModels };
            $.writeln("Using fallback models for " + providerId);
        } else {
            result = { ok: false, models: [], error: e.toString() };
        }
    }

    // Final safety check - ensure we always return valid models array
    if (!result.models || !Array.isArray(result.models)) {
        result.models = [];
    }

    return JSON.stringify(result);
}

/**
 * Get fallback models when API calls fail
 * @param {string} providerId - Provider identifier
 * @returns {Array} Array of fallback models
 */
function getFallbackModels(providerId) {
    var fallbackModels = {
        'openai': [
            { id: 'gpt-4o', name: 'GPT-4o' },
            { id: 'gpt-4o-mini', name: 'GPT-4o Mini' },
            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo' },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
        ],
        'anthropic': [
            { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet' },
            { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku' },
            { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' }
        ],
        'gemini': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }
        ],
        'groq': [
            { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B' },
            { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B' },
            { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B' }
        ],
        'deepseek': [
            { id: 'deepseek-chat', name: 'DeepSeek Chat' },
            { id: 'deepseek-coder', name: 'DeepSeek Coder' }
        ],
        'mistral': [
            { id: 'mistral-large-latest', name: 'Mistral Large' },
            { id: 'mistral-medium-latest', name: 'Mistral Medium' },
            { id: 'mistral-small-latest', name: 'Mistral Small' }
        ],
        'moonshot': [
            { id: 'moonshot-v1-8k', name: 'Moonshot v1 8K' },
            { id: 'moonshot-v1-32k', name: 'Moonshot v1 32K' },
            { id: 'moonshot-v1-128k', name: 'Moonshot v1 128K' }
        ],
        'openrouter': [
            { id: 'openai/gpt-4o', name: 'GPT-4o (OpenRouter)' },
            { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet (OpenRouter)' },
            { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B (OpenRouter)' }
        ],
        'perplexity': [
            { id: 'llama-3.1-sonar-small-128k-online', name: 'Llama 3.1 Sonar Small 128K Online' },
            { id: 'llama-3.1-sonar-large-128k-online', name: 'Llama 3.1 Sonar Large 128K Online' },
            { id: 'llama-3.1-sonar-huge-128k-online', name: 'Llama 3.1 Sonar Huge 128K Online' }
        ],
        'qwen': [
            { id: 'qwen-turbo', name: 'Qwen Turbo' },
            { id: 'qwen-plus', name: 'Qwen Plus' },
            { id: 'qwen-max', name: 'Qwen Max' }
        ],
        'together': [
            { id: 'meta-llama/Llama-3-70b-chat-hf', name: 'Llama 3 70B Chat' },
            { id: 'meta-llama/Llama-3-8b-chat-hf', name: 'Llama 3 8B Chat' },
            { id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', name: 'Mixtral 8x7B Instruct' }
        ],
        'vertex': [
            { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
            { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
            { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }
        ],
        'xai': [
            { id: 'grok-beta', name: 'Grok Beta' },
            { id: 'grok-vision-beta', name: 'Grok Vision Beta' }
        ],
        'ollama': [
            { id: 'llama3.1', name: 'Llama 3.1' },
            { id: 'mistral', name: 'Mistral' },
            { id: 'codellama', name: 'Code Llama' }
        ],
        'lmstudio': [
            { id: 'local-model', name: 'Local Model' }
        ]
    };

    return fallbackModels[providerId] || [];
}

/**
 * Helper function to make HTTP requests
 * @param {string} url - URL to fetch
 * @param {Object} headers - Optional headers
 */
function getURL(url, headers) {
    try {
        var http = new XMLHttpRequest();
        http.open("GET", url, false); // Synchronous request
        if (headers) {
            for (var key in headers) {
                http.setRequestHeader(key, headers[key]);
            }
        }
        http.send();
        
        if (http.status >= 200 && http.status < 300) {
            return { success: true, data: http.responseText };
        } else {
            return { success: false, message: "HTTP " + http.status };
        }
    } catch (error) {
        return { success: false, message: error.toString() };
    }
}

// Initialize SahAI when script loads
try {
    $.writeln("=== SahAI Extension Loading ===");
    SahAI.init();
    $.writeln("=== SahAI Extension Loaded Successfully ===");
} catch (error) {
    $.writeln("=== SahAI Extension Load Error: " + error.toString() + " ===");
}
