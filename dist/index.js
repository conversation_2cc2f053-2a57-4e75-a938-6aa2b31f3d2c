const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./assets/angular-html-LfdN0zeE.js","./assets/html-C2L_23MC.js","./assets/javascript-ySlJ1b_l.js","./assets/css-BPhBrDlE.js","./assets/angular-ts-CKsD7JZE.js","./assets/scss-C31hgJw-.js","./assets/apl-BBq3IX1j.js","./assets/xml-e3z08dGr.js","./assets/java-xI-RfyKK.js","./assets/json-BQoSv7ci.js","./assets/astro-CqkE3fuf.js","./assets/typescript-Dj6nwHGl.js","./assets/postcss-B3ZDOciz.js","./assets/blade-a8OxSdnT.js","./assets/sql-COK4E0Yg.js","./assets/bsl-Dgyn0ogV.js","./assets/sdbl-BLhTXw86.js","./assets/cairo--RitsXJZ.js","./assets/python-DhUJRlN_.js","./assets/cobol-PTqiYgYu.js","./assets/coffee-dyiR41kL.js","./assets/cpp-BksuvNSY.js","./assets/regexp-DWJ3fJO_.js","./assets/glsl-DBO2IWDn.js","./assets/c-C3t2pwGQ.js","./assets/crystal-DtDmRg-F.js","./assets/shellscript-atvbtKCR.js","./assets/edge-D5gP-w-T.js","./assets/html-derivative-CSfWNPLT.js","./assets/elixir-CLiX3zqd.js","./assets/elm-CmHSxxaM.js","./assets/erb-BYTLMnw6.js","./assets/ruby-DeZ3UC14.js","./assets/haml-B2EZWmdv.js","./assets/graphql-cDcHW_If.js","./assets/jsx-BAng5TT0.js","./assets/tsx-B6W0miNI.js","./assets/lua-CvWAzNxB.js","./assets/yaml-CVw76BM1.js","./assets/fortran-fixed-form-TqA4NnZg.js","./assets/fortran-free-form-DKXYxT9g.js","./assets/fsharp-XplgxFYe.js","./assets/markdown-UIAJJxZW.js","./assets/gdresource-BHYsBjWJ.js","./assets/gdshader-SKMF96pI.js","./assets/gdscript-DfxzS6Rs.js","./assets/git-commit-i4q6IMui.js","./assets/diff-BgYniUM_.js","./assets/git-rebase-B-v9cOL2.js","./assets/glimmer-js-D-cwc0-E.js","./assets/glimmer-ts-pgjy16dm.js","./assets/hack-D1yCygmZ.js","./assets/handlebars-BQGss363.js","./assets/http-FRrOvY1W.js","./assets/hxml-TIA70rKU.js","./assets/haxe-C5wWYbrZ.js","./assets/imba-bv_oIlVt.js","./assets/jinja-DGy0s7-h.js","./assets/jison-BqZprYcd.js","./assets/julia-BBuGR-5E.js","./assets/r-CwjWoCRV.js","./assets/latex-C-cWTeAZ.js","./assets/tex-rYs2v40G.js","./assets/liquid-D3W5UaiH.js","./assets/marko-z0MBrx5-.js","./assets/less-BfCpw3nA.js","./assets/mdc-DB_EDNY_.js","./assets/nginx-D_VnBJ67.js","./assets/nim-ZlGxZxc3.js","./assets/perl-CHQXSrWU.js","./assets/php-B5ebYQev.js","./assets/pug-CM9l7STV.js","./assets/qml-D8XfuvdV.js","./assets/razor-CNLDkMZG.js","./assets/csharp-D9R-vmeu.js","./assets/rst-4NLicBqY.js","./assets/cmake-DbXoA79R.js","./assets/sas-BmTFh92c.js","./assets/shaderlab-B7qAK45m.js","./assets/hlsl-ifBTmRxC.js","./assets/shellsession-C_rIy8kc.js","./assets/soy-C-lX7w71.js","./assets/sparql-bYkjHRlG.js","./assets/turtle-BMR_PYu6.js","./assets/stata-DorPZHa4.js","./assets/svelte-MSaWC3Je.js","./assets/templ-dwX3ZSMB.js","./assets/go-B1SYOhNW.js","./assets/ts-tags-CipyTH0X.js","./assets/twig-NC5TFiHP.js","./assets/vue-BuYVFjOK.js","./assets/vue-html-xdeiXROB.js","./assets/xsl-Dd0NUgwM.js"])))=>i.map(i=>d[i]);
var Cp=Object.defineProperty;var Lp=(e,t,n)=>t in e?Cp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var w=(e,t,n)=>Lp(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function gc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var _c={exports:{}},di={},yc={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mr=Symbol.for("react.element"),Pp=Symbol.for("react.portal"),Rp=Symbol.for("react.fragment"),Tp=Symbol.for("react.strict_mode"),Np=Symbol.for("react.profiler"),Ap=Symbol.for("react.provider"),Ip=Symbol.for("react.context"),Op=Symbol.for("react.forward_ref"),bp=Symbol.for("react.suspense"),Dp=Symbol.for("react.memo"),jp=Symbol.for("react.lazy"),Na=Symbol.iterator;function Mp(e){return e===null||typeof e!="object"?null:(e=Na&&e[Na]||e["@@iterator"],typeof e=="function"?e:null)}var vc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},xc=Object.assign,Sc={};function $n(e,t,n){this.props=e,this.context=t,this.refs=Sc,this.updater=n||vc}$n.prototype.isReactComponent={};$n.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};$n.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Ec(){}Ec.prototype=$n.prototype;function ks(e,t,n){this.props=e,this.context=t,this.refs=Sc,this.updater=n||vc}var Cs=ks.prototype=new Ec;Cs.constructor=ks;xc(Cs,$n.prototype);Cs.isPureReactComponent=!0;var Aa=Array.isArray,wc=Object.prototype.hasOwnProperty,Ls={current:null},kc={key:!0,ref:!0,__self:!0,__source:!0};function Cc(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)wc.call(t,r)&&!kc.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:Mr,type:e,key:i,ref:l,props:o,_owner:Ls.current}}function Vp(e,t){return{$$typeof:Mr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ps(e){return typeof e=="object"&&e!==null&&e.$$typeof===Mr}function zp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ia=/\/+/g;function ji(e,t){return typeof e=="object"&&e!==null&&e.key!=null?zp(""+e.key):t.toString(36)}function go(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Mr:case Pp:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+ji(l,0):r,Aa(o)?(n="",e!=null&&(n=e.replace(Ia,"$&/")+"/"),go(o,t,n,"",function(u){return u})):o!=null&&(Ps(o)&&(o=Vp(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Ia,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Aa(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+ji(i,s);l+=go(i,t,n,a,o)}else if(a=Mp(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+ji(i,s++),l+=go(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Kr(e,t,n){if(e==null)return e;var r=[],o=0;return go(e,r,"","",function(i){return t.call(n,i,o++)}),r}function Bp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee={current:null},_o={transition:null},$p={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:_o,ReactCurrentOwner:Ls};function Lc(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:Kr,forEach:function(e,t,n){Kr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Kr(e,function(){t++}),t},toArray:function(e){return Kr(e,function(t){return t})||[]},only:function(e){if(!Ps(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=$n;V.Fragment=Rp;V.Profiler=Np;V.PureComponent=ks;V.StrictMode=Tp;V.Suspense=bp;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=$p;V.act=Lc;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=xc({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Ls.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)wc.call(t,a)&&!kc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Mr,type:e.type,key:o,ref:i,props:r,_owner:l}};V.createContext=function(e){return e={$$typeof:Ip,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ap,_context:e},e.Consumer=e};V.createElement=Cc;V.createFactory=function(e){var t=Cc.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:Op,render:e}};V.isValidElement=Ps;V.lazy=function(e){return{$$typeof:jp,_payload:{_status:-1,_result:e},_init:Bp}};V.memo=function(e,t){return{$$typeof:Dp,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=_o.transition;_o.transition={};try{e()}finally{_o.transition=t}};V.unstable_act=Lc;V.useCallback=function(e,t){return Ee.current.useCallback(e,t)};V.useContext=function(e){return Ee.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return Ee.current.useDeferredValue(e)};V.useEffect=function(e,t){return Ee.current.useEffect(e,t)};V.useId=function(){return Ee.current.useId()};V.useImperativeHandle=function(e,t,n){return Ee.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Ee.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Ee.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Ee.current.useMemo(e,t)};V.useReducer=function(e,t,n){return Ee.current.useReducer(e,t,n)};V.useRef=function(e){return Ee.current.useRef(e)};V.useState=function(e){return Ee.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return Ee.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Ee.current.useTransition()};V.version="18.3.1";yc.exports=V;var O=yc.exports;const Rs=gc(O);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fp=O,Up=Symbol.for("react.element"),Gp=Symbol.for("react.fragment"),Hp=Object.prototype.hasOwnProperty,Wp=Fp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Qp={key:!0,ref:!0,__self:!0,__source:!0};function Pc(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Hp.call(t,r)&&!Qp.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Up,type:e,key:i,ref:l,props:o,_owner:Wp.current}}di.Fragment=Gp;di.jsx=Pc;di.jsxs=Pc;_c.exports=di;var m=_c.exports,vl={},Rc={exports:{}},De={},Tc={exports:{}},Nc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(A,D){var M=A.length;A.push(D);e:for(;0<M;){var J=M-1>>>1,se=A[J];if(0<o(se,D))A[J]=D,A[M]=se,M=J;else break e}}function n(A){return A.length===0?null:A[0]}function r(A){if(A.length===0)return null;var D=A[0],M=A.pop();if(M!==D){A[0]=M;e:for(var J=0,se=A.length,Wr=se>>>1;J<Wr;){var Ut=2*(J+1)-1,Di=A[Ut],Gt=Ut+1,Qr=A[Gt];if(0>o(Di,M))Gt<se&&0>o(Qr,Di)?(A[J]=Qr,A[Gt]=M,J=Gt):(A[J]=Di,A[Ut]=M,J=Ut);else if(Gt<se&&0>o(Qr,M))A[J]=Qr,A[Gt]=M,J=Gt;else break e}}return D}function o(A,D){var M=A.sortIndex-D.sortIndex;return M!==0?M:A.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,c=null,f=3,_=!1,v=!1,x=!1,k=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(A){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=A)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function E(A){if(x=!1,y(A),!v)if(n(a)!==null)v=!0,Ft(S);else{var D=n(u);D!==null&&bi(E,D.startTime-A)}}function S(A,D){v=!1,x&&(x=!1,g(T),T=-1),_=!0;var M=f;try{for(y(D),c=n(a);c!==null&&(!(c.expirationTime>D)||A&&!I());){var J=c.callback;if(typeof J=="function"){c.callback=null,f=c.priorityLevel;var se=J(c.expirationTime<=D);D=e.unstable_now(),typeof se=="function"?c.callback=se:c===n(a)&&r(a),y(D)}else r(a);c=n(a)}if(c!==null)var Wr=!0;else{var Ut=n(u);Ut!==null&&bi(E,Ut.startTime-D),Wr=!1}return Wr}finally{c=null,f=M,_=!1}}var C=!1,R=null,T=-1,$=5,b=-1;function I(){return!(e.unstable_now()-b<$)}function ee(){if(R!==null){var A=e.unstable_now();b=A;var D=!0;try{D=R(!0,A)}finally{D?Me():(C=!1,R=null)}}else C=!1}var Me;if(typeof h=="function")Me=function(){h(ee)};else if(typeof MessageChannel<"u"){var Ve=new MessageChannel,St=Ve.port2;Ve.port1.onmessage=ee,Me=function(){St.postMessage(null)}}else Me=function(){k(ee,0)};function Ft(A){R=A,C||(C=!0,Me())}function bi(A,D){T=k(function(){A(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){v||_||(v=!0,Ft(S))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(A){switch(f){case 1:case 2:case 3:var D=3;break;default:D=f}var M=f;f=D;try{return A()}finally{f=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,D){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var M=f;f=A;try{return D()}finally{f=M}},e.unstable_scheduleCallback=function(A,D,M){var J=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?J+M:J):M=J,A){case 1:var se=-1;break;case 2:se=250;break;case 5:se=**********;break;case 4:se=1e4;break;default:se=5e3}return se=M+se,A={id:d++,callback:D,priorityLevel:A,startTime:M,expirationTime:se,sortIndex:-1},M>J?(A.sortIndex=M,t(u,A),n(a)===null&&A===n(u)&&(x?(g(T),T=-1):x=!0,bi(E,M-J))):(A.sortIndex=se,t(a,A),v||_||(v=!0,Ft(S))),A},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(A){var D=f;return function(){var M=f;f=D;try{return A.apply(this,arguments)}finally{f=M}}}})(Nc);Tc.exports=Nc;var Kp=Tc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qp=O,be=Kp;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ac=new Set,gr={};function sn(e,t){In(e,t),In(e+"Capture",t)}function In(e,t){for(gr[e]=t,e=0;e<t.length;e++)Ac.add(t[e])}var gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xl=Object.prototype.hasOwnProperty,Yp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Oa={},ba={};function Xp(e){return xl.call(ba,e)?!0:xl.call(Oa,e)?!1:Yp.test(e)?ba[e]=!0:(Oa[e]=!0,!1)}function Jp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Zp(e,t,n,r){if(t===null||typeof t>"u"||Jp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){pe[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];pe[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){pe[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){pe[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){pe[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){pe[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){pe[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){pe[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){pe[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ts=/[\-:]([a-z])/g;function Ns(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){pe[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});pe.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){pe[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function As(e,t,n,r){var o=pe.hasOwnProperty(t)?pe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Zp(t,n,o,r)&&(n=null),r||o===null?Xp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=qp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,qr=Symbol.for("react.element"),dn=Symbol.for("react.portal"),fn=Symbol.for("react.fragment"),Is=Symbol.for("react.strict_mode"),Sl=Symbol.for("react.profiler"),Ic=Symbol.for("react.provider"),Oc=Symbol.for("react.context"),Os=Symbol.for("react.forward_ref"),El=Symbol.for("react.suspense"),wl=Symbol.for("react.suspense_list"),bs=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),bc=Symbol.for("react.offscreen"),Da=Symbol.iterator;function Wn(e){return e===null||typeof e!="object"?null:(e=Da&&e[Da]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,Mi;function tr(e){if(Mi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Mi=t&&t[1]||""}return`
`+Mi+e}var Vi=!1;function zi(e,t){if(!e||Vi)return"";Vi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Vi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?tr(e):""}function em(e){switch(e.tag){case 5:return tr(e.type);case 16:return tr("Lazy");case 13:return tr("Suspense");case 19:return tr("SuspenseList");case 0:case 2:case 15:return e=zi(e.type,!1),e;case 11:return e=zi(e.type.render,!1),e;case 1:return e=zi(e.type,!0),e;default:return""}}function kl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case fn:return"Fragment";case dn:return"Portal";case Sl:return"Profiler";case Is:return"StrictMode";case El:return"Suspense";case wl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Oc:return(e.displayName||"Context")+".Consumer";case Ic:return(e._context.displayName||"Context")+".Provider";case Os:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case bs:return t=e.displayName||null,t!==null?t:kl(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return kl(e(t))}catch{}}return null}function tm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return kl(t);case 8:return t===Is?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Mt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Dc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function nm(e){var t=Dc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Yr(e){e._valueTracker||(e._valueTracker=nm(e))}function jc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Dc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function No(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Cl(e,t){var n=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ja(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Mt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Mc(e,t){t=t.checked,t!=null&&As(e,"checked",t,!1)}function Ll(e,t){Mc(e,t);var n=Mt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Pl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Pl(e,t.type,Mt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ma(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Pl(e,t,n){(t!=="number"||No(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var nr=Array.isArray;function wn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Mt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Rl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Va(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(nr(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Mt(n)}}function Vc(e,t){var n=Mt(t.value),r=Mt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function za(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function zc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Tl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?zc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Xr,Bc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Xr=Xr||document.createElement("div"),Xr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Xr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var lr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},rm=["Webkit","ms","Moz","O"];Object.keys(lr).forEach(function(e){rm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),lr[t]=lr[e]})});function $c(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||lr.hasOwnProperty(e)&&lr[e]?(""+t).trim():t+"px"}function Fc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=$c(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var om=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Nl(e,t){if(t){if(om[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Al(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Il=null;function Ds(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ol=null,kn=null,Cn=null;function Ba(e){if(e=Br(e)){if(typeof Ol!="function")throw Error(L(280));var t=e.stateNode;t&&(t=gi(t),Ol(e.stateNode,e.type,t))}}function Uc(e){kn?Cn?Cn.push(e):Cn=[e]:kn=e}function Gc(){if(kn){var e=kn,t=Cn;if(Cn=kn=null,Ba(e),t)for(e=0;e<t.length;e++)Ba(t[e])}}function Hc(e,t){return e(t)}function Wc(){}var Bi=!1;function Qc(e,t,n){if(Bi)return e(t,n);Bi=!0;try{return Hc(e,t,n)}finally{Bi=!1,(kn!==null||Cn!==null)&&(Wc(),Gc())}}function yr(e,t){var n=e.stateNode;if(n===null)return null;var r=gi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var bl=!1;if(gt)try{var Qn={};Object.defineProperty(Qn,"passive",{get:function(){bl=!0}}),window.addEventListener("test",Qn,Qn),window.removeEventListener("test",Qn,Qn)}catch{bl=!1}function im(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var sr=!1,Ao=null,Io=!1,Dl=null,lm={onError:function(e){sr=!0,Ao=e}};function sm(e,t,n,r,o,i,l,s,a){sr=!1,Ao=null,im.apply(lm,arguments)}function am(e,t,n,r,o,i,l,s,a){if(sm.apply(this,arguments),sr){if(sr){var u=Ao;sr=!1,Ao=null}else throw Error(L(198));Io||(Io=!0,Dl=u)}}function an(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Kc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function $a(e){if(an(e)!==e)throw Error(L(188))}function um(e){var t=e.alternate;if(!t){if(t=an(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return $a(o),e;if(i===r)return $a(o),t;i=i.sibling}throw Error(L(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function qc(e){return e=um(e),e!==null?Yc(e):null}function Yc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Yc(e);if(t!==null)return t;e=e.sibling}return null}var Xc=be.unstable_scheduleCallback,Fa=be.unstable_cancelCallback,cm=be.unstable_shouldYield,dm=be.unstable_requestPaint,Z=be.unstable_now,fm=be.unstable_getCurrentPriorityLevel,js=be.unstable_ImmediatePriority,Jc=be.unstable_UserBlockingPriority,Oo=be.unstable_NormalPriority,pm=be.unstable_LowPriority,Zc=be.unstable_IdlePriority,fi=null,it=null;function mm(e){if(it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(fi,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:_m,hm=Math.log,gm=Math.LN2;function _m(e){return e>>>=0,e===0?32:31-(hm(e)/gm|0)|0}var Jr=64,Zr=4194304;function rr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function bo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=rr(s):(i&=l,i!==0&&(r=rr(i)))}else l=n&~o,l!==0?r=rr(l):i!==0&&(r=rr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),o=1<<n,r|=e[n],t&=~o;return r}function ym(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-Xe(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=ym(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function jl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ed(){var e=Jr;return Jr<<=1,!(Jr&4194240)&&(Jr=64),e}function $i(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Vr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function xm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Xe(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Ms(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var B=0;function td(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var nd,Vs,rd,od,id,Ml=!1,eo=[],Tt=null,Nt=null,At=null,vr=new Map,xr=new Map,Ct=[],Sm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ua(e,t){switch(e){case"focusin":case"focusout":Tt=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":xr.delete(t.pointerId)}}function Kn(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=Br(t),t!==null&&Vs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Em(e,t,n,r,o){switch(t){case"focusin":return Tt=Kn(Tt,e,t,n,r,o),!0;case"dragenter":return Nt=Kn(Nt,e,t,n,r,o),!0;case"mouseover":return At=Kn(At,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return vr.set(i,Kn(vr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,xr.set(i,Kn(xr.get(i)||null,e,t,n,r,o)),!0}return!1}function ld(e){var t=Qt(e.target);if(t!==null){var n=an(t);if(n!==null){if(t=n.tag,t===13){if(t=Kc(n),t!==null){e.blockedOn=t,id(e.priority,function(){rd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function yo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Vl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Il=r,n.target.dispatchEvent(r),Il=null}else return t=Br(n),t!==null&&Vs(t),e.blockedOn=n,!1;t.shift()}return!0}function Ga(e,t,n){yo(e)&&n.delete(t)}function wm(){Ml=!1,Tt!==null&&yo(Tt)&&(Tt=null),Nt!==null&&yo(Nt)&&(Nt=null),At!==null&&yo(At)&&(At=null),vr.forEach(Ga),xr.forEach(Ga)}function qn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ml||(Ml=!0,be.unstable_scheduleCallback(be.unstable_NormalPriority,wm)))}function Sr(e){function t(o){return qn(o,e)}if(0<eo.length){qn(eo[0],e);for(var n=1;n<eo.length;n++){var r=eo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Tt!==null&&qn(Tt,e),Nt!==null&&qn(Nt,e),At!==null&&qn(At,e),vr.forEach(t),xr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)ld(n),n.blockedOn===null&&Ct.shift()}var Ln=xt.ReactCurrentBatchConfig,Do=!0;function km(e,t,n,r){var o=B,i=Ln.transition;Ln.transition=null;try{B=1,zs(e,t,n,r)}finally{B=o,Ln.transition=i}}function Cm(e,t,n,r){var o=B,i=Ln.transition;Ln.transition=null;try{B=4,zs(e,t,n,r)}finally{B=o,Ln.transition=i}}function zs(e,t,n,r){if(Do){var o=Vl(e,t,n,r);if(o===null)Xi(e,t,r,jo,n),Ua(e,r);else if(Em(o,e,t,n,r))r.stopPropagation();else if(Ua(e,r),t&4&&-1<Sm.indexOf(e)){for(;o!==null;){var i=Br(o);if(i!==null&&nd(i),i=Vl(e,t,n,r),i===null&&Xi(e,t,r,jo,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Xi(e,t,r,null,n)}}var jo=null;function Vl(e,t,n,r){if(jo=null,e=Ds(r),e=Qt(e),e!==null)if(t=an(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Kc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return jo=e,null}function sd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(fm()){case js:return 1;case Jc:return 4;case Oo:case pm:return 16;case Zc:return 536870912;default:return 16}default:return 16}}var Pt=null,Bs=null,vo=null;function ad(){if(vo)return vo;var e,t=Bs,n=t.length,r,o="value"in Pt?Pt.value:Pt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return vo=o.slice(e,1<r?1-r:void 0)}function xo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function to(){return!0}function Ha(){return!1}function je(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?to:Ha,this.isPropagationStopped=Ha,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=to)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=to)},persist:function(){},isPersistent:to}),t}var Fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$s=je(Fn),zr=q({},Fn,{view:0,detail:0}),Lm=je(zr),Fi,Ui,Yn,pi=q({},zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yn&&(Yn&&e.type==="mousemove"?(Fi=e.screenX-Yn.screenX,Ui=e.screenY-Yn.screenY):Ui=Fi=0,Yn=e),Fi)},movementY:function(e){return"movementY"in e?e.movementY:Ui}}),Wa=je(pi),Pm=q({},pi,{dataTransfer:0}),Rm=je(Pm),Tm=q({},zr,{relatedTarget:0}),Gi=je(Tm),Nm=q({},Fn,{animationName:0,elapsedTime:0,pseudoElement:0}),Am=je(Nm),Im=q({},Fn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Om=je(Im),bm=q({},Fn,{data:0}),Qa=je(bm),Dm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Vm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Mm[e])?!!t[e]:!1}function Fs(){return Vm}var zm=q({},zr,{key:function(e){if(e.key){var t=Dm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=xo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fs,charCode:function(e){return e.type==="keypress"?xo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?xo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bm=je(zm),$m=q({},pi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ka=je($m),Fm=q({},zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fs}),Um=je(Fm),Gm=q({},Fn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Hm=je(Gm),Wm=q({},pi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Qm=je(Wm),Km=[9,13,27,32],Us=gt&&"CompositionEvent"in window,ar=null;gt&&"documentMode"in document&&(ar=document.documentMode);var qm=gt&&"TextEvent"in window&&!ar,ud=gt&&(!Us||ar&&8<ar&&11>=ar),qa=" ",Ya=!1;function cd(e,t){switch(e){case"keyup":return Km.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var pn=!1;function Ym(e,t){switch(e){case"compositionend":return dd(t);case"keypress":return t.which!==32?null:(Ya=!0,qa);case"textInput":return e=t.data,e===qa&&Ya?null:e;default:return null}}function Xm(e,t){if(pn)return e==="compositionend"||!Us&&cd(e,t)?(e=ad(),vo=Bs=Pt=null,pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ud&&t.locale!=="ko"?null:t.data;default:return null}}var Jm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Xa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Jm[e.type]:t==="textarea"}function fd(e,t,n,r){Uc(r),t=Mo(t,"onChange"),0<t.length&&(n=new $s("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ur=null,Er=null;function Zm(e){wd(e,0)}function mi(e){var t=gn(e);if(jc(t))return e}function eh(e,t){if(e==="change")return t}var pd=!1;if(gt){var Hi;if(gt){var Wi="oninput"in document;if(!Wi){var Ja=document.createElement("div");Ja.setAttribute("oninput","return;"),Wi=typeof Ja.oninput=="function"}Hi=Wi}else Hi=!1;pd=Hi&&(!document.documentMode||9<document.documentMode)}function Za(){ur&&(ur.detachEvent("onpropertychange",md),Er=ur=null)}function md(e){if(e.propertyName==="value"&&mi(Er)){var t=[];fd(t,Er,e,Ds(e)),Qc(Zm,t)}}function th(e,t,n){e==="focusin"?(Za(),ur=t,Er=n,ur.attachEvent("onpropertychange",md)):e==="focusout"&&Za()}function nh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return mi(Er)}function rh(e,t){if(e==="click")return mi(t)}function oh(e,t){if(e==="input"||e==="change")return mi(t)}function ih(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:ih;function wr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!xl.call(t,o)||!Ze(e[o],t[o]))return!1}return!0}function eu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function tu(e,t){var n=eu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=eu(n)}}function hd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?hd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function gd(){for(var e=window,t=No();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=No(e.document)}return t}function Gs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function lh(e){var t=gd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&hd(n.ownerDocument.documentElement,n)){if(r!==null&&Gs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=tu(n,i);var l=tu(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var sh=gt&&"documentMode"in document&&11>=document.documentMode,mn=null,zl=null,cr=null,Bl=!1;function nu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Bl||mn==null||mn!==No(r)||(r=mn,"selectionStart"in r&&Gs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),cr&&wr(cr,r)||(cr=r,r=Mo(zl,"onSelect"),0<r.length&&(t=new $s("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=mn)))}function no(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hn={animationend:no("Animation","AnimationEnd"),animationiteration:no("Animation","AnimationIteration"),animationstart:no("Animation","AnimationStart"),transitionend:no("Transition","TransitionEnd")},Qi={},_d={};gt&&(_d=document.createElement("div").style,"AnimationEvent"in window||(delete hn.animationend.animation,delete hn.animationiteration.animation,delete hn.animationstart.animation),"TransitionEvent"in window||delete hn.transitionend.transition);function hi(e){if(Qi[e])return Qi[e];if(!hn[e])return e;var t=hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in _d)return Qi[e]=t[n];return e}var yd=hi("animationend"),vd=hi("animationiteration"),xd=hi("animationstart"),Sd=hi("transitionend"),Ed=new Map,ru="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zt(e,t){Ed.set(e,t),sn(t,[e])}for(var Ki=0;Ki<ru.length;Ki++){var qi=ru[Ki],ah=qi.toLowerCase(),uh=qi[0].toUpperCase()+qi.slice(1);zt(ah,"on"+uh)}zt(yd,"onAnimationEnd");zt(vd,"onAnimationIteration");zt(xd,"onAnimationStart");zt("dblclick","onDoubleClick");zt("focusin","onFocus");zt("focusout","onBlur");zt(Sd,"onTransitionEnd");In("onMouseEnter",["mouseout","mouseover"]);In("onMouseLeave",["mouseout","mouseover"]);In("onPointerEnter",["pointerout","pointerover"]);In("onPointerLeave",["pointerout","pointerover"]);sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));sn("onBeforeInput",["compositionend","keypress","textInput","paste"]);sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ch=new Set("cancel close invalid load scroll toggle".split(" ").concat(or));function ou(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,am(r,t,void 0,e),e.currentTarget=null}function wd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;ou(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;ou(o,s,u),i=a}}}if(Io)throw e=Dl,Io=!1,Dl=null,e}function G(e,t){var n=t[Hl];n===void 0&&(n=t[Hl]=new Set);var r=e+"__bubble";n.has(r)||(kd(t,e,2,!1),n.add(r))}function Yi(e,t,n){var r=0;t&&(r|=4),kd(n,e,r,t)}var ro="_reactListening"+Math.random().toString(36).slice(2);function kr(e){if(!e[ro]){e[ro]=!0,Ac.forEach(function(n){n!=="selectionchange"&&(ch.has(n)||Yi(n,!1,e),Yi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ro]||(t[ro]=!0,Yi("selectionchange",!1,t))}}function kd(e,t,n,r){switch(sd(t)){case 1:var o=km;break;case 4:o=Cm;break;default:o=zs}n=o.bind(null,t,n,e),o=void 0,!bl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Xi(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=Qt(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}Qc(function(){var u=i,d=Ds(n),c=[];e:{var f=Ed.get(e);if(f!==void 0){var _=$s,v=e;switch(e){case"keypress":if(xo(n)===0)break e;case"keydown":case"keyup":_=Bm;break;case"focusin":v="focus",_=Gi;break;case"focusout":v="blur",_=Gi;break;case"beforeblur":case"afterblur":_=Gi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=Wa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=Rm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=Um;break;case yd:case vd:case xd:_=Am;break;case Sd:_=Hm;break;case"scroll":_=Lm;break;case"wheel":_=Qm;break;case"copy":case"cut":case"paste":_=Om;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=Ka}var x=(t&4)!==0,k=!x&&e==="scroll",g=x?f!==null?f+"Capture":null:f;x=[];for(var h=u,y;h!==null;){y=h;var E=y.stateNode;if(y.tag===5&&E!==null&&(y=E,g!==null&&(E=yr(h,g),E!=null&&x.push(Cr(h,E,y)))),k)break;h=h.return}0<x.length&&(f=new _(f,v,null,n,d),c.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",_=e==="mouseout"||e==="pointerout",f&&n!==Il&&(v=n.relatedTarget||n.fromElement)&&(Qt(v)||v[_t]))break e;if((_||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,_?(v=n.relatedTarget||n.toElement,_=u,v=v?Qt(v):null,v!==null&&(k=an(v),v!==k||v.tag!==5&&v.tag!==6)&&(v=null)):(_=null,v=u),_!==v)){if(x=Wa,E="onMouseLeave",g="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=Ka,E="onPointerLeave",g="onPointerEnter",h="pointer"),k=_==null?f:gn(_),y=v==null?f:gn(v),f=new x(E,h+"leave",_,n,d),f.target=k,f.relatedTarget=y,E=null,Qt(d)===u&&(x=new x(g,h+"enter",v,n,d),x.target=y,x.relatedTarget=k,E=x),k=E,_&&v)t:{for(x=_,g=v,h=0,y=x;y;y=cn(y))h++;for(y=0,E=g;E;E=cn(E))y++;for(;0<h-y;)x=cn(x),h--;for(;0<y-h;)g=cn(g),y--;for(;h--;){if(x===g||g!==null&&x===g.alternate)break t;x=cn(x),g=cn(g)}x=null}else x=null;_!==null&&iu(c,f,_,x,!1),v!==null&&k!==null&&iu(c,k,v,x,!0)}}e:{if(f=u?gn(u):window,_=f.nodeName&&f.nodeName.toLowerCase(),_==="select"||_==="input"&&f.type==="file")var S=eh;else if(Xa(f))if(pd)S=oh;else{S=nh;var C=th}else(_=f.nodeName)&&_.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(S=rh);if(S&&(S=S(e,u))){fd(c,S,n,d);break e}C&&C(e,f,u),e==="focusout"&&(C=f._wrapperState)&&C.controlled&&f.type==="number"&&Pl(f,"number",f.value)}switch(C=u?gn(u):window,e){case"focusin":(Xa(C)||C.contentEditable==="true")&&(mn=C,zl=u,cr=null);break;case"focusout":cr=zl=mn=null;break;case"mousedown":Bl=!0;break;case"contextmenu":case"mouseup":case"dragend":Bl=!1,nu(c,n,d);break;case"selectionchange":if(sh)break;case"keydown":case"keyup":nu(c,n,d)}var R;if(Us)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else pn?cd(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(ud&&n.locale!=="ko"&&(pn||T!=="onCompositionStart"?T==="onCompositionEnd"&&pn&&(R=ad()):(Pt=d,Bs="value"in Pt?Pt.value:Pt.textContent,pn=!0)),C=Mo(u,T),0<C.length&&(T=new Qa(T,e,null,n,d),c.push({event:T,listeners:C}),R?T.data=R:(R=dd(n),R!==null&&(T.data=R)))),(R=qm?Ym(e,n):Xm(e,n))&&(u=Mo(u,"onBeforeInput"),0<u.length&&(d=new Qa("onBeforeInput","beforeinput",null,n,d),c.push({event:d,listeners:u}),d.data=R))}wd(c,t)})}function Cr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Mo(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=yr(e,n),i!=null&&r.unshift(Cr(e,i,o)),i=yr(e,t),i!=null&&r.push(Cr(e,i,o))),e=e.return}return r}function cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function iu(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=yr(n,i),a!=null&&l.unshift(Cr(n,a,s))):o||(a=yr(n,i),a!=null&&l.push(Cr(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var dh=/\r\n?/g,fh=/\u0000|\uFFFD/g;function lu(e){return(typeof e=="string"?e:""+e).replace(dh,`
`).replace(fh,"")}function oo(e,t,n){if(t=lu(t),lu(e)!==t&&n)throw Error(L(425))}function Vo(){}var $l=null,Fl=null;function Ul(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Gl=typeof setTimeout=="function"?setTimeout:void 0,ph=typeof clearTimeout=="function"?clearTimeout:void 0,su=typeof Promise=="function"?Promise:void 0,mh=typeof queueMicrotask=="function"?queueMicrotask:typeof su<"u"?function(e){return su.resolve(null).then(e).catch(hh)}:Gl;function hh(e){setTimeout(function(){throw e})}function Ji(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Sr(t)}function It(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function au(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Un=Math.random().toString(36).slice(2),ot="__reactFiber$"+Un,Lr="__reactProps$"+Un,_t="__reactContainer$"+Un,Hl="__reactEvents$"+Un,gh="__reactListeners$"+Un,_h="__reactHandles$"+Un;function Qt(e){var t=e[ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=au(e);e!==null;){if(n=e[ot])return n;e=au(e)}return t}e=n,n=e.parentNode}return null}function Br(e){return e=e[ot]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function gi(e){return e[Lr]||null}var Wl=[],_n=-1;function Bt(e){return{current:e}}function H(e){0>_n||(e.current=Wl[_n],Wl[_n]=null,_n--)}function U(e,t){_n++,Wl[_n]=e.current,e.current=t}var Vt={},ye=Bt(Vt),Le=Bt(!1),tn=Vt;function On(e,t){var n=e.type.contextTypes;if(!n)return Vt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Pe(e){return e=e.childContextTypes,e!=null}function zo(){H(Le),H(ye)}function uu(e,t,n){if(ye.current!==Vt)throw Error(L(168));U(ye,t),U(Le,n)}function Cd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(L(108,tm(e)||"Unknown",o));return q({},n,r)}function Bo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vt,tn=ye.current,U(ye,e),U(Le,Le.current),!0}function cu(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=Cd(e,t,tn),r.__reactInternalMemoizedMergedChildContext=e,H(Le),H(ye),U(ye,e)):H(Le),U(Le,n)}var at=null,_i=!1,Zi=!1;function Ld(e){at===null?at=[e]:at.push(e)}function yh(e){_i=!0,Ld(e)}function $t(){if(!Zi&&at!==null){Zi=!0;var e=0,t=B;try{var n=at;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}at=null,_i=!1}catch(o){throw at!==null&&(at=at.slice(e+1)),Xc(js,$t),o}finally{B=t,Zi=!1}}return null}var yn=[],vn=0,$o=null,Fo=0,Be=[],$e=0,nn=null,ct=1,dt="";function Ht(e,t){yn[vn++]=Fo,yn[vn++]=$o,$o=e,Fo=t}function Pd(e,t,n){Be[$e++]=ct,Be[$e++]=dt,Be[$e++]=nn,nn=e;var r=ct;e=dt;var o=32-Xe(r)-1;r&=~(1<<o),n+=1;var i=32-Xe(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,ct=1<<32-Xe(t)+o|n<<o|r,dt=i+e}else ct=1<<i|n<<o|r,dt=e}function Hs(e){e.return!==null&&(Ht(e,1),Pd(e,1,0))}function Ws(e){for(;e===$o;)$o=yn[--vn],yn[vn]=null,Fo=yn[--vn],yn[vn]=null;for(;e===nn;)nn=Be[--$e],Be[$e]=null,dt=Be[--$e],Be[$e]=null,ct=Be[--$e],Be[$e]=null}var Oe=null,Ie=null,W=!1,Ye=null;function Rd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function du(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Oe=e,Ie=It(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Oe=e,Ie=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nn!==null?{id:ct,overflow:dt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Oe=e,Ie=null,!0):!1;default:return!1}}function Ql(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Kl(e){if(W){var t=Ie;if(t){var n=t;if(!du(e,t)){if(Ql(e))throw Error(L(418));t=It(n.nextSibling);var r=Oe;t&&du(e,t)?Rd(r,n):(e.flags=e.flags&-4097|2,W=!1,Oe=e)}}else{if(Ql(e))throw Error(L(418));e.flags=e.flags&-4097|2,W=!1,Oe=e}}}function fu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Oe=e}function io(e){if(e!==Oe)return!1;if(!W)return fu(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ul(e.type,e.memoizedProps)),t&&(t=Ie)){if(Ql(e))throw Td(),Error(L(418));for(;t;)Rd(e,t),t=It(t.nextSibling)}if(fu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ie=It(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ie=null}}else Ie=Oe?It(e.stateNode.nextSibling):null;return!0}function Td(){for(var e=Ie;e;)e=It(e.nextSibling)}function bn(){Ie=Oe=null,W=!1}function Qs(e){Ye===null?Ye=[e]:Ye.push(e)}var vh=xt.ReactCurrentBatchConfig;function Xn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function lo(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function pu(e){var t=e._init;return t(e._payload)}function Nd(e){function t(g,h){if(e){var y=g.deletions;y===null?(g.deletions=[h],g.flags|=16):y.push(h)}}function n(g,h){if(!e)return null;for(;h!==null;)t(g,h),h=h.sibling;return null}function r(g,h){for(g=new Map;h!==null;)h.key!==null?g.set(h.key,h):g.set(h.index,h),h=h.sibling;return g}function o(g,h){return g=jt(g,h),g.index=0,g.sibling=null,g}function i(g,h,y){return g.index=y,e?(y=g.alternate,y!==null?(y=y.index,y<h?(g.flags|=2,h):y):(g.flags|=2,h)):(g.flags|=1048576,h)}function l(g){return e&&g.alternate===null&&(g.flags|=2),g}function s(g,h,y,E){return h===null||h.tag!==6?(h=ll(y,g.mode,E),h.return=g,h):(h=o(h,y),h.return=g,h)}function a(g,h,y,E){var S=y.type;return S===fn?d(g,h,y.props.children,E,y.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&pu(S)===h.type)?(E=o(h,y.props),E.ref=Xn(g,h,y),E.return=g,E):(E=Po(y.type,y.key,y.props,null,g.mode,E),E.ref=Xn(g,h,y),E.return=g,E)}function u(g,h,y,E){return h===null||h.tag!==4||h.stateNode.containerInfo!==y.containerInfo||h.stateNode.implementation!==y.implementation?(h=sl(y,g.mode,E),h.return=g,h):(h=o(h,y.children||[]),h.return=g,h)}function d(g,h,y,E,S){return h===null||h.tag!==7?(h=Zt(y,g.mode,E,S),h.return=g,h):(h=o(h,y),h.return=g,h)}function c(g,h,y){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ll(""+h,g.mode,y),h.return=g,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case qr:return y=Po(h.type,h.key,h.props,null,g.mode,y),y.ref=Xn(g,null,h),y.return=g,y;case dn:return h=sl(h,g.mode,y),h.return=g,h;case wt:var E=h._init;return c(g,E(h._payload),y)}if(nr(h)||Wn(h))return h=Zt(h,g.mode,y,null),h.return=g,h;lo(g,h)}return null}function f(g,h,y,E){var S=h!==null?h.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return S!==null?null:s(g,h,""+y,E);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case qr:return y.key===S?a(g,h,y,E):null;case dn:return y.key===S?u(g,h,y,E):null;case wt:return S=y._init,f(g,h,S(y._payload),E)}if(nr(y)||Wn(y))return S!==null?null:d(g,h,y,E,null);lo(g,y)}return null}function _(g,h,y,E,S){if(typeof E=="string"&&E!==""||typeof E=="number")return g=g.get(y)||null,s(h,g,""+E,S);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case qr:return g=g.get(E.key===null?y:E.key)||null,a(h,g,E,S);case dn:return g=g.get(E.key===null?y:E.key)||null,u(h,g,E,S);case wt:var C=E._init;return _(g,h,y,C(E._payload),S)}if(nr(E)||Wn(E))return g=g.get(y)||null,d(h,g,E,S,null);lo(h,E)}return null}function v(g,h,y,E){for(var S=null,C=null,R=h,T=h=0,$=null;R!==null&&T<y.length;T++){R.index>T?($=R,R=null):$=R.sibling;var b=f(g,R,y[T],E);if(b===null){R===null&&(R=$);break}e&&R&&b.alternate===null&&t(g,R),h=i(b,h,T),C===null?S=b:C.sibling=b,C=b,R=$}if(T===y.length)return n(g,R),W&&Ht(g,T),S;if(R===null){for(;T<y.length;T++)R=c(g,y[T],E),R!==null&&(h=i(R,h,T),C===null?S=R:C.sibling=R,C=R);return W&&Ht(g,T),S}for(R=r(g,R);T<y.length;T++)$=_(R,g,T,y[T],E),$!==null&&(e&&$.alternate!==null&&R.delete($.key===null?T:$.key),h=i($,h,T),C===null?S=$:C.sibling=$,C=$);return e&&R.forEach(function(I){return t(g,I)}),W&&Ht(g,T),S}function x(g,h,y,E){var S=Wn(y);if(typeof S!="function")throw Error(L(150));if(y=S.call(y),y==null)throw Error(L(151));for(var C=S=null,R=h,T=h=0,$=null,b=y.next();R!==null&&!b.done;T++,b=y.next()){R.index>T?($=R,R=null):$=R.sibling;var I=f(g,R,b.value,E);if(I===null){R===null&&(R=$);break}e&&R&&I.alternate===null&&t(g,R),h=i(I,h,T),C===null?S=I:C.sibling=I,C=I,R=$}if(b.done)return n(g,R),W&&Ht(g,T),S;if(R===null){for(;!b.done;T++,b=y.next())b=c(g,b.value,E),b!==null&&(h=i(b,h,T),C===null?S=b:C.sibling=b,C=b);return W&&Ht(g,T),S}for(R=r(g,R);!b.done;T++,b=y.next())b=_(R,g,T,b.value,E),b!==null&&(e&&b.alternate!==null&&R.delete(b.key===null?T:b.key),h=i(b,h,T),C===null?S=b:C.sibling=b,C=b);return e&&R.forEach(function(ee){return t(g,ee)}),W&&Ht(g,T),S}function k(g,h,y,E){if(typeof y=="object"&&y!==null&&y.type===fn&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case qr:e:{for(var S=y.key,C=h;C!==null;){if(C.key===S){if(S=y.type,S===fn){if(C.tag===7){n(g,C.sibling),h=o(C,y.props.children),h.return=g,g=h;break e}}else if(C.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===wt&&pu(S)===C.type){n(g,C.sibling),h=o(C,y.props),h.ref=Xn(g,C,y),h.return=g,g=h;break e}n(g,C);break}else t(g,C);C=C.sibling}y.type===fn?(h=Zt(y.props.children,g.mode,E,y.key),h.return=g,g=h):(E=Po(y.type,y.key,y.props,null,g.mode,E),E.ref=Xn(g,h,y),E.return=g,g=E)}return l(g);case dn:e:{for(C=y.key;h!==null;){if(h.key===C)if(h.tag===4&&h.stateNode.containerInfo===y.containerInfo&&h.stateNode.implementation===y.implementation){n(g,h.sibling),h=o(h,y.children||[]),h.return=g,g=h;break e}else{n(g,h);break}else t(g,h);h=h.sibling}h=sl(y,g.mode,E),h.return=g,g=h}return l(g);case wt:return C=y._init,k(g,h,C(y._payload),E)}if(nr(y))return v(g,h,y,E);if(Wn(y))return x(g,h,y,E);lo(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,h!==null&&h.tag===6?(n(g,h.sibling),h=o(h,y),h.return=g,g=h):(n(g,h),h=ll(y,g.mode,E),h.return=g,g=h),l(g)):n(g,h)}return k}var Dn=Nd(!0),Ad=Nd(!1),Uo=Bt(null),Go=null,xn=null,Ks=null;function qs(){Ks=xn=Go=null}function Ys(e){var t=Uo.current;H(Uo),e._currentValue=t}function ql(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pn(e,t){Go=e,Ks=xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(Ks!==e)if(e={context:e,memoizedValue:t,next:null},xn===null){if(Go===null)throw Error(L(308));xn=e,Go.dependencies={lanes:0,firstContext:e}}else xn=xn.next=e;return t}var Kt=null;function Xs(e){Kt===null?Kt=[e]:Kt.push(e)}function Id(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Xs(t)):(n.next=o.next,o.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function Js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Od(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function mt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,yt(e,n)}return o=r.interleaved,o===null?(t.next=t,Xs(r)):(t.next=o.next,o.next=t),r.interleaved=t,yt(e,n)}function So(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}function mu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ho(e,t,n,r){var o=e.updateQueue;kt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var c=o.baseState;l=0,d=u=a=null,s=i;do{var f=s.lane,_=s.eventTime;if((r&f)===f){d!==null&&(d=d.next={eventTime:_,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var v=e,x=s;switch(f=t,_=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){c=v.call(_,c,f);break e}c=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(_,c,f):v,f==null)break e;c=q({},c,f);break e;case 2:kt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,f=o.effects,f===null?o.effects=[s]:f.push(s))}else _={eventTime:_,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=_,a=c):d=d.next=_,l|=f;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;f=s,s=f.next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}while(!0);if(d===null&&(a=c),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);on|=l,e.lanes=l,e.memoizedState=c}}function hu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(L(191,o));o.call(r)}}}var $r={},lt=Bt($r),Pr=Bt($r),Rr=Bt($r);function qt(e){if(e===$r)throw Error(L(174));return e}function Zs(e,t){switch(U(Rr,t),U(Pr,e),U(lt,$r),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Tl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Tl(t,e)}H(lt),U(lt,t)}function jn(){H(lt),H(Pr),H(Rr)}function bd(e){qt(Rr.current);var t=qt(lt.current),n=Tl(t,e.type);t!==n&&(U(Pr,e),U(lt,n))}function ea(e){Pr.current===e&&(H(lt),H(Pr))}var Q=Bt(0);function Wo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var el=[];function ta(){for(var e=0;e<el.length;e++)el[e]._workInProgressVersionPrimary=null;el.length=0}var Eo=xt.ReactCurrentDispatcher,tl=xt.ReactCurrentBatchConfig,rn=0,K=null,re=null,ue=null,Qo=!1,dr=!1,Tr=0,xh=0;function me(){throw Error(L(321))}function na(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function ra(e,t,n,r,o,i){if(rn=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Eo.current=e===null||e.memoizedState===null?kh:Ch,e=n(r,o),dr){i=0;do{if(dr=!1,Tr=0,25<=i)throw Error(L(301));i+=1,ue=re=null,t.updateQueue=null,Eo.current=Lh,e=n(r,o)}while(dr)}if(Eo.current=Ko,t=re!==null&&re.next!==null,rn=0,ue=re=K=null,Qo=!1,t)throw Error(L(300));return e}function oa(){var e=Tr!==0;return Tr=0,e}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?K.memoizedState=ue=e:ue=ue.next=e,ue}function We(){if(re===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=ue===null?K.memoizedState:ue.next;if(t!==null)ue=t,re=e;else{if(e===null)throw Error(L(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},ue===null?K.memoizedState=ue=e:ue=ue.next=e}return ue}function Nr(e,t){return typeof t=="function"?t(e):t}function nl(e){var t=We(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=re,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((rn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=c,l=r):a=a.next=c,K.lanes|=d,on|=d}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,Ze(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,K.lanes|=i,on|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function rl(e){var t=We(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);Ze(i,t.memoizedState)||(Ce=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Dd(){}function jd(e,t){var n=K,r=We(),o=t(),i=!Ze(r.memoizedState,o);if(i&&(r.memoizedState=o,Ce=!0),r=r.queue,ia(zd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,Ar(9,Vd.bind(null,n,r,o,t),void 0,null),ce===null)throw Error(L(349));rn&30||Md(n,t,o)}return o}function Md(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Vd(e,t,n,r){t.value=n,t.getSnapshot=r,Bd(t)&&$d(e)}function zd(e,t,n){return n(function(){Bd(t)&&$d(e)})}function Bd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function $d(e){var t=yt(e,1);t!==null&&Je(t,e,1,-1)}function gu(e){var t=rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Nr,lastRenderedState:e},t.queue=e,e=e.dispatch=wh.bind(null,K,e),[t.memoizedState,e]}function Ar(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Fd(){return We().memoizedState}function wo(e,t,n,r){var o=rt();K.flags|=e,o.memoizedState=Ar(1|t,n,void 0,r===void 0?null:r)}function yi(e,t,n,r){var o=We();r=r===void 0?null:r;var i=void 0;if(re!==null){var l=re.memoizedState;if(i=l.destroy,r!==null&&na(r,l.deps)){o.memoizedState=Ar(t,n,i,r);return}}K.flags|=e,o.memoizedState=Ar(1|t,n,i,r)}function _u(e,t){return wo(8390656,8,e,t)}function ia(e,t){return yi(2048,8,e,t)}function Ud(e,t){return yi(4,2,e,t)}function Gd(e,t){return yi(4,4,e,t)}function Hd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wd(e,t,n){return n=n!=null?n.concat([e]):null,yi(4,4,Hd.bind(null,t,e),n)}function la(){}function Qd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Kd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function qd(e,t,n){return rn&21?(Ze(n,t)||(n=ed(),K.lanes|=n,on|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function Sh(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=tl.transition;tl.transition={};try{e(!1),t()}finally{B=n,tl.transition=r}}function Yd(){return We().memoizedState}function Eh(e,t,n){var r=Dt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Xd(e))Jd(t,n);else if(n=Id(e,t,n,r),n!==null){var o=Se();Je(n,e,r,o),Zd(n,t,r)}}function wh(e,t,n){var r=Dt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Xd(e))Jd(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,Ze(s,l)){var a=t.interleaved;a===null?(o.next=o,Xs(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Id(e,t,o,r),n!==null&&(o=Se(),Je(n,e,r,o),Zd(n,t,r))}}function Xd(e){var t=e.alternate;return e===K||t!==null&&t===K}function Jd(e,t){dr=Qo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Zd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}var Ko={readContext:He,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},kh={readContext:He,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:_u,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,wo(4194308,4,Hd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return wo(4194308,4,e,t)},useInsertionEffect:function(e,t){return wo(4,2,e,t)},useMemo:function(e,t){var n=rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Eh.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:gu,useDebugValue:la,useDeferredValue:function(e){return rt().memoizedState=e},useTransition:function(){var e=gu(!1),t=e[0];return e=Sh.bind(null,e[1]),rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,o=rt();if(W){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),ce===null)throw Error(L(349));rn&30||Md(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,_u(zd.bind(null,r,i,e),[e]),r.flags|=2048,Ar(9,Vd.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=rt(),t=ce.identifierPrefix;if(W){var n=dt,r=ct;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Tr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=xh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ch={readContext:He,useCallback:Qd,useContext:He,useEffect:ia,useImperativeHandle:Wd,useInsertionEffect:Ud,useLayoutEffect:Gd,useMemo:Kd,useReducer:nl,useRef:Fd,useState:function(){return nl(Nr)},useDebugValue:la,useDeferredValue:function(e){var t=We();return qd(t,re.memoizedState,e)},useTransition:function(){var e=nl(Nr)[0],t=We().memoizedState;return[e,t]},useMutableSource:Dd,useSyncExternalStore:jd,useId:Yd,unstable_isNewReconciler:!1},Lh={readContext:He,useCallback:Qd,useContext:He,useEffect:ia,useImperativeHandle:Wd,useInsertionEffect:Ud,useLayoutEffect:Gd,useMemo:Kd,useReducer:rl,useRef:Fd,useState:function(){return rl(Nr)},useDebugValue:la,useDeferredValue:function(e){var t=We();return re===null?t.memoizedState=e:qd(t,re.memoizedState,e)},useTransition:function(){var e=rl(Nr)[0],t=We().memoizedState;return[e,t]},useMutableSource:Dd,useSyncExternalStore:jd,useId:Yd,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Yl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var vi={isMounted:function(e){return(e=e._reactInternals)?an(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Se(),o=Dt(e),i=mt(r,o);i.payload=t,n!=null&&(i.callback=n),t=Ot(e,i,o),t!==null&&(Je(t,e,o,r),So(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Se(),o=Dt(e),i=mt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Ot(e,i,o),t!==null&&(Je(t,e,o,r),So(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Se(),r=Dt(e),o=mt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Ot(e,o,r),t!==null&&(Je(t,e,r,n),So(t,e,r))}};function yu(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!wr(n,r)||!wr(o,i):!0}function ef(e,t,n){var r=!1,o=Vt,i=t.contextType;return typeof i=="object"&&i!==null?i=He(i):(o=Pe(t)?tn:ye.current,r=t.contextTypes,i=(r=r!=null)?On(e,o):Vt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=vi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function vu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&vi.enqueueReplaceState(t,t.state,null)}function Xl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Js(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=He(i):(i=Pe(t)?tn:ye.current,o.context=On(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Yl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&vi.enqueueReplaceState(o,o.state,null),Ho(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Mn(e,t){try{var n="",r=t;do n+=em(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ol(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Jl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ph=typeof WeakMap=="function"?WeakMap:Map;function tf(e,t,n){n=mt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Yo||(Yo=!0,as=r),Jl(e,t)},n}function nf(e,t,n){n=mt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Jl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Jl(e,t),typeof r!="function"&&(bt===null?bt=new Set([this]):bt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function xu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ph;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=$h.bind(null,e,t,n),t.then(e,e))}function Su(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Eu(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=mt(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var Rh=xt.ReactCurrentOwner,Ce=!1;function ve(e,t,n,r){t.child=e===null?Ad(t,null,n,r):Dn(t,e.child,n,r)}function wu(e,t,n,r,o){n=n.render;var i=t.ref;return Pn(t,o),r=ra(e,t,n,r,i,o),n=oa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&n&&Hs(t),t.flags|=1,ve(e,t,r,o),t.child)}function ku(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!ma(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,rf(e,t,i,r,o)):(e=Po(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:wr,n(l,r)&&e.ref===t.ref)return vt(e,t,o)}return t.flags|=1,e=jt(i,r),e.ref=t.ref,e.return=t,t.child=e}function rf(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(wr(i,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,vt(e,t,o)}return Zl(e,t,n,r,o)}function of(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(En,Ae),Ae|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(En,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(En,Ae),Ae|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(En,Ae),Ae|=r;return ve(e,t,o,n),t.child}function lf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Zl(e,t,n,r,o){var i=Pe(n)?tn:ye.current;return i=On(t,i),Pn(t,o),n=ra(e,t,n,r,i,o),r=oa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&r&&Hs(t),t.flags|=1,ve(e,t,n,o),t.child)}function Cu(e,t,n,r,o){if(Pe(n)){var i=!0;Bo(t)}else i=!1;if(Pn(t,o),t.stateNode===null)ko(e,t),ef(t,n,r),Xl(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Pe(n)?tn:ye.current,u=On(t,u));var d=n.getDerivedStateFromProps,c=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";c||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&vu(t,l,r,u),kt=!1;var f=t.memoizedState;l.state=f,Ho(t,r,l,o),a=t.memoizedState,s!==r||f!==a||Le.current||kt?(typeof d=="function"&&(Yl(t,n,d,r),a=t.memoizedState),(s=kt||yu(t,n,s,r,f,a,u))?(c||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Od(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ke(t.type,s),l.props=u,c=t.pendingProps,f=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Pe(n)?tn:ye.current,a=On(t,a));var _=n.getDerivedStateFromProps;(d=typeof _=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==c||f!==a)&&vu(t,l,r,a),kt=!1,f=t.memoizedState,l.state=f,Ho(t,r,l,o);var v=t.memoizedState;s!==c||f!==v||Le.current||kt?(typeof _=="function"&&(Yl(t,n,_,r),v=t.memoizedState),(u=kt||yu(t,n,u,r,f,v,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,v,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,v,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),l.props=r,l.state=v,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return es(e,t,n,r,i,o)}function es(e,t,n,r,o,i){lf(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&cu(t,n,!1),vt(e,t,i);r=t.stateNode,Rh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Dn(t,e.child,null,i),t.child=Dn(t,null,s,i)):ve(e,t,s,i),t.memoizedState=r.state,o&&cu(t,n,!0),t.child}function sf(e){var t=e.stateNode;t.pendingContext?uu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&uu(e,t.context,!1),Zs(e,t.containerInfo)}function Lu(e,t,n,r,o){return bn(),Qs(o),t.flags|=256,ve(e,t,n,r),t.child}var ts={dehydrated:null,treeContext:null,retryLane:0};function ns(e){return{baseLanes:e,cachePool:null,transitions:null}}function af(e,t,n){var r=t.pendingProps,o=Q.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),U(Q,o&1),e===null)return Kl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Ei(l,r,0,null),e=Zt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ns(n),t.memoizedState=ts,e):sa(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return Th(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=jt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=jt(s,i):(i=Zt(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?ns(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=ts,r}return i=e.child,e=i.sibling,r=jt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function sa(e,t){return t=Ei({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function so(e,t,n,r){return r!==null&&Qs(r),Dn(t,e.child,null,n),e=sa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Th(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=ol(Error(L(422))),so(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ei({mode:"visible",children:r.children},o,0,null),i=Zt(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Dn(t,e.child,null,l),t.child.memoizedState=ns(l),t.memoizedState=ts,i);if(!(t.mode&1))return so(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(L(419)),r=ol(i,r,void 0),so(e,t,l,r)}if(s=(l&e.childLanes)!==0,Ce||s){if(r=ce,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,yt(e,o),Je(r,e,o,-1))}return pa(),r=ol(Error(L(421))),so(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Fh.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ie=It(o.nextSibling),Oe=t,W=!0,Ye=null,e!==null&&(Be[$e++]=ct,Be[$e++]=dt,Be[$e++]=nn,ct=e.id,dt=e.overflow,nn=t),t=sa(t,r.children),t.flags|=4096,t)}function Pu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ql(e.return,t,n)}function il(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function uf(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ve(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Pu(e,n,t);else if(e.tag===19)Pu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(Q,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Wo(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),il(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Wo(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}il(t,!0,n,null,i);break;case"together":il(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ko(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),on|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=jt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=jt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Nh(e,t,n){switch(t.tag){case 3:sf(t),bn();break;case 5:bd(t);break;case 1:Pe(t.type)&&Bo(t);break;case 4:Zs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;U(Uo,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?af(e,t,n):(U(Q,Q.current&1),e=vt(e,t,n),e!==null?e.sibling:null);U(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return uf(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),U(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,of(e,t,n)}return vt(e,t,n)}var cf,rs,df,ff;cf=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};rs=function(){};df=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,qt(lt.current);var i=null;switch(n){case"input":o=Cl(e,o),r=Cl(e,r),i=[];break;case"select":o=q({},o,{value:void 0}),r=q({},r,{value:void 0}),i=[];break;case"textarea":o=Rl(e,o),r=Rl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Vo)}Nl(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(gr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(gr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&G("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};ff=function(e,t,n,r){n!==r&&(t.flags|=4)};function Jn(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ah(e,t,n){var r=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return Pe(t.type)&&zo(),he(t),null;case 3:return r=t.stateNode,jn(),H(Le),H(ye),ta(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(io(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(ds(Ye),Ye=null))),rs(e,t),he(t),null;case 5:ea(t);var o=qt(Rr.current);if(n=t.type,e!==null&&t.stateNode!=null)df(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return he(t),null}if(e=qt(lt.current),io(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ot]=t,r[Lr]=i,e=(t.mode&1)!==0,n){case"dialog":G("cancel",r),G("close",r);break;case"iframe":case"object":case"embed":G("load",r);break;case"video":case"audio":for(o=0;o<or.length;o++)G(or[o],r);break;case"source":G("error",r);break;case"img":case"image":case"link":G("error",r),G("load",r);break;case"details":G("toggle",r);break;case"input":ja(r,i),G("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},G("invalid",r);break;case"textarea":Va(r,i),G("invalid",r)}Nl(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&oo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&oo(r.textContent,s,e),o=["children",""+s]):gr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&G("scroll",r)}switch(n){case"input":Yr(r),Ma(r,i,!0);break;case"textarea":Yr(r),za(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Vo)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=zc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[ot]=t,e[Lr]=r,cf(e,t,!1,!1),t.stateNode=e;e:{switch(l=Al(n,r),n){case"dialog":G("cancel",e),G("close",e),o=r;break;case"iframe":case"object":case"embed":G("load",e),o=r;break;case"video":case"audio":for(o=0;o<or.length;o++)G(or[o],e);o=r;break;case"source":G("error",e),o=r;break;case"img":case"image":case"link":G("error",e),G("load",e),o=r;break;case"details":G("toggle",e),o=r;break;case"input":ja(e,r),o=Cl(e,r),G("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=q({},r,{value:void 0}),G("invalid",e);break;case"textarea":Va(e,r),o=Rl(e,r),G("invalid",e);break;default:o=r}Nl(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Fc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Bc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&_r(e,a):typeof a=="number"&&_r(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(gr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&G("scroll",e):a!=null&&As(e,i,a,l))}switch(n){case"input":Yr(e),Ma(e,r,!1);break;case"textarea":Yr(e),za(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Mt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?wn(e,!!r.multiple,i,!1):r.defaultValue!=null&&wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Vo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return he(t),null;case 6:if(e&&t.stateNode!=null)ff(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=qt(Rr.current),qt(lt.current),io(t)){if(r=t.stateNode,n=t.memoizedProps,r[ot]=t,(i=r.nodeValue!==n)&&(e=Oe,e!==null))switch(e.tag){case 3:oo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&oo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ot]=t,t.stateNode=r}return he(t),null;case 13:if(H(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ie!==null&&t.mode&1&&!(t.flags&128))Td(),bn(),t.flags|=98560,i=!1;else if(i=io(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(L(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(L(317));i[ot]=t}else bn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),i=!1}else Ye!==null&&(ds(Ye),Ye=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?ie===0&&(ie=3):pa())),t.updateQueue!==null&&(t.flags|=4),he(t),null);case 4:return jn(),rs(e,t),e===null&&kr(t.stateNode.containerInfo),he(t),null;case 10:return Ys(t.type._context),he(t),null;case 17:return Pe(t.type)&&zo(),he(t),null;case 19:if(H(Q),i=t.memoizedState,i===null)return he(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Jn(i,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Wo(e),l!==null){for(t.flags|=128,Jn(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(Q,Q.current&1|2),t.child}e=e.sibling}i.tail!==null&&Z()>Vn&&(t.flags|=128,r=!0,Jn(i,!1),t.lanes=4194304)}else{if(!r)if(e=Wo(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Jn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!W)return he(t),null}else 2*Z()-i.renderingStartTime>Vn&&n!==1073741824&&(t.flags|=128,r=!0,Jn(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Z(),t.sibling=null,n=Q.current,U(Q,r?n&1|2:n&1),t):(he(t),null);case 22:case 23:return fa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function Ih(e,t){switch(Ws(t),t.tag){case 1:return Pe(t.type)&&zo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return jn(),H(Le),H(ye),ta(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ea(t),null;case 13:if(H(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(Q),null;case 4:return jn(),null;case 10:return Ys(t.type._context),null;case 22:case 23:return fa(),null;case 24:return null;default:return null}}var ao=!1,_e=!1,Oh=typeof WeakSet=="function"?WeakSet:Set,N=null;function Sn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function os(e,t,n){try{n()}catch(r){X(e,t,r)}}var Ru=!1;function bh(e,t){if($l=Do,e=gd(),Gs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,d=0,c=e,f=null;t:for(;;){for(var _;c!==n||o!==0&&c.nodeType!==3||(s=l+o),c!==i||r!==0&&c.nodeType!==3||(a=l+r),c.nodeType===3&&(l+=c.nodeValue.length),(_=c.firstChild)!==null;)f=c,c=_;for(;;){if(c===e)break t;if(f===n&&++u===o&&(s=l),f===i&&++d===r&&(a=l),(_=c.nextSibling)!==null)break;c=f,f=c.parentNode}c=_}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fl={focusedElem:e,selectionRange:n},Do=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,k=v.memoizedState,g=t.stateNode,h=g.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ke(t.type,x),k);g.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(E){X(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return v=Ru,Ru=!1,v}function fr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&os(t,n,i)}o=o.next}while(o!==r)}}function xi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function is(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function pf(e){var t=e.alternate;t!==null&&(e.alternate=null,pf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ot],delete t[Lr],delete t[Hl],delete t[gh],delete t[_h])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function mf(e){return e.tag===5||e.tag===3||e.tag===4}function Tu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||mf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ls(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Vo));else if(r!==4&&(e=e.child,e!==null))for(ls(e,t,n),e=e.sibling;e!==null;)ls(e,t,n),e=e.sibling}function ss(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ss(e,t,n),e=e.sibling;e!==null;)ss(e,t,n),e=e.sibling}var de=null,qe=!1;function Et(e,t,n){for(n=n.child;n!==null;)hf(e,t,n),n=n.sibling}function hf(e,t,n){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(fi,n)}catch{}switch(n.tag){case 5:_e||Sn(n,t);case 6:var r=de,o=qe;de=null,Et(e,t,n),de=r,qe=o,de!==null&&(qe?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(qe?(e=de,n=n.stateNode,e.nodeType===8?Ji(e.parentNode,n):e.nodeType===1&&Ji(e,n),Sr(e)):Ji(de,n.stateNode));break;case 4:r=de,o=qe,de=n.stateNode.containerInfo,qe=!0,Et(e,t,n),de=r,qe=o;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&os(n,t,l),o=o.next}while(o!==r)}Et(e,t,n);break;case 1:if(!_e&&(Sn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){X(n,t,s)}Et(e,t,n);break;case 21:Et(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,Et(e,t,n),_e=r):Et(e,t,n);break;default:Et(e,t,n)}}function Nu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Oh),t.forEach(function(r){var o=Uh.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Qe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:de=s.stateNode,qe=!1;break e;case 3:de=s.stateNode.containerInfo,qe=!0;break e;case 4:de=s.stateNode.containerInfo,qe=!0;break e}s=s.return}if(de===null)throw Error(L(160));hf(i,l,o),de=null,qe=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){X(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)gf(t,e),t=t.sibling}function gf(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Qe(t,e),et(e),r&4){try{fr(3,e,e.return),xi(3,e)}catch(x){X(e,e.return,x)}try{fr(5,e,e.return)}catch(x){X(e,e.return,x)}}break;case 1:Qe(t,e),et(e),r&512&&n!==null&&Sn(n,n.return);break;case 5:if(Qe(t,e),et(e),r&512&&n!==null&&Sn(n,n.return),e.flags&32){var o=e.stateNode;try{_r(o,"")}catch(x){X(e,e.return,x)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&Mc(o,i),Al(s,l);var u=Al(s,i);for(l=0;l<a.length;l+=2){var d=a[l],c=a[l+1];d==="style"?Fc(o,c):d==="dangerouslySetInnerHTML"?Bc(o,c):d==="children"?_r(o,c):As(o,d,c,u)}switch(s){case"input":Ll(o,i);break;case"textarea":Vc(o,i);break;case"select":var f=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var _=i.value;_!=null?wn(o,!!i.multiple,_,!1):f!==!!i.multiple&&(i.defaultValue!=null?wn(o,!!i.multiple,i.defaultValue,!0):wn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Lr]=i}catch(x){X(e,e.return,x)}}break;case 6:if(Qe(t,e),et(e),r&4){if(e.stateNode===null)throw Error(L(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(x){X(e,e.return,x)}}break;case 3:if(Qe(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Sr(t.containerInfo)}catch(x){X(e,e.return,x)}break;case 4:Qe(t,e),et(e);break;case 13:Qe(t,e),et(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ca=Z())),r&4&&Nu(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||d,Qe(t,e),_e=u):Qe(t,e),et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(N=e,d=e.child;d!==null;){for(c=N=d;N!==null;){switch(f=N,_=f.child,f.tag){case 0:case 11:case 14:case 15:fr(4,f,f.return);break;case 1:Sn(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){r=f,n=f.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){X(r,n,x)}}break;case 5:Sn(f,f.return);break;case 22:if(f.memoizedState!==null){Iu(c);continue}}_!==null?(_.return=f,N=_):Iu(c)}d=d.sibling}e:for(d=null,c=e;;){if(c.tag===5){if(d===null){d=c;try{o=c.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=c.stateNode,a=c.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=$c("display",l))}catch(x){X(e,e.return,x)}}}else if(c.tag===6){if(d===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(x){X(e,e.return,x)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;d===c&&(d=null),c=c.return}d===c&&(d=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:Qe(t,e),et(e),r&4&&Nu(e);break;case 21:break;default:Qe(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(mf(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(_r(o,""),r.flags&=-33);var i=Tu(e);ss(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Tu(e);ls(e,s,l);break;default:throw Error(L(161))}}catch(a){X(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Dh(e,t,n){N=e,_f(e)}function _f(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var o=N,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||ao;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||_e;s=ao;var u=_e;if(ao=l,(_e=a)&&!u)for(N=o;N!==null;)l=N,a=l.child,l.tag===22&&l.memoizedState!==null?Ou(o):a!==null?(a.return=l,N=a):Ou(o);for(;i!==null;)N=i,_f(i),i=i.sibling;N=o,ao=s,_e=u}Au(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,N=i):Au(e)}}function Au(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||xi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&hu(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}hu(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var c=d.dehydrated;c!==null&&Sr(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}_e||t.flags&512&&is(t)}catch(f){X(t,t.return,f)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function Iu(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function Ou(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{xi(4,t)}catch(a){X(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){X(t,o,a)}}var i=t.return;try{is(t)}catch(a){X(t,i,a)}break;case 5:var l=t.return;try{is(t)}catch(a){X(t,l,a)}}}catch(a){X(t,t.return,a)}if(t===e){N=null;break}var s=t.sibling;if(s!==null){s.return=t.return,N=s;break}N=t.return}}var jh=Math.ceil,qo=xt.ReactCurrentDispatcher,aa=xt.ReactCurrentOwner,Ge=xt.ReactCurrentBatchConfig,z=0,ce=null,ne=null,fe=0,Ae=0,En=Bt(0),ie=0,Ir=null,on=0,Si=0,ua=0,pr=null,ke=null,ca=0,Vn=1/0,st=null,Yo=!1,as=null,bt=null,uo=!1,Rt=null,Xo=0,mr=0,us=null,Co=-1,Lo=0;function Se(){return z&6?Z():Co!==-1?Co:Co=Z()}function Dt(e){return e.mode&1?z&2&&fe!==0?fe&-fe:vh.transition!==null?(Lo===0&&(Lo=ed()),Lo):(e=B,e!==0||(e=window.event,e=e===void 0?16:sd(e.type)),e):1}function Je(e,t,n,r){if(50<mr)throw mr=0,us=null,Error(L(185));Vr(e,n,r),(!(z&2)||e!==ce)&&(e===ce&&(!(z&2)&&(Si|=n),ie===4&&Lt(e,fe)),Re(e,r),n===1&&z===0&&!(t.mode&1)&&(Vn=Z()+500,_i&&$t()))}function Re(e,t){var n=e.callbackNode;vm(e,t);var r=bo(e,e===ce?fe:0);if(r===0)n!==null&&Fa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Fa(n),t===1)e.tag===0?yh(bu.bind(null,e)):Ld(bu.bind(null,e)),mh(function(){!(z&6)&&$t()}),n=null;else{switch(td(r)){case 1:n=js;break;case 4:n=Jc;break;case 16:n=Oo;break;case 536870912:n=Zc;break;default:n=Oo}n=Cf(n,yf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function yf(e,t){if(Co=-1,Lo=0,z&6)throw Error(L(327));var n=e.callbackNode;if(Rn()&&e.callbackNode!==n)return null;var r=bo(e,e===ce?fe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Jo(e,r);else{t=r;var o=z;z|=2;var i=xf();(ce!==e||fe!==t)&&(st=null,Vn=Z()+500,Jt(e,t));do try{zh();break}catch(s){vf(e,s)}while(!0);qs(),qo.current=i,z=o,ne!==null?t=0:(ce=null,fe=0,t=ie)}if(t!==0){if(t===2&&(o=jl(e),o!==0&&(r=o,t=cs(e,o))),t===1)throw n=Ir,Jt(e,0),Lt(e,r),Re(e,Z()),n;if(t===6)Lt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Mh(o)&&(t=Jo(e,r),t===2&&(i=jl(e),i!==0&&(r=i,t=cs(e,i))),t===1))throw n=Ir,Jt(e,0),Lt(e,r),Re(e,Z()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:Wt(e,ke,st);break;case 3:if(Lt(e,r),(r&130023424)===r&&(t=ca+500-Z(),10<t)){if(bo(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Se(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Gl(Wt.bind(null,e,ke,st),t);break}Wt(e,ke,st);break;case 4:if(Lt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-Xe(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=Z()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jh(r/1960))-r,10<r){e.timeoutHandle=Gl(Wt.bind(null,e,ke,st),r);break}Wt(e,ke,st);break;case 5:Wt(e,ke,st);break;default:throw Error(L(329))}}}return Re(e,Z()),e.callbackNode===n?yf.bind(null,e):null}function cs(e,t){var n=pr;return e.current.memoizedState.isDehydrated&&(Jt(e,t).flags|=256),e=Jo(e,t),e!==2&&(t=ke,ke=n,t!==null&&ds(t)),e}function ds(e){ke===null?ke=e:ke.push.apply(ke,e)}function Mh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Ze(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Lt(e,t){for(t&=~ua,t&=~Si,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function bu(e){if(z&6)throw Error(L(327));Rn();var t=bo(e,0);if(!(t&1))return Re(e,Z()),null;var n=Jo(e,t);if(e.tag!==0&&n===2){var r=jl(e);r!==0&&(t=r,n=cs(e,r))}if(n===1)throw n=Ir,Jt(e,0),Lt(e,t),Re(e,Z()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Wt(e,ke,st),Re(e,Z()),null}function da(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Vn=Z()+500,_i&&$t())}}function ln(e){Rt!==null&&Rt.tag===0&&!(z&6)&&Rn();var t=z;z|=1;var n=Ge.transition,r=B;try{if(Ge.transition=null,B=1,e)return e()}finally{B=r,Ge.transition=n,z=t,!(z&6)&&$t()}}function fa(){Ae=En.current,H(En)}function Jt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ph(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(Ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&zo();break;case 3:jn(),H(Le),H(ye),ta();break;case 5:ea(r);break;case 4:jn();break;case 13:H(Q);break;case 19:H(Q);break;case 10:Ys(r.type._context);break;case 22:case 23:fa()}n=n.return}if(ce=e,ne=e=jt(e.current,null),fe=Ae=t,ie=0,Ir=null,ua=Si=on=0,ke=pr=null,Kt!==null){for(t=0;t<Kt.length;t++)if(n=Kt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}Kt=null}return e}function vf(e,t){do{var n=ne;try{if(qs(),Eo.current=Ko,Qo){for(var r=K.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Qo=!1}if(rn=0,ue=re=K=null,dr=!1,Tr=0,aa.current=null,n===null||n.return===null){ie=1,Ir=t,ne=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=fe,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,c=d.tag;if(!(d.mode&1)&&(c===0||c===11||c===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var _=Su(l);if(_!==null){_.flags&=-257,Eu(_,l,s,i,t),_.mode&1&&xu(i,u,t),t=_,a=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(a),t.updateQueue=x}else v.add(a);break e}else{if(!(t&1)){xu(i,u,t),pa();break e}a=Error(L(426))}}else if(W&&s.mode&1){var k=Su(l);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Eu(k,l,s,i,t),Qs(Mn(a,s));break e}}i=a=Mn(a,s),ie!==4&&(ie=2),pr===null?pr=[i]:pr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=tf(i,a,t);mu(i,g);break e;case 1:s=a;var h=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(bt===null||!bt.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=nf(i,s,t);mu(i,E);break e}}i=i.return}while(i!==null)}Ef(n)}catch(S){t=S,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function xf(){var e=qo.current;return qo.current=Ko,e===null?Ko:e}function pa(){(ie===0||ie===3||ie===2)&&(ie=4),ce===null||!(on&268435455)&&!(Si&268435455)||Lt(ce,fe)}function Jo(e,t){var n=z;z|=2;var r=xf();(ce!==e||fe!==t)&&(st=null,Jt(e,t));do try{Vh();break}catch(o){vf(e,o)}while(!0);if(qs(),z=n,qo.current=r,ne!==null)throw Error(L(261));return ce=null,fe=0,ie}function Vh(){for(;ne!==null;)Sf(ne)}function zh(){for(;ne!==null&&!cm();)Sf(ne)}function Sf(e){var t=kf(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?Ef(e):ne=t,aa.current=null}function Ef(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Ih(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,ne=null;return}}else if(n=Ah(n,t,Ae),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);ie===0&&(ie=5)}function Wt(e,t,n){var r=B,o=Ge.transition;try{Ge.transition=null,B=1,Bh(e,t,n,r)}finally{Ge.transition=o,B=r}return null}function Bh(e,t,n,r){do Rn();while(Rt!==null);if(z&6)throw Error(L(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(xm(e,i),e===ce&&(ne=ce=null,fe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||uo||(uo=!0,Cf(Oo,function(){return Rn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ge.transition,Ge.transition=null;var l=B;B=1;var s=z;z|=4,aa.current=null,bh(e,n),gf(n,e),lh(Fl),Do=!!$l,Fl=$l=null,e.current=n,Dh(n),dm(),z=s,B=l,Ge.transition=i}else e.current=n;if(uo&&(uo=!1,Rt=e,Xo=o),i=e.pendingLanes,i===0&&(bt=null),mm(n.stateNode),Re(e,Z()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Yo)throw Yo=!1,e=as,as=null,e;return Xo&1&&e.tag!==0&&Rn(),i=e.pendingLanes,i&1?e===us?mr++:(mr=0,us=e):mr=0,$t(),null}function Rn(){if(Rt!==null){var e=td(Xo),t=Ge.transition,n=B;try{if(Ge.transition=null,B=16>e?16:e,Rt===null)var r=!1;else{if(e=Rt,Rt=null,Xo=0,z&6)throw Error(L(331));var o=z;for(z|=4,N=e.current;N!==null;){var i=N,l=i.child;if(N.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(N=u;N!==null;){var d=N;switch(d.tag){case 0:case 11:case 15:fr(8,d,i)}var c=d.child;if(c!==null)c.return=d,N=c;else for(;N!==null;){d=N;var f=d.sibling,_=d.return;if(pf(d),d===u){N=null;break}if(f!==null){f.return=_,N=f;break}N=_}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var k=x.sibling;x.sibling=null,x=k}while(x!==null)}}N=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,N=l;else e:for(;N!==null;){if(i=N,i.flags&2048)switch(i.tag){case 0:case 11:case 15:fr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,N=g;break e}N=i.return}}var h=e.current;for(N=h;N!==null;){l=N;var y=l.child;if(l.subtreeFlags&2064&&y!==null)y.return=l,N=y;else e:for(l=h;N!==null;){if(s=N,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:xi(9,s)}}catch(S){X(s,s.return,S)}if(s===l){N=null;break e}var E=s.sibling;if(E!==null){E.return=s.return,N=E;break e}N=s.return}}if(z=o,$t(),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(fi,e)}catch{}r=!0}return r}finally{B=n,Ge.transition=t}}return!1}function Du(e,t,n){t=Mn(n,t),t=tf(e,t,1),e=Ot(e,t,1),t=Se(),e!==null&&(Vr(e,1,t),Re(e,t))}function X(e,t,n){if(e.tag===3)Du(e,e,n);else for(;t!==null;){if(t.tag===3){Du(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(bt===null||!bt.has(r))){e=Mn(n,e),e=nf(t,e,1),t=Ot(t,e,1),e=Se(),t!==null&&(Vr(t,1,e),Re(t,e));break}}t=t.return}}function $h(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Se(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(fe&n)===n&&(ie===4||ie===3&&(fe&130023424)===fe&&500>Z()-ca?Jt(e,0):ua|=n),Re(e,t)}function wf(e,t){t===0&&(e.mode&1?(t=Zr,Zr<<=1,!(Zr&130023424)&&(Zr=4194304)):t=1);var n=Se();e=yt(e,t),e!==null&&(Vr(e,t,n),Re(e,n))}function Fh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),wf(e,n)}function Uh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),wf(e,n)}var kf;kf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Le.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,Nh(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,W&&t.flags&1048576&&Pd(t,Fo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ko(e,t),e=t.pendingProps;var o=On(t,ye.current);Pn(t,n),o=ra(null,t,r,e,o,n);var i=oa();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(i=!0,Bo(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Js(t),o.updater=vi,t.stateNode=o,o._reactInternals=t,Xl(t,r,e,n),t=es(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Hs(t),ve(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ko(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=Hh(r),e=Ke(r,e),o){case 0:t=Zl(null,t,r,e,n);break e;case 1:t=Cu(null,t,r,e,n);break e;case 11:t=wu(null,t,r,e,n);break e;case 14:t=ku(null,t,r,Ke(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),Zl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),Cu(e,t,r,o,n);case 3:e:{if(sf(t),e===null)throw Error(L(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Od(e,t),Ho(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Mn(Error(L(423)),t),t=Lu(e,t,r,n,o);break e}else if(r!==o){o=Mn(Error(L(424)),t),t=Lu(e,t,r,n,o);break e}else for(Ie=It(t.stateNode.containerInfo.firstChild),Oe=t,W=!0,Ye=null,n=Ad(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(bn(),r===o){t=vt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return bd(t),e===null&&Kl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Ul(r,o)?l=null:i!==null&&Ul(r,i)&&(t.flags|=32),lf(e,t),ve(e,t,l,n),t.child;case 6:return e===null&&Kl(t),null;case 13:return af(e,t,n);case 4:return Zs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Dn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),wu(e,t,r,o,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,U(Uo,r._currentValue),r._currentValue=l,i!==null)if(Ze(i.value,l)){if(i.children===o.children&&!Le.current){t=vt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=mt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),ql(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(L(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),ql(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ve(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Pn(t,n),o=He(o),r=r(o),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,o=Ke(r,t.pendingProps),o=Ke(r.type,o),ku(e,t,r,o,n);case 15:return rf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),ko(e,t),t.tag=1,Pe(r)?(e=!0,Bo(t)):e=!1,Pn(t,n),ef(t,r,o),Xl(t,r,o,n),es(null,t,r,!0,e,n);case 19:return uf(e,t,n);case 22:return of(e,t,n)}throw Error(L(156,t.tag))};function Cf(e,t){return Xc(e,t)}function Gh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new Gh(e,t,n,r)}function ma(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Hh(e){if(typeof e=="function")return ma(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Os)return 11;if(e===bs)return 14}return 2}function jt(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Po(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")ma(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case fn:return Zt(n.children,o,i,t);case Is:l=8,o|=8;break;case Sl:return e=Ue(12,n,t,o|2),e.elementType=Sl,e.lanes=i,e;case El:return e=Ue(13,n,t,o),e.elementType=El,e.lanes=i,e;case wl:return e=Ue(19,n,t,o),e.elementType=wl,e.lanes=i,e;case bc:return Ei(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ic:l=10;break e;case Oc:l=9;break e;case Os:l=11;break e;case bs:l=14;break e;case wt:l=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=Ue(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Zt(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Ei(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=bc,e.lanes=n,e.stateNode={isHidden:!1},e}function ll(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function sl(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Wh(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=$i(0),this.expirationTimes=$i(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$i(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ha(e,t,n,r,o,i,l,s,a){return e=new Wh(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ue(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Js(i),e}function Qh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Lf(e){if(!e)return Vt;e=e._reactInternals;e:{if(an(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(Pe(n))return Cd(e,n,t)}return t}function Pf(e,t,n,r,o,i,l,s,a){return e=ha(n,r,!0,e,o,i,l,s,a),e.context=Lf(null),n=e.current,r=Se(),o=Dt(n),i=mt(r,o),i.callback=t??null,Ot(n,i,o),e.current.lanes=o,Vr(e,o,r),Re(e,r),e}function wi(e,t,n,r){var o=t.current,i=Se(),l=Dt(o);return n=Lf(n),t.context===null?t.context=n:t.pendingContext=n,t=mt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(o,t,l),e!==null&&(Je(e,o,l,i),So(e,o,l)),l}function Zo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ju(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ga(e,t){ju(e,t),(e=e.alternate)&&ju(e,t)}function Kh(){return null}var Rf=typeof reportError=="function"?reportError:function(e){console.error(e)};function _a(e){this._internalRoot=e}ki.prototype.render=_a.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));wi(e,t,null,null)};ki.prototype.unmount=_a.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ln(function(){wi(null,e,null,null)}),t[_t]=null}};function ki(e){this._internalRoot=e}ki.prototype.unstable_scheduleHydration=function(e){if(e){var t=od();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&ld(e)}};function ya(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ci(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Mu(){}function qh(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Zo(l);i.call(u)}}var l=Pf(t,r,e,0,null,!1,!1,"",Mu);return e._reactRootContainer=l,e[_t]=l.current,kr(e.nodeType===8?e.parentNode:e),ln(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Zo(a);s.call(u)}}var a=ha(e,0,!1,null,null,!1,!1,"",Mu);return e._reactRootContainer=a,e[_t]=a.current,kr(e.nodeType===8?e.parentNode:e),ln(function(){wi(t,a,n,r)}),a}function Li(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Zo(l);s.call(a)}}wi(t,l,e,o)}else l=qh(n,t,e,o,r);return Zo(l)}nd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=rr(t.pendingLanes);n!==0&&(Ms(t,n|1),Re(t,Z()),!(z&6)&&(Vn=Z()+500,$t()))}break;case 13:ln(function(){var r=yt(e,1);if(r!==null){var o=Se();Je(r,e,1,o)}}),ga(e,1)}};Vs=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=Se();Je(t,e,134217728,n)}ga(e,134217728)}};rd=function(e){if(e.tag===13){var t=Dt(e),n=yt(e,t);if(n!==null){var r=Se();Je(n,e,t,r)}ga(e,t)}};od=function(){return B};id=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};Ol=function(e,t,n){switch(t){case"input":if(Ll(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=gi(r);if(!o)throw Error(L(90));jc(r),Ll(r,o)}}}break;case"textarea":Vc(e,n);break;case"select":t=n.value,t!=null&&wn(e,!!n.multiple,t,!1)}};Hc=da;Wc=ln;var Yh={usingClientEntryPoint:!1,Events:[Br,gn,gi,Uc,Gc,da]},Zn={findFiberByHostInstance:Qt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Xh={bundleType:Zn.bundleType,version:Zn.version,rendererPackageName:Zn.rendererPackageName,rendererConfig:Zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=qc(e),e===null?null:e.stateNode},findFiberByHostInstance:Zn.findFiberByHostInstance||Kh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var co=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!co.isDisabled&&co.supportsFiber)try{fi=co.inject(Xh),it=co}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yh;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ya(t))throw Error(L(200));return Qh(e,t,null,n)};De.createRoot=function(e,t){if(!ya(e))throw Error(L(299));var n=!1,r="",o=Rf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ha(e,1,!1,null,null,n,!1,r,o),e[_t]=t.current,kr(e.nodeType===8?e.parentNode:e),new _a(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=qc(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return ln(e)};De.hydrate=function(e,t,n){if(!Ci(t))throw Error(L(200));return Li(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!ya(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=Rf;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Pf(t,null,e,1,n??null,o,!1,i,l),e[_t]=t.current,kr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ki(t)};De.render=function(e,t,n){if(!Ci(t))throw Error(L(200));return Li(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!Ci(e))throw Error(L(40));return e._reactRootContainer?(ln(function(){Li(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};De.unstable_batchedUpdates=da;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ci(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return Li(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function Tf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Tf)}catch(e){console.error(e)}}Tf(),Rc.exports=De;var Jh=Rc.exports,Vu=Jh;vl.createRoot=Vu.createRoot,vl.hydrateRoot=Vu.hydrateRoot;const Zh="modulepreload",eg=function(e,t){return new URL(e,t).href},zu={},p=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const l=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=eg(u,r),u in zu)return;zu[u]=!0;const d=u.endsWith(".css"),c=d?'[rel="stylesheet"]':"";if(!!r)for(let v=l.length-1;v>=0;v--){const x=l[v];if(x.href===u&&(!d||x.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${c}`))return;const _=document.createElement("link");if(_.rel=d?"stylesheet":Zh,d||(_.as="script"),_.crossOrigin="",_.href=u,a&&_.setAttribute("nonce",a),document.head.appendChild(_),d)return new Promise((v,x)=>{_.addEventListener("load",v),_.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return o.then(l=>{for(const s of l||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})},tg={},Bu=e=>{let t;const n=new Set,r=(d,c)=>{const f=typeof d=="function"?d(t):d;if(!Object.is(f,t)){const _=t;t=c??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(v=>v(t,_))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>u,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(tg?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,o,a);return a},ng=e=>e?Bu(e):Bu;var Nf={exports:{}},Af={},If={exports:{}},Of={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zn=O;function rg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var og=typeof Object.is=="function"?Object.is:rg,ig=zn.useState,lg=zn.useEffect,sg=zn.useLayoutEffect,ag=zn.useDebugValue;function ug(e,t){var n=t(),r=ig({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return sg(function(){o.value=n,o.getSnapshot=t,al(o)&&i({inst:o})},[e,n,t]),lg(function(){return al(o)&&i({inst:o}),e(function(){al(o)&&i({inst:o})})},[e]),ag(n),n}function al(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!og(e,n)}catch{return!0}}function cg(e,t){return t()}var dg=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?cg:ug;Of.useSyncExternalStore=zn.useSyncExternalStore!==void 0?zn.useSyncExternalStore:dg;If.exports=Of;var fg=If.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pi=O,pg=fg;function mg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var hg=typeof Object.is=="function"?Object.is:mg,gg=pg.useSyncExternalStore,_g=Pi.useRef,yg=Pi.useEffect,vg=Pi.useMemo,xg=Pi.useDebugValue;Af.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=_g(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=vg(function(){function a(_){if(!u){if(u=!0,d=_,_=r(_),o!==void 0&&l.hasValue){var v=l.value;if(o(v,_))return c=v}return c=_}if(v=c,hg(d,_))return v;var x=r(_);return o!==void 0&&o(v,x)?(d=_,v):(d=_,c=x)}var u=!1,d,c,f=n===void 0?null:n;return[function(){return a(t())},f===null?void 0:function(){return a(f())}]},[t,n,r,o]);var s=gg(e,i[0],i[1]);return yg(function(){l.hasValue=!0,l.value=s},[s]),xg(s),s};Nf.exports=Af;var Sg=Nf.exports;const Eg=gc(Sg),bf={},{useDebugValue:wg}=Rs,{useSyncExternalStoreWithSelector:kg}=Eg;let $u=!1;const Cg=e=>e;function Lg(e,t=Cg,n){(bf?"production":void 0)!=="production"&&n&&!$u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),$u=!0);const r=kg(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return wg(r),r}const Fu=e=>{(bf?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?ng(e):e,n=(r,o)=>Lg(t,r,o);return Object.assign(n,t),n},Ri=e=>e?Fu(e):Fu;let ul=null;const Tn=()=>typeof window<"u"&&!!window.CSInterface,va=()=>{if(!ul&&Tn())try{ul=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(e){console.error("Failed to initialize CSInterface:",e)}return ul},Df=()=>{if(!Tn()){console.warn("Not running in CEP environment");return}const e=va();if(!e)return;e.addEventListener("com.adobe.csxs.events.ThemeColorChanged",n=>{console.log("Theme changed:",n)});const t=e.getHostEnvironment();console.log("Host environment:",t),e.evalScript("SahAI.getAppInfo()",n=>{try{if(!n||n.trim()===""){console.warn("Empty response from ExtendScript");return}const r=JSON.parse(n);console.log("ExtendScript response:",r)}catch(r){console.error("Failed to parse ExtendScript response:",r,"Raw result:",n)}})},ft=(e,t=3e4)=>new Promise((n,r)=>{const o=va();if(!o){r(new Error("CSInterface not available - not running in CEP environment"));return}const i=setTimeout(()=>{r(new Error(`ExtendScript execution timed out after ${t}ms`))},t);try{o.evalScript(e,l=>{clearTimeout(i);try{if(typeof l=="string"&&l.startsWith("EvalScript error")){r(new Error(`ExtendScript Error: ${l}`));return}if(!l||l.trim()===""){r(new Error("Empty response from ExtendScript"));return}let s;try{s=JSON.parse(l)}catch{s={success:!0,data:l}}typeof s=="object"&&s!==null?s.success===!1?r(new Error(s.message||"ExtendScript execution failed")):n(s):n({success:!0,data:s})}catch(s){r(new Error(`Failed to process ExtendScript response: ${s}`))}})}catch(l){clearTimeout(i),r(new Error(`Failed to execute ExtendScript: ${l}`))}});class en{static async save(t){const n=JSON.stringify(t);try{if(Tn())try{const r=await ft(`saveSettings(${JSON.stringify(t)})`,1e4);if(r.success)console.log("Settings saved to CEP storage successfully");else throw new Error(r.message||"CEP save failed")}catch(r){console.warn("CEP storage save failed, falling back to localStorage:",r)}localStorage.setItem(this.SETTINGS_KEY,n),console.log("Settings saved to localStorage successfully")}catch(r){console.error("All settings save methods failed:",r);try{localStorage.setItem(this.SETTINGS_KEY,n)}catch(o){throw new Error(`Failed to save settings: ${r}. LocalStorage also failed: ${o}`)}}}static async load(){try{if(Tn())try{const n=await ft("loadSettings()",1e4);if(n.success&&n.data)return console.log("Settings loaded from CEP storage successfully"),n.data}catch(n){console.warn("CEP storage load failed, falling back to localStorage:",n)}const t=localStorage.getItem(this.SETTINGS_KEY);if(t){const n=JSON.parse(t);return console.log("Settings loaded from localStorage successfully"),n}return console.log("No existing settings found, returning defaults"),{providers:[]}}catch(t){return console.error("All settings load methods failed:",t),{providers:[]}}}static async exportSettings(){const t=await this.load();return JSON.stringify(t,null,2)}static async importSettings(t){try{const n=JSON.parse(t);await this.save(n)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(Tn())try{await ft("saveSettings({})",1e4)}catch(t){console.warn("Failed to clear CEP storage:",t)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(t){throw new Error(`Failed to clear settings: ${t}`)}}}w(en,"SETTINGS_KEY","sahAI_settings");class jf{static async checkProviderStatus(t,n){const r=Date.now();try{const{ProviderBridge:o}=await p(async()=>{const{ProviderBridge:l}=await Promise.resolve().then(()=>Mf);return{ProviderBridge:l}},void 0,import.meta.url);return{isOnline:(await o.listModels(t,n.baseURL,n.apiKey)).length>0,latency:Date.now()-r}}catch(o){return{isOnline:!1,error:o.message||String(o),latency:Date.now()-r}}}}const Pg={async listModels(e,t,n){try{const r=`listModels('${e}', '${t||""}', '${n||""}')`,o=await ft(r,15e3);if(o&&typeof o=="object"){if(o.success&&o.data){const i=typeof o.data=="string"?JSON.parse(o.data):o.data;return i.ok?i.models:this.getFallbackModels(e)}if(o.ok)return o.models||[]}return console.warn(`Failed to fetch models for ${e}, using fallback`),this.getFallbackModels(e)}catch(r){return console.error(`Error fetching models for ${e}:`,r),this.getFallbackModels(e)}},getFallbackModels(e){return{openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:128e3,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:2e5,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:2e5}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6}],groq:[{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072},{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:32768}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:128e3,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",description:"Most capable",contextLength:128e3,isRecommended:!0},{id:"mistral-medium-latest",name:"Mistral Medium",description:"Balanced",contextLength:32e3},{id:"mistral-small-latest",name:"Mistral Small",description:"Fast and efficient",contextLength:32e3}],ollama:[{id:"llama2",name:"Llama 2",description:"Open source LLM",contextLength:4096},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:8192},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384}],lmstudio:[{id:"local-model",name:"Local Model",description:"Your local model",contextLength:4096}]}[e]||[]}},Mf=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:en,ProviderBridge:Pg,ProviderStatusChecker:jf,executeExtendScript:ft,getCSInterface:va,initializeCEP:Df,isCEPEnvironment:Tn},Symbol.toStringTag,{value:"Module"})),Ti=Ri((e,t)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",isConfigured:!1,models:[]},{id:"groq",name:"Groq",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",isConfigured:!1,models:[]},{id:"mistral",name:"Mistral",isConfigured:!1,models:[]},{id:"moonshot",name:"Moonshot AI",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",isConfigured:!1,models:[]},{id:"perplexity",name:"Perplexity",isConfigured:!1,models:[]},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1,models:[]},{id:"together",name:"Together AI",isConfigured:!1,models:[]},{id:"vertex",name:"Google Vertex AI",isConfigured:!1,models:[]},{id:"xai",name:"xAI",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",isConfigured:!1,models:[]},{id:"lmstudio",name:"LM Studio",isConfigured:!1,models:[]}],activeProviderId:void 0,isLoadingModels:!1,setActiveProvider:n=>{e({activeProviderId:n}),t().persistSettings()},updateProviderConfig:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,...r,isConfigured:!!r.apiKey}:i)})),t().persistSettings()},setProviderModels:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,models:r,isLoading:!1,error:void 0}:i)}))},setSelectedModel:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,selectedModelId:r}:i)})),t().persistSettings()},updateProviderKey:(n,r,o)=>{e(i=>({providers:i.providers.map(l=>l.id===n?{...l,apiKey:r,isConfigured:!!r,selectedModelId:o||l.selectedModelId}:l)})),t().persistSettings()},saveProviderSelection:(n,r)=>{e(o=>({activeProviderId:n,providers:o.providers.map(i=>i.id===n?{...i,...r,isConfigured:!!(r.apiKey||i.baseURL)}:i)})),t().persistSettings()},loadModelsForProvider:async n=>{const r=t().providers.find(o=>o.id===n);if(r!=null&&r.isConfigured){e(o=>({providers:o.providers.map(i=>i.id===n?{...i,isLoading:!0,error:void 0}:i)}));try{const{ProviderBridge:o}=await p(async()=>{const{ProviderBridge:s}=await Promise.resolve().then(()=>Mf);return{ProviderBridge:s}},void 0,import.meta.url),l=(await o.listModels(n,r.baseURL,r.apiKey)).map(s=>({id:s.id,name:s.name,description:s.description,contextLength:s.contextLength,isRecommended:s.isRecommended}));t().setProviderModels(n,l)}catch(o){e(i=>({providers:i.providers.map(l=>l.id===n?{...l,isLoading:!1,error:o.message||String(o)}:l)}))}}},persistSettings:()=>{const{activeProviderId:n,providers:r}=t();en.save({activeProviderId:n,providers:r.map(o=>({id:o.id,isConfigured:o.isConfigured,apiKey:o.apiKey,baseURL:o.baseURL,selectedModelId:o.selectedModelId,settings:o.settings}))})},loadSettings:async()=>{try{const n=await en.load();n.activeProviderId&&e({activeProviderId:n.activeProviderId}),n.providers&&Array.isArray(n.providers)&&e(r=>({providers:r.providers.map(o=>{var l;const i=(l=n.providers)==null?void 0:l.find(s=>s.id===o.id);return i?{...o,...i}:o})}))}catch(n){console.error("Failed to load CEP settings:",n)}},getActiveProvider:()=>{const{providers:n,activeProviderId:r}=t();return n.find(o=>o.id===r)||null},getActiveModel:()=>{const n=t().getActiveProvider();return n!=null&&n.selectedModelId&&n.models.find(r=>r.id===n.selectedModelId)||null}})),Fr=Ri(e=>({modal:null,openModal:t=>e({modal:t}),closeModal:()=>e({modal:null})})),Ni=Ri(e=>({messages:[],isLoading:!1,addMessage:t=>e(n=>({messages:[...n.messages,{...t,id:crypto.randomUUID(),timestamp:Date.now()}]})),setLoading:t=>e({isLoading:t}),createNewSession:()=>e({messages:[],currentSession:crypto.randomUUID()})}));/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Vf=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Tg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ng=O.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>O.createElement("svg",{ref:a,...Tg,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Vf("lucide",o),...s},[...l.map(([u,d])=>O.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=(e,t)=>{const n=O.forwardRef(({className:r,...o},i)=>O.createElement(Ng,{ref:i,iconNode:t,className:Vf(`lucide-${Rg(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=Y("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uu=Y("BarChart2",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ig=Y("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xa=Y("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Og=Y("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bg=Y("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=Y("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jg=Y("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mg=Y("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gu=Y("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ei=Y("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hu=Y("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vg=Y("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zg=Y("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bg=Y("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wu=Y("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=Y("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $g=Y("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fg=Y("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qu=Y("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ug=Y("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gg=Y("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=Y("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Hg=()=>{const{getActiveProvider:e}=Ti(),[t,n]=O.useState({isOnline:null,isChecking:!1}),r=e();O.useEffect(()=>{let s;const a=async()=>{if(!(r!=null&&r.isConfigured)){n({isOnline:null,isChecking:!1});return}n(u=>({...u,isChecking:!0,error:void 0}));try{const u=await jf.checkProviderStatus(r.id,{apiKey:r.apiKey,baseURL:r.baseURL});n({isOnline:u.isOnline,latency:u.latency,isChecking:!1})}catch(u){n({isOnline:!1,isChecking:!1,error:u.message})}};return r!=null&&r.isConfigured&&(a(),s=setInterval(a,3e4)),()=>{s&&clearInterval(s)}},[r]);const o=()=>t.isChecking?m.jsx(ei,{size:12,className:"animate-spin"}):t.isOnline===!0?m.jsx(Gg,{size:12}):t.isOnline===!1?m.jsx(Ug,{size:12}):m.jsx(Og,{size:12}),i=()=>t.isChecking?"text-yellow-500":t.isOnline===!0?"text-green-500":"text-red-500",l=()=>r?t.isChecking?"Checking connection…":t.isOnline===!0?`${r.name} online${t.latency?` (${t.latency} ms)`:""}`:t.error?`${r.name} error: ${t.error}`:`${r.name} offline`:"No provider selected";return m.jsxs("div",{className:`flex items-center space-x-1.5 ${i()}`,title:l(),children:[o(),m.jsx("span",{className:"text-xs font-medium hidden sm:inline",children:t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown"})]})},Wg=()=>{const{getActiveProvider:e,getActiveModel:t,loadSettings:n}=Ti(),{openModal:r}=Fr(),{createNewSession:o}=Ni(),i=e(),l=t(),s=O.useMemo(()=>i?l?`${i.name} • ${l.name}`:`${i.name} • Select Model`:"Select AI Provider & Model",[i,l]);return O.useEffect(()=>{n()},[n]),m.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[m.jsxs("div",{className:"flex items-center space-x-3",children:[m.jsx(Hg,{}),m.jsx("div",{className:"h-4 w-px bg-adobe-border"}),m.jsxs("button",{onClick:()=>r("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[m.jsx("span",{className:"max-w-[300px] truncate",children:s}),m.jsx(xa,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),m.jsxs("div",{className:"flex items-center space-x-2",children:[m.jsx("button",{onClick:()=>r("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:m.jsx(Mg,{size:16})}),m.jsx("button",{onClick:()=>r("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:m.jsx(Fg,{size:16})}),m.jsx("div",{className:"h-4 w-px bg-adobe-border"}),m.jsx("button",{onClick:o,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:m.jsx(Bg,{size:16})})]})]})},zf=[{id:"abap",name:"ABAP",import:()=>p(()=>import("./assets/abap-DsBKuouk.js"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>p(()=>import("./assets/actionscript-3-D_z4Izcz.js"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>p(()=>import("./assets/ada-727ZlQH0.js"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>p(()=>import("./assets/angular-html-LfdN0zeE.js").then(e=>e.f),__vite__mapDeps([0,1,2,3]),import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>p(()=>import("./assets/angular-ts-CKsD7JZE.js"),__vite__mapDeps([4,0,1,2,3,5]),import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>p(()=>import("./assets/apache-Dn00JSTd.js"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>p(()=>import("./assets/apex-COJ4H7py.js"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>p(()=>import("./assets/apl-BBq3IX1j.js"),__vite__mapDeps([6,1,2,3,7,8,9]),import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>p(()=>import("./assets/applescript-Bu5BbsvL.js"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>p(()=>import("./assets/ara-7O62HKoU.js"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>p(()=>import("./assets/asciidoc-BPT9niGB.js"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>p(()=>import("./assets/asm-Dhn9LcZ4.js"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>p(()=>import("./assets/astro-CqkE3fuf.js"),__vite__mapDeps([10,9,2,11,3,12]),import.meta.url)},{id:"awk",name:"AWK",import:()=>p(()=>import("./assets/awk-eg146-Ew.js"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>p(()=>import("./assets/ballerina-Du268qiB.js"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>p(()=>import("./assets/bat-fje9CFhw.js"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>p(()=>import("./assets/beancount-BwXTMy5W.js"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>p(()=>import("./assets/berry-3xVqZejG.js"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>p(()=>import("./assets/bibtex-xW4inM5L.js"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>p(()=>import("./assets/bicep-DHo0CJ0O.js"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>p(()=>import("./assets/blade-a8OxSdnT.js"),__vite__mapDeps([13,1,2,3,7,8,14,9]),import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>p(()=>import("./assets/bsl-Dgyn0ogV.js"),__vite__mapDeps([15,16]),import.meta.url)},{id:"c",name:"C",import:()=>p(()=>import("./assets/c-C3t2pwGQ.js"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>p(()=>import("./assets/cadence-DNquZEk8.js"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>p(()=>import("./assets/cairo--RitsXJZ.js"),__vite__mapDeps([17,18]),import.meta.url)},{id:"clarity",name:"Clarity",import:()=>p(()=>import("./assets/clarity-BHOwM8T6.js"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>p(()=>import("./assets/clojure-DxSadP1t.js"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>p(()=>import("./assets/cmake-DbXoA79R.js"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>p(()=>import("./assets/cobol-PTqiYgYu.js"),__vite__mapDeps([19,1,2,3,8]),import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>p(()=>import("./assets/codeowners-Bp6g37R7.js"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>p(()=>import("./assets/codeql-sacFqUAJ.js"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>p(()=>import("./assets/coffee-dyiR41kL.js"),__vite__mapDeps([20,2]),import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>p(()=>import("./assets/common-lisp-C7gG9l05.js"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>p(()=>import("./assets/coq-Dsg_Bt_b.js"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>p(()=>import("./assets/cpp-BksuvNSY.js"),__vite__mapDeps([21,22,23,24,14]),import.meta.url)},{id:"crystal",name:"Crystal",import:()=>p(()=>import("./assets/crystal-DtDmRg-F.js"),__vite__mapDeps([25,1,2,3,14,24,26]),import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>p(()=>import("./assets/csharp-D9R-vmeu.js"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>p(()=>import("./assets/css-BPhBrDlE.js"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>p(()=>import("./assets/csv-B0qRVHPH.js"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>p(()=>import("./assets/cue-DtFQj3wx.js"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>p(()=>import("./assets/cypher-m2LEI-9-.js"),[],import.meta.url)},{id:"d",name:"D",import:()=>p(()=>import("./assets/d-BoXegm-a.js"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>p(()=>import("./assets/dart-B9wLZaAG.js"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>p(()=>import("./assets/dax-ClGRhx96.js"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>p(()=>import("./assets/desktop-DEIpsLCJ.js"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>p(()=>import("./assets/diff-BgYniUM_.js"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>p(()=>import("./assets/docker-COcR7UxN.js"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>p(()=>import("./assets/dotenv-BjQB5zDj.js"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>p(()=>import("./assets/dream-maker-C-nORZOA.js"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>p(()=>import("./assets/edge-D5gP-w-T.js"),__vite__mapDeps([27,11,1,2,3,28]),import.meta.url)},{id:"elixir",name:"Elixir",import:()=>p(()=>import("./assets/elixir-CLiX3zqd.js"),__vite__mapDeps([29,1,2,3]),import.meta.url)},{id:"elm",name:"Elm",import:()=>p(()=>import("./assets/elm-CmHSxxaM.js"),__vite__mapDeps([30,23,24]),import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>p(()=>import("./assets/emacs-lisp-BX77sIaO.js"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>p(()=>import("./assets/erb-BYTLMnw6.js"),__vite__mapDeps([31,1,2,3,32,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>p(()=>import("./assets/erlang-B-DoSBHF.js"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>p(()=>import("./assets/fennel-bCA53EVm.js"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>p(()=>import("./assets/fish-w-ucz2PV.js"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>p(()=>import("./assets/fluent-Dayu4EKP.js"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>p(()=>import("./assets/fortran-fixed-form-TqA4NnZg.js"),__vite__mapDeps([39,40]),import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>p(()=>import("./assets/fortran-free-form-DKXYxT9g.js"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>p(()=>import("./assets/fsharp-XplgxFYe.js"),__vite__mapDeps([41,42]),import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>p(()=>import("./assets/gdresource-BHYsBjWJ.js"),__vite__mapDeps([43,44,45]),import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>p(()=>import("./assets/gdscript-DfxzS6Rs.js"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>p(()=>import("./assets/gdshader-SKMF96pI.js"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>p(()=>import("./assets/genie-ajMbGru0.js"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>p(()=>import("./assets/gherkin--30QC5Em.js"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>p(()=>import("./assets/git-commit-i4q6IMui.js"),__vite__mapDeps([46,47]),import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>p(()=>import("./assets/git-rebase-B-v9cOL2.js"),__vite__mapDeps([48,26]),import.meta.url)},{id:"gleam",name:"Gleam",import:()=>p(()=>import("./assets/gleam-B430Bg39.js"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>p(()=>import("./assets/glimmer-js-D-cwc0-E.js"),__vite__mapDeps([49,2,11,3,1]),import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>p(()=>import("./assets/glimmer-ts-pgjy16dm.js"),__vite__mapDeps([50,11,3,2,1]),import.meta.url)},{id:"glsl",name:"GLSL",import:()=>p(()=>import("./assets/glsl-DBO2IWDn.js"),__vite__mapDeps([23,24]),import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>p(()=>import("./assets/gnuplot-CM8KxXT1.js"),[],import.meta.url)},{id:"go",name:"Go",import:()=>p(()=>import("./assets/go-B1SYOhNW.js"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>p(()=>import("./assets/graphql-cDcHW_If.js"),__vite__mapDeps([34,2,11,35,36]),import.meta.url)},{id:"groovy",name:"Groovy",import:()=>p(()=>import("./assets/groovy-DkBy-JyN.js"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>p(()=>import("./assets/hack-D1yCygmZ.js"),__vite__mapDeps([51,1,2,3,14]),import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>p(()=>import("./assets/haml-B2EZWmdv.js"),__vite__mapDeps([33,2,3]),import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>p(()=>import("./assets/handlebars-BQGss363.js"),__vite__mapDeps([52,1,2,3,38]),import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>p(()=>import("./assets/haskell-BILxekzW.js"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>p(()=>import("./assets/haxe-C5wWYbrZ.js"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>p(()=>import("./assets/hcl-HzYwdGDm.js"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>p(()=>import("./assets/hjson-T-Tgc4AT.js"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>p(()=>import("./assets/hlsl-ifBTmRxC.js"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>p(()=>import("./assets/html-C2L_23MC.js"),__vite__mapDeps([1,2,3]),import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>p(()=>import("./assets/html-derivative-CSfWNPLT.js"),__vite__mapDeps([28,1,2,3]),import.meta.url)},{id:"http",name:"HTTP",import:()=>p(()=>import("./assets/http-FRrOvY1W.js"),__vite__mapDeps([53,26,9,7,8,34,2,11,35,36]),import.meta.url)},{id:"hxml",name:"HXML",import:()=>p(()=>import("./assets/hxml-TIA70rKU.js"),__vite__mapDeps([54,55]),import.meta.url)},{id:"hy",name:"Hy",import:()=>p(()=>import("./assets/hy-BMj5Y0dO.js"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>p(()=>import("./assets/imba-bv_oIlVt.js"),__vite__mapDeps([56,11]),import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>p(()=>import("./assets/ini-BjABl1g7.js"),[],import.meta.url)},{id:"java",name:"Java",import:()=>p(()=>import("./assets/java-xI-RfyKK.js"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>p(()=>import("./assets/javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>p(()=>import("./assets/jinja-DGy0s7-h.js"),__vite__mapDeps([57,1,2,3]),import.meta.url)},{id:"jison",name:"Jison",import:()=>p(()=>import("./assets/jison-BqZprYcd.js"),__vite__mapDeps([58,2]),import.meta.url)},{id:"json",name:"JSON",import:()=>p(()=>import("./assets/json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>p(()=>import("./assets/json5-w8dY5SsB.js"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>p(()=>import("./assets/jsonc-TU54ms6u.js"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>p(()=>import("./assets/jsonl-DREVFZK8.js"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>p(()=>import("./assets/jsonnet-BfivnA6A.js"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>p(()=>import("./assets/jssm-P4WzXJd0.js"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>p(()=>import("./assets/jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>p(()=>import("./assets/julia-BBuGR-5E.js"),__vite__mapDeps([59,21,22,23,24,14,18,2,60]),import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>p(()=>import("./assets/kotlin-B5lbUyaz.js"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>p(()=>import("./assets/kusto-mebxcVVE.js"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>p(()=>import("./assets/latex-C-cWTeAZ.js"),__vite__mapDeps([61,62,60]),import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>p(()=>import("./assets/lean-XBlWyCtg.js"),[],import.meta.url)},{id:"less",name:"Less",import:()=>p(()=>import("./assets/less-BfCpw3nA.js"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>p(()=>import("./assets/liquid-D3W5UaiH.js"),__vite__mapDeps([63,1,2,3,9]),import.meta.url)},{id:"log",name:"Log file",import:()=>p(()=>import("./assets/log-Cc5clBb7.js"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>p(()=>import("./assets/logo-IuBKFhSY.js"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>p(()=>import("./assets/lua-CvWAzNxB.js"),__vite__mapDeps([37,24]),import.meta.url)},{id:"luau",name:"Luau",import:()=>p(()=>import("./assets/luau-Du5NY7AG.js"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>p(()=>import("./assets/make-Bvotw-X0.js"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>p(()=>import("./assets/markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>p(()=>import("./assets/marko-z0MBrx5-.js"),__vite__mapDeps([64,3,65,5,2]),import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>p(()=>import("./assets/matlab-D9-PGadD.js"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>p(()=>import("./assets/mdc-DB_EDNY_.js"),__vite__mapDeps([66,42,38,28,1,2,3]),import.meta.url)},{id:"mdx",name:"MDX",import:()=>p(()=>import("./assets/mdx-sdHcTMYB.js"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>p(()=>import("./assets/mermaid-Ci6OQyBP.js"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>p(()=>import("./assets/mipsasm-BC5c_5Pe.js"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>p(()=>import("./assets/mojo-Tz6hzZYG.js"),[],import.meta.url)},{id:"move",name:"Move",import:()=>p(()=>import("./assets/move-DB_GagMm.js"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>p(()=>import("./assets/narrat-DLbgOhZU.js"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>p(()=>import("./assets/nextflow-B0XVJmRM.js"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>p(()=>import("./assets/nginx-D_VnBJ67.js"),__vite__mapDeps([67,37,24]),import.meta.url)},{id:"nim",name:"Nim",import:()=>p(()=>import("./assets/nim-ZlGxZxc3.js"),__vite__mapDeps([68,24,1,2,3,7,8,23,42]),import.meta.url)},{id:"nix",name:"Nix",import:()=>p(()=>import("./assets/nix-shcSOmrb.js"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>p(()=>import("./assets/nushell-D4Tzg5kh.js"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>p(()=>import("./assets/objective-c-Deuh7S70.js"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>p(()=>import("./assets/objective-cpp-BUEGK8hf.js"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>p(()=>import("./assets/ocaml-BNioltXt.js"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>p(()=>import("./assets/pascal-JqZropPD.js"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>p(()=>import("./assets/perl-CHQXSrWU.js"),__vite__mapDeps([69,1,2,3,7,8,14]),import.meta.url)},{id:"php",name:"PHP",import:()=>p(()=>import("./assets/php-B5ebYQev.js"),__vite__mapDeps([70,1,2,3,7,8,14,9]),import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>p(()=>import("./assets/plsql-LKU2TuZ1.js"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>p(()=>import("./assets/po-BFLt1xDp.js"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>p(()=>import("./assets/polar-DKykz6zU.js"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>p(()=>import("./assets/postcss-B3ZDOciz.js"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>p(()=>import("./assets/powerquery-CSHBycmS.js"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>p(()=>import("./assets/powershell-BIEUsx6d.js"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>p(()=>import("./assets/prisma-B48N-Iqd.js"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>p(()=>import("./assets/prolog-BY-TUvya.js"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>p(()=>import("./assets/proto-zocC4JxJ.js"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>p(()=>import("./assets/pug-CM9l7STV.js"),__vite__mapDeps([71,2,3,1]),import.meta.url)},{id:"puppet",name:"Puppet",import:()=>p(()=>import("./assets/puppet-Cza_XSSt.js"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>p(()=>import("./assets/purescript-Bg-kzb6g.js"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>p(()=>import("./assets/python-DhUJRlN_.js"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>p(()=>import("./assets/qml-D8XfuvdV.js"),__vite__mapDeps([72,2]),import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>p(()=>import("./assets/qmldir-C8lEn-DE.js"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>p(()=>import("./assets/qss-DhMKtDLN.js"),[],import.meta.url)},{id:"r",name:"R",import:()=>p(()=>import("./assets/r-CwjWoCRV.js"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>p(()=>import("./assets/racket-CzouJOBO.js"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>p(()=>import("./assets/raku-B1bQXN8T.js"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>p(()=>import("./assets/razor-CNLDkMZG.js"),__vite__mapDeps([73,1,2,3,74]),import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>p(()=>import("./assets/reg-5LuOXUq_.js"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>p(()=>import("./assets/regexp-DWJ3fJO_.js"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>p(()=>import("./assets/rel-DJlmqQ1C.js"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>p(()=>import("./assets/riscv-QhoSD0DR.js"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>p(()=>import("./assets/rst-4NLicBqY.js"),__vite__mapDeps([75,28,1,2,3,21,22,23,24,14,18,26,38,76,32,33,7,8,34,11,35,36,37]),import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>p(()=>import("./assets/ruby-DeZ3UC14.js"),__vite__mapDeps([32,1,2,3,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>p(()=>import("./assets/rust-Be6lgOlo.js"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>p(()=>import("./assets/sas-BmTFh92c.js"),__vite__mapDeps([77,14]),import.meta.url)},{id:"sass",name:"Sass",import:()=>p(()=>import("./assets/sass-BJ4Li9vH.js"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>p(()=>import("./assets/scala-DQVVAn-B.js"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>p(()=>import("./assets/scheme-BJGe-b2p.js"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>p(()=>import("./assets/scss-C31hgJw-.js"),__vite__mapDeps([5,3]),import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>p(()=>import("./assets/sdbl-BLhTXw86.js"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>p(()=>import("./assets/shaderlab-B7qAK45m.js"),__vite__mapDeps([78,79]),import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>p(()=>import("./assets/shellscript-atvbtKCR.js"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>p(()=>import("./assets/shellsession-C_rIy8kc.js"),__vite__mapDeps([80,26]),import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>p(()=>import("./assets/smalltalk-DkLiglaE.js"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>p(()=>import("./assets/solidity-C1w2a3ep.js"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>p(()=>import("./assets/soy-C-lX7w71.js"),__vite__mapDeps([81,1,2,3]),import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>p(()=>import("./assets/sparql-bYkjHRlG.js"),__vite__mapDeps([82,83]),import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>p(()=>import("./assets/splunk-Cf8iN4DR.js"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>p(()=>import("./assets/sql-COK4E0Yg.js"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>p(()=>import("./assets/ssh-config-BknIz3MU.js"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>p(()=>import("./assets/stata-DorPZHa4.js"),__vite__mapDeps([84,14]),import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>p(()=>import("./assets/stylus-BeQkCIfX.js"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>p(()=>import("./assets/svelte-MSaWC3Je.js"),__vite__mapDeps([85,2,11,3,12]),import.meta.url)},{id:"swift",name:"Swift",import:()=>p(()=>import("./assets/swift-BSxZ-RaX.js"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>p(()=>import("./assets/system-verilog-C7L56vO4.js"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>p(()=>import("./assets/systemd-CUnW07Te.js"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>p(()=>import("./assets/talonscript-C1XDQQGZ.js"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>p(()=>import("./assets/tasl-CQjiPCtT.js"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>p(()=>import("./assets/tcl-DQ1-QYvQ.js"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>p(()=>import("./assets/templ-dwX3ZSMB.js"),__vite__mapDeps([86,87,2,3]),import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>p(()=>import("./assets/terraform-BbSNqyBO.js"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>p(()=>import("./assets/tex-rYs2v40G.js"),__vite__mapDeps([62,60]),import.meta.url)},{id:"toml",name:"TOML",import:()=>p(()=>import("./assets/toml-CB2ApiWb.js"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>p(()=>import("./assets/ts-tags-CipyTH0X.js"),__vite__mapDeps([88,11,3,2,23,24,1,14,7,8]),import.meta.url)},{id:"tsv",name:"TSV",import:()=>p(()=>import("./assets/tsv-B_m7g4N7.js"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>p(()=>import("./assets/tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>p(()=>import("./assets/turtle-BMR_PYu6.js"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>p(()=>import("./assets/twig-NC5TFiHP.js"),__vite__mapDeps([89,3,2,5,70,1,7,8,14,9,18,32,33,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>p(()=>import("./assets/typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>p(()=>import("./assets/typespec-BpWG_bgh.js"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>p(()=>import("./assets/typst-BVUVsWT6.js"),[],import.meta.url)},{id:"v",name:"V",import:()=>p(()=>import("./assets/v-CAQ2eGtk.js"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>p(()=>import("./assets/vala-BFOHcciG.js"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>p(()=>import("./assets/vb-CdO5JTpU.js"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>p(()=>import("./assets/verilog-CJaU5se_.js"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>p(()=>import("./assets/vhdl-DYoNaHQp.js"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>p(()=>import("./assets/viml-m4uW47V2.js"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>p(()=>import("./assets/vue-BuYVFjOK.js"),__vite__mapDeps([90,1,2,3,11,9,28]),import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>p(()=>import("./assets/vue-html-xdeiXROB.js"),__vite__mapDeps([91,90,1,2,3,11,9,28]),import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>p(()=>import("./assets/vyper-nyqBNV6O.js"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>p(()=>import("./assets/wasm-C6j12Q_x.js"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>p(()=>import("./assets/wenyan-7A4Fjokl.js"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>p(()=>import("./assets/wgsl-CB0Krxn9.js"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>p(()=>import("./assets/wikitext-DCE3LsBG.js"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>p(()=>import("./assets/wolfram-C3FkfJm5.js"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>p(()=>import("./assets/xml-e3z08dGr.js"),__vite__mapDeps([7,8]),import.meta.url)},{id:"xsl",name:"XSL",import:()=>p(()=>import("./assets/xsl-Dd0NUgwM.js"),__vite__mapDeps([92,7,8]),import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>p(()=>import("./assets/yaml-CVw76BM1.js"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>p(()=>import("./assets/zenscript-HnGAYVZD.js"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>p(()=>import("./assets/zig-BVz_zdnA.js"),[],import.meta.url)}],Qg=Object.fromEntries(zf.map(e=>[e.id,e.import])),Kg=Object.fromEntries(zf.flatMap(e=>{var t;return((t=e.aliases)==null?void 0:t.map(n=>[n,e.import]))||[]})),qg={...Qg,...Kg},Yg=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>p(()=>import("./assets/andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>p(()=>import("./assets/aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>p(()=>import("./assets/ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>p(()=>import("./assets/catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>p(()=>import("./assets/catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>p(()=>import("./assets/catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>p(()=>import("./assets/catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>p(()=>import("./assets/dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>p(()=>import("./assets/dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>p(()=>import("./assets/dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>p(()=>import("./assets/everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>p(()=>import("./assets/everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>p(()=>import("./assets/github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>p(()=>import("./assets/github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>p(()=>import("./assets/github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>p(()=>import("./assets/github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>p(()=>import("./assets/github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>p(()=>import("./assets/github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>p(()=>import("./assets/github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>p(()=>import("./assets/houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>p(()=>import("./assets/kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>p(()=>import("./assets/kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>p(()=>import("./assets/kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>p(()=>import("./assets/laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>p(()=>import("./assets/light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>p(()=>import("./assets/material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>p(()=>import("./assets/material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>p(()=>import("./assets/material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>p(()=>import("./assets/material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>p(()=>import("./assets/material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>p(()=>import("./assets/min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>p(()=>import("./assets/min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>p(()=>import("./assets/monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>p(()=>import("./assets/night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>p(()=>import("./assets/nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>p(()=>import("./assets/one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>p(()=>import("./assets/one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>p(()=>import("./assets/plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>p(()=>import("./assets/poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>p(()=>import("./assets/red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>p(()=>import("./assets/rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>p(()=>import("./assets/rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>p(()=>import("./assets/rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>p(()=>import("./assets/slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>p(()=>import("./assets/slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>p(()=>import("./assets/snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>p(()=>import("./assets/solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>p(()=>import("./assets/solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>p(()=>import("./assets/synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>p(()=>import("./assets/tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>p(()=>import("./assets/vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>p(()=>import("./assets/vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>p(()=>import("./assets/vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>p(()=>import("./assets/vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],Xg=Object.fromEntries(Yg.map(e=>[e.id,e.import]));let ht=class extends Error{constructor(t){super(t),this.name="ShikiError"}},Sa=class extends Error{constructor(t){super(t),this.name="ShikiError"}};function Jg(){return 2147483648}function Zg(){return typeof performance<"u"?performance.now():Date.now()}const e0=(e,t)=>e+(t-e%t)%t;async function t0(e){let t,n;const r={};function o(_){n=_,r.HEAPU8=new Uint8Array(_),r.HEAPU32=new Uint32Array(_)}function i(_,v,x){r.HEAPU8.copyWithin(_,v,v+x)}function l(_){try{return t.grow(_-n.byteLength+65535>>>16),o(t.buffer),1}catch{}}function s(_){const v=r.HEAPU8.length;_=_>>>0;const x=Jg();if(_>x)return!1;for(let k=1;k<=4;k*=2){let g=v*(1+.2/k);g=Math.min(g,_+100663296);const h=Math.min(x,e0(Math.max(_,g),65536));if(l(h))return!0}return!1}const a=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function u(_,v,x=1024){const k=v+x;let g=v;for(;_[g]&&!(g>=k);)++g;if(g-v>16&&_.buffer&&a)return a.decode(_.subarray(v,g));let h="";for(;v<g;){let y=_[v++];if(!(y&128)){h+=String.fromCharCode(y);continue}const E=_[v++]&63;if((y&224)===192){h+=String.fromCharCode((y&31)<<6|E);continue}const S=_[v++]&63;if((y&240)===224?y=(y&15)<<12|E<<6|S:y=(y&7)<<18|E<<12|S<<6|_[v++]&63,y<65536)h+=String.fromCharCode(y);else{const C=y-65536;h+=String.fromCharCode(55296|C>>10,56320|C&1023)}}return h}function d(_,v){return _?u(r.HEAPU8,_,v):""}const c={emscripten_get_now:Zg,emscripten_memcpy_big:i,emscripten_resize_heap:s,fd_write:()=>0};async function f(){const v=await e({env:c,wasi_snapshot_preview1:c});t=v.memory,o(t.buffer),Object.assign(r,v),r.UTF8ToString=d}return await f(),r}var n0=Object.defineProperty,r0=(e,t,n)=>t in e?n0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,oe=(e,t,n)=>(r0(e,typeof t!="symbol"?t+"":t,n),n);let ae=null;function o0(e){throw new Sa(e.UTF8ToString(e.getLastOnigError()))}class Ai{constructor(t){oe(this,"utf16Length"),oe(this,"utf8Length"),oe(this,"utf16Value"),oe(this,"utf8Value"),oe(this,"utf16OffsetToUtf8"),oe(this,"utf8OffsetToUtf16");const n=t.length,r=Ai._utf8ByteLength(t),o=r!==n,i=o?new Uint32Array(n+1):null;o&&(i[n]=r);const l=o?new Uint32Array(r+1):null;o&&(l[r]=n);const s=new Uint8Array(r);let a=0;for(let u=0;u<n;u++){const d=t.charCodeAt(u);let c=d,f=!1;if(d>=55296&&d<=56319&&u+1<n){const _=t.charCodeAt(u+1);_>=56320&&_<=57343&&(c=(d-55296<<10)+65536|_-56320,f=!0)}o&&(i[u]=a,f&&(i[u+1]=a),c<=127?l[a+0]=u:c<=2047?(l[a+0]=u,l[a+1]=u):c<=65535?(l[a+0]=u,l[a+1]=u,l[a+2]=u):(l[a+0]=u,l[a+1]=u,l[a+2]=u,l[a+3]=u)),c<=127?s[a++]=c:c<=2047?(s[a++]=192|(c&1984)>>>6,s[a++]=128|(c&63)>>>0):c<=65535?(s[a++]=224|(c&61440)>>>12,s[a++]=128|(c&4032)>>>6,s[a++]=128|(c&63)>>>0):(s[a++]=240|(c&1835008)>>>18,s[a++]=128|(c&258048)>>>12,s[a++]=128|(c&4032)>>>6,s[a++]=128|(c&63)>>>0),f&&u++}this.utf16Length=n,this.utf8Length=r,this.utf16Value=t,this.utf8Value=s,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=l}static _utf8ByteLength(t){let n=0;for(let r=0,o=t.length;r<o;r++){const i=t.charCodeAt(r);let l=i,s=!1;if(i>=55296&&i<=56319&&r+1<o){const a=t.charCodeAt(r+1);a>=56320&&a<=57343&&(l=(i-55296<<10)+65536|a-56320,s=!0)}l<=127?n+=1:l<=2047?n+=2:l<=65535?n+=3:n+=4,s&&r++}return n}createString(t){const n=t.omalloc(this.utf8Length);return t.HEAPU8.set(this.utf8Value,n),n}}const tt=class{constructor(e){if(oe(this,"id",++tt.LAST_ID),oe(this,"_onigBinding"),oe(this,"content"),oe(this,"utf16Length"),oe(this,"utf8Length"),oe(this,"utf16OffsetToUtf8"),oe(this,"utf8OffsetToUtf16"),oe(this,"ptr"),!ae)throw new Sa("Must invoke loadWasm first.");this._onigBinding=ae,this.content=e;const t=new Ai(e);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!tt._sharedPtrInUse?(tt._sharedPtr||(tt._sharedPtr=ae.omalloc(1e4)),tt._sharedPtrInUse=!0,ae.HEAPU8.set(t.utf8Value,tt._sharedPtr),this.ptr=tt._sharedPtr):this.ptr=t.createString(ae)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===tt._sharedPtr?tt._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let Ur=tt;oe(Ur,"LAST_ID",0);oe(Ur,"_sharedPtr",0);oe(Ur,"_sharedPtrInUse",!1);class i0{constructor(t){if(oe(this,"_onigBinding"),oe(this,"_ptr"),!ae)throw new Sa("Must invoke loadWasm first.");const n=[],r=[];for(let s=0,a=t.length;s<a;s++){const u=new Ai(t[s]);n[s]=u.createString(ae),r[s]=u.utf8Length}const o=ae.omalloc(4*t.length);ae.HEAPU32.set(n,o/4);const i=ae.omalloc(4*t.length);ae.HEAPU32.set(r,i/4);const l=ae.createOnigScanner(o,i,t.length);for(let s=0,a=t.length;s<a;s++)ae.ofree(n[s]);ae.ofree(i),ae.ofree(o),l===0&&o0(ae),this._onigBinding=ae,this._ptr=l}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(t,n,r){let o=0;if(typeof r=="number"&&(o=r),typeof t=="string"){t=new Ur(t);const i=this._findNextMatchSync(t,n,!1,o);return t.dispose(),i}return this._findNextMatchSync(t,n,!1,o)}_findNextMatchSync(t,n,r,o){const i=this._onigBinding,l=i.findNextOnigScannerMatch(this._ptr,t.id,t.ptr,t.utf8Length,t.convertUtf16OffsetToUtf8(n),o);if(l===0)return null;const s=i.HEAPU32;let a=l/4;const u=s[a++],d=s[a++],c=[];for(let f=0;f<d;f++){const _=t.convertUtf8OffsetToUtf16(s[a++]),v=t.convertUtf8OffsetToUtf16(s[a++]);c[f]={start:_,end:v,length:v-_}}return{index:u,captureIndices:c}}}function l0(e){return typeof e.instantiator=="function"}function s0(e){return typeof e.default=="function"}function a0(e){return typeof e.data<"u"}function u0(e){return typeof Response<"u"&&e instanceof Response}function c0(e){var t;return typeof ArrayBuffer<"u"&&(e instanceof ArrayBuffer||ArrayBuffer.isView(e))||typeof Buffer<"u"&&((t=Buffer.isBuffer)==null?void 0:t.call(Buffer,e))||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&e instanceof Uint32Array}let fo;function d0(e){if(fo)return fo;async function t(){ae=await t0(async n=>{let r=e;return r=await r,typeof r=="function"&&(r=await r(n)),typeof r=="function"&&(r=await r(n)),l0(r)?r=await r.instantiator(n):s0(r)?r=await r.default(n):(a0(r)&&(r=r.data),u0(r)?typeof WebAssembly.instantiateStreaming=="function"?r=await f0(r)(n):r=await p0(r)(n):c0(r)?r=await cl(r)(n):r instanceof WebAssembly.Module?r=await cl(r)(n):"default"in r&&r.default instanceof WebAssembly.Module&&(r=await cl(r.default)(n))),"instance"in r&&(r=r.instance),"exports"in r&&(r=r.exports),r})}return fo=t(),fo}function cl(e){return t=>WebAssembly.instantiate(e,t)}function f0(e){return t=>WebAssembly.instantiateStreaming(e,t)}function p0(e){return async t=>{const n=await e.arrayBuffer();return WebAssembly.instantiate(n,t)}}let m0;function h0(){return m0}async function Bf(e){return e&&await d0(e),{createScanner(t){return new i0(t.map(n=>typeof n=="string"?n:n.source))},createString(t){return new Ur(t)}}}function g0(e){return Ea(e)}function Ea(e){return Array.isArray(e)?_0(e):e instanceof RegExp?e:typeof e=="object"?y0(e):e}function _0(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=Ea(e[n]);return t}function y0(e){let t={};for(let n in e)t[n]=Ea(e[n]);return t}function $f(e,...t){return t.forEach(n=>{for(let r in n)e[r]=n[r]}),e}function Ff(e){const t=~e.lastIndexOf("/")||~e.lastIndexOf("\\");return t===0?e:~t===e.length-1?Ff(e.substring(0,e.length-1)):e.substr(~t+1)}var dl=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,po=class{static hasCaptures(e){return e===null?!1:(dl.lastIndex=0,dl.test(e))}static replaceCaptures(e,t,n){return e.replace(dl,(r,o,i,l)=>{let s=n[parseInt(o||i,10)];if(s){let a=t.substring(s.start,s.end);for(;a[0]===".";)a=a.substring(1);switch(l){case"downcase":return a.toLowerCase();case"upcase":return a.toUpperCase();default:return a}}else return r})}};function Uf(e,t){return e<t?-1:e>t?1:0}function Gf(e,t){if(e===null&&t===null)return 0;if(!e)return-1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let o=0;o<n;o++){let i=Uf(e[o],t[o]);if(i!==0)return i}return 0}return n-r}function Ku(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function Hf(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Wf=class{constructor(e){w(this,"cache",new Map);this.fn=e}get(e){if(this.cache.has(e))return this.cache.get(e);const t=this.fn(e);return this.cache.set(e,t),t}},ti=class{constructor(e,t,n){w(this,"_cachedMatchRoot",new Wf(e=>this._root.match(e)));this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(S0(e),t)}static createFromParsedTheme(e,t){return w0(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(e===null)return this._defaults;const t=e.scopeName,r=this._cachedMatchRoot.get(t).find(o=>v0(e.parent,o.parentScopes));return r?new Qf(r.fontStyle,r.foreground,r.background):null}},fl=class Ro{constructor(t,n){this.parent=t,this.scopeName=n}static push(t,n){for(const r of n)t=new Ro(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new Ro(n,t[r]);return n}push(t){return new Ro(this,t)}getSegments(){let t=this;const n=[];for(;t;)n.push(t.scopeName),t=t.parent;return n.reverse(),n}toString(){return this.getSegments().join(" ")}extends(t){return this===t?!0:this.parent===null?!1:this.parent.extends(t)}getExtensionIfDefined(t){const n=[];let r=this;for(;r&&r!==t;)n.push(r.scopeName),r=r.parent;return r===t?n.reverse():void 0}};function v0(e,t){if(t.length===0)return!0;for(let n=0;n<t.length;n++){let r=t[n],o=!1;if(r===">"){if(n===t.length-1)return!1;r=t[++n],o=!0}for(;e&&!x0(e.scopeName,r);){if(o)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0}function x0(e,t){return t===e||e.startsWith(t)&&e[t.length]==="."}var Qf=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}};function S0(e){if(!e)return[];if(!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let o=0,i=t.length;o<i;o++){let l=t[o];if(!l.settings)continue;let s;if(typeof l.scope=="string"){let c=l.scope;c=c.replace(/^[,]+/,""),c=c.replace(/[,]+$/,""),s=c.split(",")}else Array.isArray(l.scope)?s=l.scope:s=[""];let a=-1;if(typeof l.settings.fontStyle=="string"){a=0;let c=l.settings.fontStyle.split(" ");for(let f=0,_=c.length;f<_;f++)switch(c[f]){case"italic":a=a|1;break;case"bold":a=a|2;break;case"underline":a=a|4;break;case"strikethrough":a=a|8;break}}let u=null;typeof l.settings.foreground=="string"&&Ku(l.settings.foreground)&&(u=l.settings.foreground);let d=null;typeof l.settings.background=="string"&&Ku(l.settings.background)&&(d=l.settings.background);for(let c=0,f=s.length;c<f;c++){let v=s[c].trim().split(" "),x=v[v.length-1],k=null;v.length>1&&(k=v.slice(0,v.length-1),k.reverse()),n[r++]=new E0(x,k,o,a,u,d)}}return n}var E0=class{constructor(e,t,n,r,o,i){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=o,this.background=i}},pt=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(pt||{});function w0(e,t){e.sort((a,u)=>{let d=Uf(a.scope,u.scope);return d!==0||(d=Gf(a.parentScopes,u.parentScopes),d!==0)?d:a.index-u.index});let n=0,r="#000000",o="#ffffff";for(;e.length>=1&&e[0].scope==="";){let a=e.shift();a.fontStyle!==-1&&(n=a.fontStyle),a.foreground!==null&&(r=a.foreground),a.background!==null&&(o=a.background)}let i=new k0(t),l=new Qf(n,i.getId(r),i.getId(o)),s=new L0(new ms(0,null,-1,0,0),[]);for(let a=0,u=e.length;a<u;a++){let d=e[a];s.insert(0,d.scope,d.parentScopes,d.fontStyle,i.getId(d.foreground),i.getId(d.background))}return new ti(i,l,s)}var k0=class{constructor(e){w(this,"_isFrozen");w(this,"_lastColorId");w(this,"_id2color");w(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(e===null)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw new Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},C0=Object.freeze([]),ms=class Kf{constructor(t,n,r,o,i){w(this,"scopeDepth");w(this,"parentScopes");w(this,"fontStyle");w(this,"foreground");w(this,"background");this.scopeDepth=t,this.parentScopes=n||C0,this.fontStyle=r,this.foreground=o,this.background=i}clone(){return new Kf(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(t){let n=[];for(let r=0,o=t.length;r<o;r++)n[r]=t[r].clone();return n}acceptOverwrite(t,n,r,o){this.scopeDepth>t?console.log("how did this happen?"):this.scopeDepth=t,n!==-1&&(this.fontStyle=n),r!==0&&(this.foreground=r),o!==0&&(this.background=o)}},L0=class hs{constructor(t,n=[],r={}){w(this,"_rulesWithParentScopes");this._mainRule=t,this._children=r,this._rulesWithParentScopes=n}static _cmpBySpecificity(t,n){if(t.scopeDepth!==n.scopeDepth)return n.scopeDepth-t.scopeDepth;let r=0,o=0;for(;t.parentScopes[r]===">"&&r++,n.parentScopes[o]===">"&&o++,!(r>=t.parentScopes.length||o>=n.parentScopes.length);){const i=n.parentScopes[o].length-t.parentScopes[r].length;if(i!==0)return i;r++,o++}return n.parentScopes.length-t.parentScopes.length}match(t){if(t!==""){let r=t.indexOf("."),o,i;if(r===-1?(o=t,i=""):(o=t.substring(0,r),i=t.substring(r+1)),this._children.hasOwnProperty(o))return this._children[o].match(i)}const n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(hs._cmpBySpecificity),n}insert(t,n,r,o,i,l){if(n===""){this._doInsertHere(t,r,o,i,l);return}let s=n.indexOf("."),a,u;s===-1?(a=n,u=""):(a=n.substring(0,s),u=n.substring(s+1));let d;this._children.hasOwnProperty(a)?d=this._children[a]:(d=new hs(this._mainRule.clone(),ms.cloneArr(this._rulesWithParentScopes)),this._children[a]=d),d.insert(t+1,u,r,o,i,l)}_doInsertHere(t,n,r,o,i){if(n===null){this._mainRule.acceptOverwrite(t,r,o,i);return}for(let l=0,s=this._rulesWithParentScopes.length;l<s;l++){let a=this._rulesWithParentScopes[l];if(Gf(a.parentScopes,n)===0){a.acceptOverwrite(t,r,o,i);return}}r===-1&&(r=this._mainRule.fontStyle),o===0&&(o=this._mainRule.foreground),i===0&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new ms(t,n,r,o,i))}},Bn=class ze{static toBinaryStr(t){return t.toString(2).padStart(32,"0")}static print(t){const n=ze.getLanguageId(t),r=ze.getTokenType(t),o=ze.getFontStyle(t),i=ze.getForeground(t),l=ze.getBackground(t);console.log({languageId:n,tokenType:r,fontStyle:o,foreground:i,background:l})}static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static containsBalancedBrackets(t){return(t&1024)!==0}static getFontStyle(t){return(t&30720)>>>11}static getForeground(t){return(t&16744448)>>>15}static getBackground(t){return(t&4278190080)>>>24}static set(t,n,r,o,i,l,s){let a=ze.getLanguageId(t),u=ze.getTokenType(t),d=ze.containsBalancedBrackets(t)?1:0,c=ze.getFontStyle(t),f=ze.getForeground(t),_=ze.getBackground(t);return n!==0&&(a=n),r!==8&&(u=r),o!==null&&(d=o?1:0),i!==-1&&(c=i),l!==0&&(f=l),s!==0&&(_=s),(a<<0|u<<8|d<<10|c<<11|f<<15|_<<24)>>>0}};function ni(e,t){const n=[],r=P0(e);let o=r.next();for(;o!==null;){let a=0;if(o.length===2&&o.charAt(1)===":"){switch(o.charAt(0)){case"R":a=1;break;case"L":a=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=r.next()}let u=l();if(n.push({matcher:u,priority:a}),o!==",")break;o=r.next()}return n;function i(){if(o==="-"){o=r.next();const a=i();return u=>!!a&&!a(u)}if(o==="("){o=r.next();const a=s();return o===")"&&(o=r.next()),a}if(qu(o)){const a=[];do a.push(o),o=r.next();while(qu(o));return u=>t(a,u)}return null}function l(){const a=[];let u=i();for(;u;)a.push(u),u=i();return d=>a.every(c=>c(d))}function s(){const a=[];let u=l();for(;u&&(a.push(u),o==="|"||o===",");){do o=r.next();while(o==="|"||o===",");u=l()}return d=>a.some(c=>c(d))}}function qu(e){return!!e&&!!e.match(/[\w\.:]+/)}function P0(e){let t=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,n=t.exec(e);return{next:()=>{if(!n)return null;const r=n[0];return n=t.exec(e),r}}}function qf(e){typeof e.dispose=="function"&&e.dispose()}var Or=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},R0=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},T0=class{constructor(){w(this,"_references",[]);w(this,"_seenReferenceKeys",new Set);w(this,"visitedRule",new Set)}get references(){return this._references}add(e){const t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},N0=class{constructor(e,t){w(this,"seenFullScopeRequests",new Set);w(this,"seenPartialScopeRequests",new Set);w(this,"Q");this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new Or(this.initialScopeName)]}processQueue(){const e=this.Q;this.Q=[];const t=new T0;for(const n of e)A0(n,this.initialScopeName,this.repo,t);for(const n of t.references)if(n instanceof Or){if(this.seenFullScopeRequests.has(n.scopeName))continue;this.seenFullScopeRequests.add(n.scopeName),this.Q.push(n)}else{if(this.seenFullScopeRequests.has(n.scopeName)||this.seenPartialScopeRequests.has(n.toKey()))continue;this.seenPartialScopeRequests.add(n.toKey()),this.Q.push(n)}}};function A0(e,t,n,r){const o=n.lookup(e.scopeName);if(!o){if(e.scopeName===t)throw new Error(`No grammar provided for <${t}>`);return}const i=n.lookup(t);e instanceof Or?To({baseGrammar:i,selfGrammar:o},r):gs(e.ruleName,{baseGrammar:i,selfGrammar:o,repository:o.repository},r);const l=n.injections(e.scopeName);if(l)for(const s of l)r.add(new Or(s))}function gs(e,t,n){if(t.repository&&t.repository[e]){const r=t.repository[e];ri([r],t,n)}}function To(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&ri(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&ri(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function ri(e,t,n){for(const r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);const o=r.repository?$f({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&ri(r.patterns,{...t,repository:o},n);const i=r.include;if(!i)continue;const l=Yf(i);switch(l.kind){case 0:To({...t,selfGrammar:t.baseGrammar},n);break;case 1:To(t,n);break;case 2:gs(l.ruleName,{...t,repository:o},n);break;case 3:case 4:const s=l.scopeName===t.selfGrammar.scopeName?t.selfGrammar:l.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){const a={baseGrammar:t.baseGrammar,selfGrammar:s,repository:o};l.kind===4?gs(l.ruleName,a,n):To(a,n)}else l.kind===4?n.add(new R0(l.scopeName,l.ruleName)):n.add(new Or(l.scopeName));break}}}var I0=class{constructor(){w(this,"kind",0)}},O0=class{constructor(){w(this,"kind",1)}},b0=class{constructor(e){w(this,"kind",2);this.ruleName=e}},D0=class{constructor(e){w(this,"kind",3);this.scopeName=e}},j0=class{constructor(e,t){w(this,"kind",4);this.scopeName=e,this.ruleName=t}};function Yf(e){if(e==="$base")return new I0;if(e==="$self")return new O0;const t=e.indexOf("#");if(t===-1)return new D0(e);if(t===0)return new b0(e.substring(1));{const n=e.substring(0,t),r=e.substring(t+1);return new j0(n,r)}}var M0=/\\(\d+)/,Yu=/\\(\d+)/g,V0=-1,Xf=-2;var Gr=class{constructor(e,t,n,r){w(this,"$location");w(this,"id");w(this,"_nameIsCapturing");w(this,"_name");w(this,"_contentNameIsCapturing");w(this,"_contentName");this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=po.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=po.hasCaptures(this._contentName)}get debugName(){const e=this.$location?`${Ff(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return!this._nameIsCapturing||this._name===null||e===null||t===null?this._name:po.replaceCaptures(this._name,e,t)}getContentName(e,t){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:po.replaceCaptures(this._contentName,e,t)}},z0=class extends Gr{constructor(t,n,r,o,i){super(t,n,r,o);w(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(t,n){throw new Error("Not supported!")}compile(t,n){throw new Error("Not supported!")}compileAG(t,n,r,o){throw new Error("Not supported!")}},B0=class extends Gr{constructor(t,n,r,o,i){super(t,n,r,null);w(this,"_match");w(this,"captures");w(this,"_cachedCompiledPatterns");this._match=new br(o,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(t,n){n.push(this._match)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Dr,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Xu=class extends Gr{constructor(t,n,r,o,i){super(t,n,r,o);w(this,"hasMissingPatterns");w(this,"patterns");w(this,"_cachedCompiledPatterns");this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(t,n){for(const r of this.patterns)t.getRule(r).collectPatterns(t,n)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new Dr,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},_s=class extends Gr{constructor(t,n,r,o,i,l,s,a,u,d){super(t,n,r,o);w(this,"_begin");w(this,"beginCaptures");w(this,"_end");w(this,"endHasBackReferences");w(this,"endCaptures");w(this,"applyEndPatternLast");w(this,"hasMissingPatterns");w(this,"patterns");w(this,"_cachedCompiledPatterns");this._begin=new br(i,this.id),this.beginCaptures=l,this._end=new br(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=u||!1,this.patterns=d.patterns,this.hasMissingPatterns=d.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(t,n){return this._end.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t,n).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t,n).compileAG(t,r,o)}_getCachedCompiledPatterns(t,n){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Dr;for(const r of this.patterns)t.getRule(r).collectPatterns(t,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,n):this._cachedCompiledPatterns.setSource(0,n)),this._cachedCompiledPatterns}},oi=class extends Gr{constructor(t,n,r,o,i,l,s,a,u){super(t,n,r,o);w(this,"_begin");w(this,"beginCaptures");w(this,"whileCaptures");w(this,"_while");w(this,"whileHasBackReferences");w(this,"hasMissingPatterns");w(this,"patterns");w(this,"_cachedCompiledPatterns");w(this,"_cachedCompiledWhilePatterns");this._begin=new br(i,this.id),this.beginCaptures=l,this.whileCaptures=a,this._while=new br(s,Xf),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(t,n){return this._while.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new Dr;for(const n of this.patterns)t.getRule(n).collectPatterns(t,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(t,n){return this._getCachedCompiledWhilePatterns(t,n).compile(t)}compileWhileAG(t,n,r,o){return this._getCachedCompiledWhilePatterns(t,n).compileAG(t,r,o)}_getCachedCompiledWhilePatterns(t,n){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new Dr,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,n||"￿"),this._cachedCompiledWhilePatterns}},Jf=class ge{static createCaptureRule(t,n,r,o,i){return t.registerRule(l=>new z0(n,l,r,o,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(o=>{if(t.id=o,t.match)return new B0(t.$vscodeTextmateLocation,t.id,t.name,t.match,ge._compileCaptures(t.captures,n,r));if(typeof t.begin>"u"){t.repository&&(r=$f({},r,t.repository));let i=t.patterns;return typeof i>"u"&&t.include&&(i=[{include:t.include}]),new Xu(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,ge._compilePatterns(i,n,r))}return t.while?new oi(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,ge._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,ge._compileCaptures(t.whileCaptures||t.captures,n,r),ge._compilePatterns(t.patterns,n,r)):new _s(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,ge._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,ge._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,ge._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let o=[];if(t){let i=0;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);s>i&&(i=s)}for(let l=0;l<=i;l++)o[l]=null;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);let a=0;t[l].patterns&&(a=ge.getCompiledRuleId(t[l],n,r)),o[s]=ge.createCaptureRule(n,t[l].$vscodeTextmateLocation,t[l].name,t[l].contentName,a)}}return o}static _compilePatterns(t,n,r){let o=[];if(t)for(let i=0,l=t.length;i<l;i++){const s=t[i];let a=-1;if(s.include){const u=Yf(s.include);switch(u.kind){case 0:case 1:a=ge.getCompiledRuleId(r[s.include],n,r);break;case 2:let d=r[u.ruleName];d&&(a=ge.getCompiledRuleId(d,n,r));break;case 3:case 4:const c=u.scopeName,f=u.kind===4?u.ruleName:null,_=n.getExternalGrammar(c,r);if(_)if(f){let v=_.repository[f];v&&(a=ge.getCompiledRuleId(v,n,_.repository))}else a=ge.getCompiledRuleId(_.repository.$self,n,_.repository);break}}else a=ge.getCompiledRuleId(s,n,r);if(a!==-1){const u=n.getRule(a);let d=!1;if((u instanceof Xu||u instanceof _s||u instanceof oi)&&u.hasMissingPatterns&&u.patterns.length===0&&(d=!0),d)continue;o.push(a)}}return{patterns:o,hasMissingPatterns:(t?t.length:0)!==o.length}}},br=class Zf{constructor(t,n){w(this,"source");w(this,"ruleId");w(this,"hasAnchor");w(this,"hasBackReferences");w(this,"_anchorCache");if(t&&typeof t=="string"){const r=t.length;let o=0,i=[],l=!1;for(let s=0;s<r;s++)if(t.charAt(s)==="\\"&&s+1<r){const u=t.charAt(s+1);u==="z"?(i.push(t.substring(o,s)),i.push("$(?!\\n)(?<!\\n)"),o=s+2):(u==="A"||u==="G")&&(l=!0),s++}this.hasAnchor=l,o===0?this.source=t:(i.push(t.substring(o,r)),this.source=i.join(""))}else this.hasAnchor=!1,this.source=t;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=n,typeof this.source=="string"?this.hasBackReferences=M0.test(this.source):this.hasBackReferences=!1}clone(){return new Zf(this.source,this.ruleId)}setSource(t){this.source!==t&&(this.source=t,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(t,n){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let r=n.map(o=>t.substring(o.start,o.end));return Yu.lastIndex=0,this.source.replace(Yu,(o,i)=>Hf(r[parseInt(i,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let t=[],n=[],r=[],o=[],i,l,s,a;for(i=0,l=this.source.length;i<l;i++)s=this.source.charAt(i),t[i]=s,n[i]=s,r[i]=s,o[i]=s,s==="\\"&&i+1<l&&(a=this.source.charAt(i+1),a==="A"?(t[i+1]="￿",n[i+1]="￿",r[i+1]="A",o[i+1]="A"):a==="G"?(t[i+1]="￿",n[i+1]="G",r[i+1]="￿",o[i+1]="G"):(t[i+1]=a,n[i+1]=a,r[i+1]=a,o[i+1]=a),i++);return{A0_G0:t.join(""),A0_G1:n.join(""),A1_G0:r.join(""),A1_G1:o.join("")}}resolveAnchors(t,n){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:t?n?this._anchorCache.A1_G1:this._anchorCache.A1_G0:n?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},Dr=class{constructor(){w(this,"_items");w(this,"_hasAnchors");w(this,"_cached");w(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(n=>n.source);this._cached=new Ju(e,t,this._items.map(n=>n.ruleId))}return this._cached}compileAG(e,t,n){return this._hasAnchors?t?n?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0):n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0):this.compile(e)}_resolveAnchors(e,t,n){let r=this._items.map(o=>o.resolveAnchors(t,n));return new Ju(e,r,this._items.map(o=>o.ruleId))}},Ju=class{constructor(e,t,n){w(this,"scanner");this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join(`
`)}findNextMatchSync(e,t,n){const r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},pl=class{constructor(e,t){this.languageId=e,this.tokenType=t}},ut,$0=(ut=class{constructor(t,n){w(this,"_defaultAttributes");w(this,"_embeddedLanguagesMatcher");w(this,"_getBasicScopeAttributes",new Wf(t=>{const n=this._scopeToLanguage(t),r=this._toStandardTokenType(t);return new pl(n,r)}));this._defaultAttributes=new pl(t,8),this._embeddedLanguagesMatcher=new F0(Object.entries(n||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return t===null?ut._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}_scopeToLanguage(t){return this._embeddedLanguagesMatcher.match(t)||0}_toStandardTokenType(t){const n=t.match(ut.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},w(ut,"_NULL_SCOPE_METADATA",new pl(0,0)),w(ut,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),ut),F0=class{constructor(e){w(this,"values");w(this,"scopesRegExp");if(e.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);const t=e.map(([n,r])=>Hf(n));t.sort(),t.reverse(),this.scopesRegExp=new RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;const t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}},Zu=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function ep(e,t,n,r,o,i,l,s){const a=t.content.length;let u=!1,d=-1;if(l){const _=U0(e,t,n,r,o,i);o=_.stack,r=_.linePos,n=_.isFirstLine,d=_.anchorPosition}const c=Date.now();for(;!u;){if(s!==0&&Date.now()-c>s)return new Zu(o,!0);f()}return new Zu(o,!1);function f(){const _=G0(e,t,n,r,o,d);if(!_){i.produce(o,a),u=!0;return}const v=_.captureIndices,x=_.matchedRuleId,k=v&&v.length>0?v[0].end>r:!1;if(x===V0){const g=o.getRule(e);i.produce(o,v[0].start),o=o.withContentNameScopesList(o.nameScopesList),ir(e,t,n,o,i,g.endCaptures,v),i.produce(o,v[0].end);const h=o;if(o=o.parent,d=h.getAnchorPos(),!k&&h.getEnterPos()===r){o=h,i.produce(o,a),u=!0;return}}else{const g=e.getRule(x);i.produce(o,v[0].start);const h=o,y=g.getName(t.content,v),E=o.contentNameScopesList.pushAttributed(y,e);if(o=o.push(x,r,d,v[0].end===a,null,E,E),g instanceof _s){const S=g;ir(e,t,n,o,i,S.beginCaptures,v),i.produce(o,v[0].end),d=v[0].end;const C=S.getContentName(t.content,v),R=E.pushAttributed(C,e);if(o=o.withContentNameScopesList(R),S.endHasBackReferences&&(o=o.withEndRule(S.getEndWithResolvedBackReferences(t.content,v))),!k&&h.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(g instanceof oi){const S=g;ir(e,t,n,o,i,S.beginCaptures,v),i.produce(o,v[0].end),d=v[0].end;const C=S.getContentName(t.content,v),R=E.pushAttributed(C,e);if(o=o.withContentNameScopesList(R),S.whileHasBackReferences&&(o=o.withEndRule(S.getWhileWithResolvedBackReferences(t.content,v))),!k&&h.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(ir(e,t,n,o,i,g.captures,v),i.produce(o,v[0].end),o=o.pop(),!k){o=o.safePop(),i.produce(o,a),u=!0;return}}v[0].end>r&&(r=v[0].end,n=!1)}}function U0(e,t,n,r,o,i){let l=o.beginRuleCapturedEOL?0:-1;const s=[];for(let a=o;a;a=a.pop()){const u=a.getRule(e);u instanceof oi&&s.push({rule:u,stack:a})}for(let a=s.pop();a;a=s.pop()){const{ruleScanner:u,findOptions:d}=Q0(a.rule,e,a.stack.endRule,n,r===l),c=u.findNextMatchSync(t,r,d);if(c){if(c.ruleId!==Xf){o=a.stack.pop();break}c.captureIndices&&c.captureIndices.length&&(i.produce(a.stack,c.captureIndices[0].start),ir(e,t,n,a.stack,i,a.rule.whileCaptures,c.captureIndices),i.produce(a.stack,c.captureIndices[0].end),l=c.captureIndices[0].end,c.captureIndices[0].end>r&&(r=c.captureIndices[0].end,n=!1))}else{o=a.stack.pop();break}}return{stack:o,linePos:r,anchorPosition:l,isFirstLine:n}}function G0(e,t,n,r,o,i){const l=H0(e,t,n,r,o,i),s=e.getInjections();if(s.length===0)return l;const a=W0(s,e,t,n,r,o,i);if(!a)return l;if(!l)return a;const u=l.captureIndices[0].start,d=a.captureIndices[0].start;return d<u||a.priorityMatch&&d===u?a:l}function H0(e,t,n,r,o,i){const l=o.getRule(e),{ruleScanner:s,findOptions:a}=tp(l,e,o.endRule,n,r===i),u=s.findNextMatchSync(t,r,a);return u?{captureIndices:u.captureIndices,matchedRuleId:u.ruleId}:null}function W0(e,t,n,r,o,i,l){let s=Number.MAX_VALUE,a=null,u,d=0;const c=i.contentNameScopesList.getScopeNames();for(let f=0,_=e.length;f<_;f++){const v=e[f];if(!v.matcher(c))continue;const x=t.getRule(v.ruleId),{ruleScanner:k,findOptions:g}=tp(x,t,null,r,o===l),h=k.findNextMatchSync(n,o,g);if(!h)continue;const y=h.captureIndices[0].start;if(!(y>=s)&&(s=y,a=h.captureIndices,u=h.ruleId,d=v.priority,s===o))break}return a?{priorityMatch:d===-1,captureIndices:a,matchedRuleId:u}:null}function tp(e,t,n,r,o){return{ruleScanner:e.compileAG(t,n,r,o),findOptions:0}}function Q0(e,t,n,r,o){return{ruleScanner:e.compileWhileAG(t,n,r,o),findOptions:0}}function ir(e,t,n,r,o,i,l){if(i.length===0)return;const s=t.content,a=Math.min(i.length,l.length),u=[],d=l[0].end;for(let c=0;c<a;c++){const f=i[c];if(f===null)continue;const _=l[c];if(_.length===0)continue;if(_.start>d)break;for(;u.length>0&&u[u.length-1].endPos<=_.start;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?o.produceFromScopes(u[u.length-1].scopes,_.start):o.produce(r,_.start),f.retokenizeCapturedWithRuleId){const x=f.getName(s,l),k=r.contentNameScopesList.pushAttributed(x,e),g=f.getContentName(s,l),h=k.pushAttributed(g,e),y=r.push(f.retokenizeCapturedWithRuleId,_.start,-1,!1,null,k,h),E=e.createOnigString(s.substring(0,_.end));ep(e,E,n&&_.start===0,_.start,y,o,!1,0),qf(E);continue}const v=f.getName(s,l);if(v!==null){const k=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(v,e);u.push(new K0(k,_.end))}}for(;u.length>0;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var K0=class{constructor(e,t){w(this,"scopes");w(this,"endPos");this.scopes=e,this.endPos=t}};function q0(e,t,n,r,o,i,l,s){return new X0(e,t,n,r,o,i,l,s)}function ec(e,t,n,r,o){const i=ni(t,ii),l=Jf.getCompiledRuleId(n,r,o.repository);for(const s of i)e.push({debugSelector:t,matcher:s.matcher,ruleId:l,grammar:o,priority:s.priority})}function ii(e,t){if(t.length<e.length)return!1;let n=0;return e.every(r=>{for(let o=n;o<t.length;o++)if(Y0(t[o],r))return n=o+1,!0;return!1})}function Y0(e,t){if(!e)return!1;if(e===t)return!0;const n=t.length;return e.length>n&&e.substr(0,n)===t&&e[n]==="."}var X0=class{constructor(e,t,n,r,o,i,l,s){w(this,"_rootId");w(this,"_lastRuleId");w(this,"_ruleId2desc");w(this,"_includedGrammars");w(this,"_grammarRepository");w(this,"_grammar");w(this,"_injections");w(this,"_basicScopeAttributesProvider");w(this,"_tokenTypeMatchers");if(this._rootScopeName=e,this.balancedBracketSelectors=i,this._onigLib=s,this._basicScopeAttributesProvider=new $0(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=l,this._grammar=tc(t,null),this._injections=null,this._tokenTypeMatchers=[],o)for(const a of Object.keys(o)){const u=ni(a,ii);for(const d of u)this._tokenTypeMatchers.push({matcher:d.matcher,type:o[a]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){const e={lookup:o=>o===this._rootScopeName?this._grammar:this.getExternalGrammar(o),injections:o=>this._grammarRepository.injections(o)},t=[],n=this._rootScopeName,r=e.lookup(n);if(r){const o=r.injections;if(o)for(let l in o)ec(t,l,o[l],this,r);const i=this._grammarRepository.injections(n);i&&i.forEach(l=>{const s=this.getExternalGrammar(l);if(s){const a=s.injectionSelector;a&&ec(t,a,s,this,s)}})}return t.sort((o,i)=>o.priority-i.priority),t}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(e){const t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){const n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=tc(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){const r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){const r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){this._rootId===-1&&(this._rootId=Jf.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let o;if(!t||t===ys.NULL){o=!0;const u=this._basicScopeAttributesProvider.getDefaultAttributes(),d=this.themeProvider.getDefaults(),c=Bn.set(0,u.languageId,u.tokenType,null,d.fontStyle,d.foregroundId,d.backgroundId),f=this.getRule(this._rootId).getName(null,null);let _;f?_=hr.createRootAndLookUpScopeName(f,c,this):_=hr.createRoot("unknown",c),t=new ys(null,this._rootId,-1,-1,!1,null,_,_)}else o=!1,t.reset();e=e+`
`;const i=this.createOnigString(e),l=i.content.length,s=new Z0(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),a=ep(this,i,o,0,t,s,!0,r);return qf(i),{lineLength:l,lineTokens:s,ruleStack:a.stack,stoppedEarly:a.stoppedEarly}}};function tc(e,t){return e=g0(e),e.repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var hr=class nt{constructor(t,n,r){this.parent=t,this.scopePath=n,this.tokenAttributes=r}static fromExtension(t,n){let r=t,o=(t==null?void 0:t.scopePath)??null;for(const i of n)o=fl.push(o,i.scopeNames),r=new nt(r,o,i.encodedTokenAttributes);return r}static createRoot(t,n){return new nt(null,new fl(null,t),n)}static createRootAndLookUpScopeName(t,n,r){const o=r.getMetadataForScope(t),i=new fl(null,t),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(n,o,l);return new nt(null,i,s)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return nt.equals(this,t)}static equals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.scopeName!==n.scopeName||t.tokenAttributes!==n.tokenAttributes)return!1;t=t.parent,n=n.parent}while(!0)}static mergeAttributes(t,n,r){let o=-1,i=0,l=0;return r!==null&&(o=r.fontStyle,i=r.foregroundId,l=r.backgroundId),Bn.set(t,n.languageId,n.tokenType,null,o,i,l)}pushAttributed(t,n){if(t===null)return this;if(t.indexOf(" ")===-1)return nt._pushAttributed(this,t,n);const r=t.split(/ /g);let o=this;for(const i of r)o=nt._pushAttributed(o,i,n);return o}static _pushAttributed(t,n,r){const o=r.getMetadataForScope(n),i=t.scopePath.push(n),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(t.tokenAttributes,o,l);return new nt(t,i,s)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(t){var o;const n=[];let r=this;for(;r&&r!==t;)n.push({encodedTokenAttributes:r.tokenAttributes,scopeNames:r.scopePath.getExtensionIfDefined(((o=r.parent)==null?void 0:o.scopePath)??null)}),r=r.parent;return r===t?n.reverse():void 0}},Fe,ys=(Fe=class{constructor(t,n,r,o,i,l,s,a){w(this,"_stackElementBrand");w(this,"_enterPos");w(this,"_anchorPos");w(this,"depth");this.parent=t,this.ruleId=n,this.beginRuleCapturedEOL=i,this.endRule=l,this.nameScopesList=s,this.contentNameScopesList=a,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=r,this._anchorPos=o}equals(t){return t===null?!1:Fe._equals(this,t)}static _equals(t,n){return t===n?!0:this._structuralEquals(t,n)?hr.equals(t.contentNameScopesList,n.contentNameScopesList):!1}static _structuralEquals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.depth!==n.depth||t.ruleId!==n.ruleId||t.endRule!==n.endRule)return!1;t=t.parent,n=n.parent}while(!0)}clone(){return this}static _reset(t){for(;t;)t._enterPos=-1,t._anchorPos=-1,t=t.parent}reset(){Fe._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,o,i,l,s){return new Fe(this,t,n,r,o,i,l,s)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(t){return t.getRule(this.ruleId)}toString(){const t=[];return this._writeString(t,0),"["+t.join(",")+"]"}_writeString(t,n){var r,o;return this.parent&&(n=this.parent._writeString(t,n)),t[n++]=`(${this.ruleId}, ${(r=this.nameScopesList)==null?void 0:r.toString()}, ${(o=this.contentNameScopesList)==null?void 0:o.toString()})`,n}withContentNameScopesList(t){return this.contentNameScopesList===t?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,t)}withEndRule(t){return this.endRule===t?this:new Fe(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(t){let n=this;for(;n&&n._enterPos===t._enterPos;){if(n.ruleId===t.ruleId)return!0;n=n.parent}return!1}toStateStackFrame(){var t,n,r;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((n=this.nameScopesList)==null?void 0:n.getExtensionIfDefined(((t=this.parent)==null?void 0:t.nameScopesList)??null))??[],contentNameScopesList:((r=this.contentNameScopesList)==null?void 0:r.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(t,n){const r=hr.fromExtension((t==null?void 0:t.nameScopesList)??null,n.nameScopesList);return new Fe(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,hr.fromExtension(r,n.contentNameScopesList))}},w(Fe,"NULL",new Fe(null,0,0,0,!1,null,null,null)),Fe),J0=class{constructor(e,t){w(this,"balancedBracketScopes");w(this,"unbalancedBracketScopes");w(this,"allowAny",!1);this.balancedBracketScopes=e.flatMap(n=>n==="*"?(this.allowAny=!0,[]):ni(n,ii).map(r=>r.matcher)),this.unbalancedBracketScopes=t.flatMap(n=>ni(n,ii).map(r=>r.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(e){for(const t of this.unbalancedBracketScopes)if(t(e))return!1;for(const t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},Z0=class{constructor(e,t,n,r){w(this,"_emitBinaryTokens");w(this,"_lineText");w(this,"_tokens");w(this,"_binaryTokens");w(this,"_lastTokenEndIndex");w(this,"_tokenTypeOverrides");this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){var r;if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let o=(e==null?void 0:e.tokenAttributes)??0,i=!1;if((r=this.balancedBracketSelectors)!=null&&r.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const l=(e==null?void 0:e.getScopeNames())??[];for(const s of this._tokenTypeOverrides)s.matcher(l)&&(o=Bn.set(o,0,s.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(l))}if(i&&(o=Bn.set(o,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===o){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(o),this._lastTokenEndIndex=t;return}const n=(e==null?void 0:e.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);const n=new Uint32Array(this._binaryTokens.length);for(let r=0,o=this._binaryTokens.length;r<o;r++)n[r]=this._binaryTokens[r];return n}},e_=class{constructor(e,t){w(this,"_grammars",new Map);w(this,"_rawGrammars",new Map);w(this,"_injectionGrammars",new Map);w(this,"_theme");this._onigLib=t,this._theme=e}dispose(){for(const e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,o){if(!this._grammars.has(e)){let i=this._rawGrammars.get(e);if(!i)return null;this._grammars.set(e,q0(e,i,t,n,r,o,this,this._onigLib))}return this._grammars.get(e)}},t_=class{constructor(t){w(this,"_options");w(this,"_syncRegistry");w(this,"_ensureGrammarCache");this._options=t,this._syncRegistry=new e_(ti.createFromRawTheme(t.theme,t.colorMap),t.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(t,n){this._syncRegistry.setTheme(ti.createFromRawTheme(t,n))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(t,n,r){return this.loadGrammarWithConfiguration(t,n,{embeddedLanguages:r})}loadGrammarWithConfiguration(t,n,r){return this._loadGrammar(t,n,r.embeddedLanguages,r.tokenTypes,new J0(r.balancedBracketSelectors||[],r.unbalancedBracketSelectors||[]))}loadGrammar(t){return this._loadGrammar(t,0,null,null,null)}_loadGrammar(t,n,r,o,i){const l=new N0(this._syncRegistry,t);for(;l.Q.length>0;)l.Q.map(s=>this._loadSingleGrammar(s.scopeName)),l.processQueue();return this._grammarForScopeName(t,n,r,o,i)}_loadSingleGrammar(t){this._ensureGrammarCache.has(t)||(this._doLoadSingleGrammar(t),this._ensureGrammarCache.set(t,!0))}_doLoadSingleGrammar(t){const n=this._options.loadGrammar(t);if(n){const r=typeof this._options.getInjections=="function"?this._options.getInjections(t):void 0;this._syncRegistry.addGrammar(n,r)}}addGrammar(t,n=[],r=0,o=null){return this._syncRegistry.addGrammar(t,n),this._grammarForScopeName(t.scopeName,r,o)}_grammarForScopeName(t,n=0,r=null,o=null,i=null){return this._syncRegistry.grammarForScopeName(t,n,r,o,i)}},vs=ys.NULL;const n_=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class Hr{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}Hr.prototype.normal={};Hr.prototype.property={};Hr.prototype.space=void 0;function np(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new Hr(n,r,t)}function xs(e){return e.toLowerCase()}class Te{constructor(t,n){this.attribute=n,this.property=t}}Te.prototype.attribute="";Te.prototype.booleanish=!1;Te.prototype.boolean=!1;Te.prototype.commaOrSpaceSeparated=!1;Te.prototype.commaSeparated=!1;Te.prototype.defined=!1;Te.prototype.mustUseProperty=!1;Te.prototype.number=!1;Te.prototype.overloadedBoolean=!1;Te.prototype.property="";Te.prototype.spaceSeparated=!1;Te.prototype.space=void 0;let r_=0;const j=un(),te=un(),Ss=un(),P=un(),F=un(),Nn=un(),Ne=un();function un(){return 2**++r_}const Es=Object.freeze(Object.defineProperty({__proto__:null,boolean:j,booleanish:te,commaOrSpaceSeparated:Ne,commaSeparated:Nn,number:P,overloadedBoolean:Ss,spaceSeparated:F},Symbol.toStringTag,{value:"Module"})),ml=Object.keys(Es);class wa extends Te{constructor(t,n,r,o){let i=-1;if(super(t,n),nc(this,"space",o),typeof r=="number")for(;++i<ml.length;){const l=ml[i];nc(this,ml[i],(r&Es[l])===Es[l])}}}wa.prototype.defined=!0;function nc(e,t,n){n&&(e[t]=n)}function Gn(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new wa(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[xs(r)]=r,n[xs(i.attribute)]=r}return new Hr(t,n,e.space)}const rp=Gn({properties:{ariaActiveDescendant:null,ariaAtomic:te,ariaAutoComplete:null,ariaBusy:te,ariaChecked:te,ariaColCount:P,ariaColIndex:P,ariaColSpan:P,ariaControls:F,ariaCurrent:null,ariaDescribedBy:F,ariaDetails:null,ariaDisabled:te,ariaDropEffect:F,ariaErrorMessage:null,ariaExpanded:te,ariaFlowTo:F,ariaGrabbed:te,ariaHasPopup:null,ariaHidden:te,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:F,ariaLevel:P,ariaLive:null,ariaModal:te,ariaMultiLine:te,ariaMultiSelectable:te,ariaOrientation:null,ariaOwns:F,ariaPlaceholder:null,ariaPosInSet:P,ariaPressed:te,ariaReadOnly:te,ariaRelevant:null,ariaRequired:te,ariaRoleDescription:F,ariaRowCount:P,ariaRowIndex:P,ariaRowSpan:P,ariaSelected:te,ariaSetSize:P,ariaSort:null,ariaValueMax:P,ariaValueMin:P,ariaValueNow:P,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function op(e,t){return t in e?e[t]:t}function ip(e,t){return op(e,t.toLowerCase())}const o_=Gn({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Nn,acceptCharset:F,accessKey:F,action:null,allow:null,allowFullScreen:j,allowPaymentRequest:j,allowUserMedia:j,alt:null,as:null,async:j,autoCapitalize:null,autoComplete:F,autoFocus:j,autoPlay:j,blocking:F,capture:null,charSet:null,checked:j,cite:null,className:F,cols:P,colSpan:null,content:null,contentEditable:te,controls:j,controlsList:F,coords:P|Nn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:j,defer:j,dir:null,dirName:null,disabled:j,download:Ss,draggable:te,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:j,formTarget:null,headers:F,height:P,hidden:Ss,high:P,href:null,hrefLang:null,htmlFor:F,httpEquiv:F,id:null,imageSizes:null,imageSrcSet:null,inert:j,inputMode:null,integrity:null,is:null,isMap:j,itemId:null,itemProp:F,itemRef:F,itemScope:j,itemType:F,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:j,low:P,manifest:null,max:null,maxLength:P,media:null,method:null,min:null,minLength:P,multiple:j,muted:j,name:null,nonce:null,noModule:j,noValidate:j,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:j,optimum:P,pattern:null,ping:F,placeholder:null,playsInline:j,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:j,referrerPolicy:null,rel:F,required:j,reversed:j,rows:P,rowSpan:P,sandbox:F,scope:null,scoped:j,seamless:j,selected:j,shadowRootClonable:j,shadowRootDelegatesFocus:j,shadowRootMode:null,shape:null,size:P,sizes:null,slot:null,span:P,spellCheck:te,src:null,srcDoc:null,srcLang:null,srcSet:null,start:P,step:null,style:null,tabIndex:P,target:null,title:null,translate:null,type:null,typeMustMatch:j,useMap:null,value:te,width:P,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:F,axis:null,background:null,bgColor:null,border:P,borderColor:null,bottomMargin:P,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:j,declare:j,event:null,face:null,frame:null,frameBorder:null,hSpace:P,leftMargin:P,link:null,longDesc:null,lowSrc:null,marginHeight:P,marginWidth:P,noResize:j,noHref:j,noShade:j,noWrap:j,object:null,profile:null,prompt:null,rev:null,rightMargin:P,rules:null,scheme:null,scrolling:te,standby:null,summary:null,text:null,topMargin:P,valueType:null,version:null,vAlign:null,vLink:null,vSpace:P,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:j,disableRemotePlayback:j,prefix:null,property:null,results:P,security:null,unselectable:null},space:"html",transform:ip}),i_=Gn({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Ne,accentHeight:P,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:P,amplitude:P,arabicForm:null,ascent:P,attributeName:null,attributeType:null,azimuth:P,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:P,by:null,calcMode:null,capHeight:P,className:F,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:P,diffuseConstant:P,direction:null,display:null,dur:null,divisor:P,dominantBaseline:null,download:j,dx:null,dy:null,edgeMode:null,editable:null,elevation:P,enableBackground:null,end:null,event:null,exponent:P,externalResourcesRequired:null,fill:null,fillOpacity:P,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Nn,g2:Nn,glyphName:Nn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:P,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:P,horizOriginX:P,horizOriginY:P,id:null,ideographic:P,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:P,k:P,k1:P,k2:P,k3:P,k4:P,kernelMatrix:Ne,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:P,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:P,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:P,overlineThickness:P,paintOrder:null,panose1:null,path:null,pathLength:P,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:F,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:P,pointsAtY:P,pointsAtZ:P,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Ne,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Ne,rev:Ne,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Ne,requiredFeatures:Ne,requiredFonts:Ne,requiredFormats:Ne,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:P,specularExponent:P,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:P,strikethroughThickness:P,string:null,stroke:null,strokeDashArray:Ne,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:P,strokeOpacity:P,strokeWidth:null,style:null,surfaceScale:P,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Ne,tabIndex:P,tableValues:null,target:null,targetX:P,targetY:P,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Ne,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:P,underlineThickness:P,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:P,values:null,vAlphabetic:P,vMathematical:P,vectorEffect:null,vHanging:P,vIdeographic:P,version:null,vertAdvY:P,vertOriginX:P,vertOriginY:P,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:P,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:op}),lp=Gn({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),sp=Gn({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:ip}),ap=Gn({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),l_=/[A-Z]/g,rc=/-[a-z]/g,s_=/^data[-\w.:]+$/i;function a_(e,t){const n=xs(t);let r=t,o=Te;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&s_.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(rc,c_);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!rc.test(i)){let l=i.replace(l_,u_);l.charAt(0)!=="-"&&(l="-"+l),t="data"+l}}o=wa}return new o(r,t)}function u_(e){return"-"+e.toLowerCase()}function c_(e){return e.charAt(1).toUpperCase()}const d_=np([rp,o_,lp,sp,ap],"html"),up=np([rp,i_,lp,sp,ap],"svg"),oc={}.hasOwnProperty;function f_(e,t){const n=t||{};function r(o,...i){let l=r.invalid;const s=r.handlers;if(o&&oc.call(o,e)){const a=String(o[e]);l=oc.call(s,a)?s[a]:r.unknown}if(l)return l.call(this,o,...i)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}const p_=/["&'<>`]/g,m_=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,h_=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,g_=/[|\\{}()[\]^$+*?.]/g,ic=new WeakMap;function __(e,t){if(e=e.replace(t.subset?y_(t.subset):p_,r),t.subset||t.escapeOnly)return e;return e.replace(m_,n).replace(h_,r);function n(o,i,l){return t.format((o.charCodeAt(0)-55296)*1024+o.charCodeAt(1)-56320+65536,l.charCodeAt(i+2),t)}function r(o,i,l){return t.format(o.charCodeAt(0),l.charCodeAt(i+1),t)}}function y_(e){let t=ic.get(e);return t||(t=v_(e),ic.set(e,t)),t}function v_(e){const t=[];let n=-1;for(;++n<e.length;)t.push(e[n].replace(g_,"\\$&"));return new RegExp("(?:"+t.join("|")+")","g")}const x_=/[\dA-Fa-f]/;function S_(e,t,n){const r="&#x"+e.toString(16).toUpperCase();return n&&t&&!x_.test(String.fromCharCode(t))?r:r+";"}const E_=/\d/;function w_(e,t,n){const r="&#"+String(e);return n&&t&&!E_.test(String.fromCharCode(t))?r:r+";"}const k_=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],hl={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},C_=["cent","copy","divide","gt","lt","not","para","times"],cp={}.hasOwnProperty,ws={};let mo;for(mo in hl)cp.call(hl,mo)&&(ws[hl[mo]]=mo);const L_=/[^\dA-Za-z]/;function P_(e,t,n,r){const o=String.fromCharCode(e);if(cp.call(ws,o)){const i=ws[o],l="&"+i;return n&&k_.includes(i)&&!C_.includes(i)&&(!r||t&&t!==61&&L_.test(String.fromCharCode(t)))?l:l+";"}return""}function R_(e,t,n){let r=S_(e,t,n.omitOptionalSemicolons),o;if((n.useNamedReferences||n.useShortestReferences)&&(o=P_(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!o)&&n.useShortestReferences){const i=w_(e,t,n.omitOptionalSemicolons);i.length<r.length&&(r=i)}return o&&(!n.useShortestReferences||o.length<r.length)?o:r}function An(e,t){return __(e,Object.assign({format:R_},t))}const T_=/^>|^->|<!--|-->|--!>|<!-$/g,N_=[">"],A_=["<",">"];function I_(e,t,n,r){return r.settings.bogusComments?"<?"+An(e.value,Object.assign({},r.settings.characterReferences,{subset:N_}))+">":"<!--"+e.value.replace(T_,o)+"-->";function o(i){return An(i,Object.assign({},r.settings.characterReferences,{subset:A_}))}}function O_(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"}function lc(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;o!==-1;)r++,o=n.indexOf(t,o+t.length);return r}function b_(e,t){const n=t||{};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}function D_(e){return e.join(" ").trim()}const j_=/[ \t\n\f\r]/g;function ka(e){return typeof e=="object"?e.type==="text"?sc(e.value):!1:sc(e)}function sc(e){return e.replace(j_,"")===""}const le=fp(1),dp=fp(-1),M_=[];function fp(e){return t;function t(n,r,o){const i=n?n.children:M_;let l=(r||0)+e,s=i[l];if(!o)for(;s&&ka(s);)l+=e,s=i[l];return s}}const V_={}.hasOwnProperty;function pp(e){return t;function t(n,r,o){return V_.call(e,n.tagName)&&e[n.tagName](n,r,o)}}const Ca=pp({body:B_,caption:gl,colgroup:gl,dd:G_,dt:U_,head:gl,html:z_,li:F_,optgroup:H_,option:W_,p:$_,rp:ac,rt:ac,tbody:K_,td:uc,tfoot:q_,th:uc,thead:Q_,tr:Y_});function gl(e,t,n){const r=le(n,t,!0);return!r||r.type!=="comment"&&!(r.type==="text"&&ka(r.value.charAt(0)))}function z_(e,t,n){const r=le(n,t);return!r||r.type!=="comment"}function B_(e,t,n){const r=le(n,t);return!r||r.type!=="comment"}function $_(e,t,n){const r=le(n,t);return r?r.type==="element"&&(r.tagName==="address"||r.tagName==="article"||r.tagName==="aside"||r.tagName==="blockquote"||r.tagName==="details"||r.tagName==="div"||r.tagName==="dl"||r.tagName==="fieldset"||r.tagName==="figcaption"||r.tagName==="figure"||r.tagName==="footer"||r.tagName==="form"||r.tagName==="h1"||r.tagName==="h2"||r.tagName==="h3"||r.tagName==="h4"||r.tagName==="h5"||r.tagName==="h6"||r.tagName==="header"||r.tagName==="hgroup"||r.tagName==="hr"||r.tagName==="main"||r.tagName==="menu"||r.tagName==="nav"||r.tagName==="ol"||r.tagName==="p"||r.tagName==="pre"||r.tagName==="section"||r.tagName==="table"||r.tagName==="ul"):!n||!(n.type==="element"&&(n.tagName==="a"||n.tagName==="audio"||n.tagName==="del"||n.tagName==="ins"||n.tagName==="map"||n.tagName==="noscript"||n.tagName==="video"))}function F_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="li"}function U_(e,t,n){const r=le(n,t);return!!(r&&r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd"))}function G_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd")}function ac(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="rp"||r.tagName==="rt")}function H_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="optgroup"}function W_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="option"||r.tagName==="optgroup")}function Q_(e,t,n){const r=le(n,t);return!!(r&&r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot"))}function K_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot")}function q_(e,t,n){return!le(n,t)}function Y_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="tr"}function uc(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="td"||r.tagName==="th")}const X_=pp({body:ey,colgroup:ty,head:Z_,html:J_,tbody:ny});function J_(e){const t=le(e,-1);return!t||t.type!=="comment"}function Z_(e){const t=new Set;for(const r of e.children)if(r.type==="element"&&(r.tagName==="base"||r.tagName==="title")){if(t.has(r.tagName))return!1;t.add(r.tagName)}const n=e.children[0];return!n||n.type==="element"}function ey(e){const t=le(e,-1,!0);return!t||t.type!=="comment"&&!(t.type==="text"&&ka(t.value.charAt(0)))&&!(t.type==="element"&&(t.tagName==="meta"||t.tagName==="link"||t.tagName==="script"||t.tagName==="style"||t.tagName==="template"))}function ty(e,t,n){const r=dp(n,t),o=le(e,-1,!0);return n&&r&&r.type==="element"&&r.tagName==="colgroup"&&Ca(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="col")}function ny(e,t,n){const r=dp(n,t),o=le(e,-1);return n&&r&&r.type==="element"&&(r.tagName==="thead"||r.tagName==="tbody")&&Ca(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="tr")}const ho={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function ry(e,t,n,r){const o=r.schema,i=o.space==="svg"?!1:r.settings.omitOptionalTags;let l=o.space==="svg"?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase());const s=[];let a;o.space==="html"&&e.tagName==="svg"&&(r.schema=up);const u=oy(r,e.properties),d=r.all(o.space==="html"&&e.tagName==="template"?e.content:e);return r.schema=o,d&&(l=!1),(u||!i||!X_(e,t,n))&&(s.push("<",e.tagName,u?" "+u:""),l&&(o.space==="svg"||r.settings.closeSelfClosing)&&(a=u.charAt(u.length-1),(!r.settings.tightSelfClosing||a==="/"||a&&a!=='"'&&a!=="'")&&s.push(" "),s.push("/")),s.push(">")),s.push(d),!l&&(!i||!Ca(e,t,n))&&s.push("</"+e.tagName+">"),s.join("")}function oy(e,t){const n=[];let r=-1,o;if(t){for(o in t)if(t[o]!==null&&t[o]!==void 0){const i=iy(e,o,t[o]);i&&n.push(i)}}for(;++r<n.length;){const i=e.settings.tightAttributes?n[r].charAt(n[r].length-1):void 0;r!==n.length-1&&i!=='"'&&i!=="'"&&(n[r]+=" ")}return n.join("")}function iy(e,t,n){const r=a_(e.schema,t),o=e.settings.allowParseErrors&&e.schema.space==="html"?0:1,i=e.settings.allowDangerousCharacters?0:1;let l=e.quote,s;if(r.overloadedBoolean&&(n===r.attribute||n==="")?n=!0:(r.boolean||r.overloadedBoolean)&&(typeof n!="string"||n===r.attribute||n==="")&&(n=!!n),n==null||n===!1||typeof n=="number"&&Number.isNaN(n))return"";const a=An(r.attribute,Object.assign({},e.settings.characterReferences,{subset:ho.name[o][i]}));return n===!0||(n=Array.isArray(n)?(r.commaSeparated?b_:D_)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?a:(e.settings.preferUnquoted&&(s=An(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:ho.unquoted[o][i]}))),s!==n&&(e.settings.quoteSmart&&lc(n,l)>lc(n,e.alternative)&&(l=e.alternative),s=l+An(n,Object.assign({},e.settings.characterReferences,{subset:(l==="'"?ho.single:ho.double)[o][i],attribute:!0}))+l),a+(s&&"="+s))}const ly=["<","&"];function mp(e,t,n,r){return n&&n.type==="element"&&(n.tagName==="script"||n.tagName==="style")?e.value:An(e.value,Object.assign({},r.settings.characterReferences,{subset:ly}))}function sy(e,t,n,r){return r.settings.allowDangerousHtml?e.value:mp(e,t,n,r)}function ay(e,t,n,r){return r.all(e)}const uy=f_("type",{invalid:cy,unknown:dy,handlers:{comment:I_,doctype:O_,element:ry,raw:sy,root:ay,text:mp}});function cy(e){throw new Error("Expected node, not `"+e+"`")}function dy(e){const t=e;throw new Error("Cannot compile unknown node `"+t.type+"`")}const fy={},py={},my=[];function hy(e,t){const n=fy,r=n.quote||'"',o=r==='"'?"'":'"';if(r!=='"'&&r!=="'")throw new Error("Invalid quote `"+r+"`, expected `'` or `\"`");return{one:gy,all:_y,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||n_,characterReferences:n.characterReferences||py,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:n.space==="svg"?up:d_,quote:r,alternative:o}.one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}function gy(e,t,n){return uy(e,t,n,this)}function _y(e){const t=[],n=e&&e.children||my;let r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}function yy(e){return Array.isArray(e)?e:[e]}function Ii(e,t=!1){var i;const n=e.split(/(\r?\n)/g);let r=0;const o=[];for(let l=0;l<n.length;l+=2){const s=t?n[l]+(n[l+1]||""):n[l];o.push([s,r]),r+=n[l].length,r+=((i=n[l+1])==null?void 0:i.length)||0}return o}function La(e){return!e||["plaintext","txt","text","plain"].includes(e)}function hp(e){return e==="ansi"||La(e)}function Pa(e){return e==="none"}function gp(e){return Pa(e)}function _p(e,t){var r;if(!t)return e;e.properties||(e.properties={}),(r=e.properties).class||(r.class=[]),typeof e.properties.class=="string"&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]);const n=Array.isArray(t)?t:t.split(/\s+/g);for(const o of n)o&&!e.properties.class.includes(o)&&e.properties.class.push(o);return e}function vy(e,t){let n=0;const r=[];for(const o of t)o>n&&r.push({...e,content:e.content.slice(n,o),offset:e.offset+n}),n=o;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r}function xy(e,t){const n=Array.from(t instanceof Set?t:new Set(t)).sort((r,o)=>r-o);return n.length?e.map(r=>r.flatMap(o=>{const i=n.filter(l=>o.offset<l&&l<o.offset+o.content.length).map(l=>l-o.offset).sort((l,s)=>l-s);return i.length?vy(o,i):o})):e}async function yp(e){return Promise.resolve(typeof e=="function"?e():e).then(t=>t.default||t)}function li(e,t){const n=typeof e=="string"?{}:{...e.colorReplacements},r=typeof e=="string"?e:e.name;for(const[o,i]of Object.entries((t==null?void 0:t.colorReplacements)||{}))typeof i=="string"?n[o]=i:o===r&&Object.assign(n,i);return n}function Yt(e,t){return e&&((t==null?void 0:t[e==null?void 0:e.toLowerCase()])||e)}function vp(e){const t={};return e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle&&(e.fontStyle&pt.Italic&&(t["font-style"]="italic"),e.fontStyle&pt.Bold&&(t["font-weight"]="bold"),e.fontStyle&pt.Underline&&(t["text-decoration"]="underline")),t}function Sy(e){return typeof e=="string"?e:Object.entries(e).map(([t,n])=>`${t}:${n}`).join(";")}function Ey(e){const t=Ii(e,!0).map(([o])=>o);function n(o){if(o===e.length)return{line:t.length-1,character:t[t.length-1].length};let i=o,l=0;for(const s of t){if(i<s.length)break;i-=s.length,l++}return{line:l,character:i}}function r(o,i){let l=0;for(let s=0;s<o;s++)l+=t[s].length;return l+=i,l}return{lines:t,indexToPos:n,posToIndex:r}}class xe extends Error{constructor(t){super(t),this.name="ShikiError"}}const xp=new WeakMap;function Oi(e,t){xp.set(e,t)}function jr(e){return xp.get(e)}class Hn{constructor(...t){w(this,"_stacks",{});w(this,"lang");if(t.length===2){const[n,r]=t;this.lang=r,this._stacks=n}else{const[n,r,o]=t;this.lang=r,this._stacks={[o]:n}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(t,n){return new Hn(Object.fromEntries(yy(n).map(r=>[r,vs])),t)}getInternalStack(t=this.theme){return this._stacks[t]}get scopes(){return cc(this._stacks[this.theme])}getScopes(t=this.theme){return cc(this._stacks[t])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function cc(e){const t=[],n=new Set;function r(o){var l;if(n.has(o))return;n.add(o);const i=(l=o==null?void 0:o.nameScopesList)==null?void 0:l.scopeName;i&&t.push(i),o.parent&&r(o.parent)}return r(e),t}function wy(e,t){if(!(e instanceof Hn))throw new xe("Invalid grammar state");return e.getInternalStack(t)}function ky(){const e=new WeakMap;function t(n){if(!e.has(n.meta)){let r=function(l){if(typeof l=="number"){if(l<0||l>n.source.length)throw new xe(`Invalid decoration offset: ${l}. Code length: ${n.source.length}`);return{...o.indexToPos(l),offset:l}}else{const s=o.lines[l.line];if(s===void 0)throw new xe(`Invalid decoration position ${JSON.stringify(l)}. Lines length: ${o.lines.length}`);if(l.character<0||l.character>s.length)throw new xe(`Invalid decoration position ${JSON.stringify(l)}. Line ${l.line} length: ${s.length}`);return{...l,offset:o.posToIndex(l.line,l.character)}}};const o=Ey(n.source),i=(n.options.decorations||[]).map(l=>({...l,start:r(l.start),end:r(l.end)}));Cy(i),e.set(n.meta,{decorations:i,converter:o,source:n.source})}return e.get(n.meta)}return{name:"shiki:decorations",tokens(n){var l;if(!((l=this.options.decorations)!=null&&l.length))return;const o=t(this).decorations.flatMap(s=>[s.start.offset,s.end.offset]);return xy(n,o)},code(n){var d;if(!((d=this.options.decorations)!=null&&d.length))return;const r=t(this),o=Array.from(n.children).filter(c=>c.type==="element"&&c.tagName==="span");if(o.length!==r.converter.lines.length)throw new xe(`Number of lines in code element (${o.length}) does not match the number of lines in the source (${r.converter.lines.length}). Failed to apply decorations.`);function i(c,f,_,v){const x=o[c];let k="",g=-1,h=-1;if(f===0&&(g=0),_===0&&(h=0),_===Number.POSITIVE_INFINITY&&(h=x.children.length),g===-1||h===-1)for(let E=0;E<x.children.length;E++)k+=Sp(x.children[E]),g===-1&&k.length===f&&(g=E+1),h===-1&&k.length===_&&(h=E+1);if(g===-1)throw new xe(`Failed to find start index for decoration ${JSON.stringify(v.start)}`);if(h===-1)throw new xe(`Failed to find end index for decoration ${JSON.stringify(v.end)}`);const y=x.children.slice(g,h);if(!v.alwaysWrap&&y.length===x.children.length)s(x,v,"line");else if(!v.alwaysWrap&&y.length===1&&y[0].type==="element")s(y[0],v,"token");else{const E={type:"element",tagName:"span",properties:{},children:y};s(E,v,"wrapper"),x.children.splice(g,y.length,E)}}function l(c,f){o[c]=s(o[c],f,"line")}function s(c,f,_){var k;const v=f.properties||{},x=f.transform||(g=>g);return c.tagName=f.tagName||"span",c.properties={...c.properties,...v,class:c.properties.class},(k=f.properties)!=null&&k.class&&_p(c,f.properties.class),c=x(c,_)||c,c}const a=[],u=r.decorations.sort((c,f)=>f.start.offset-c.start.offset);for(const c of u){const{start:f,end:_}=c;if(f.line===_.line)i(f.line,f.character,_.character,c);else if(f.line<_.line){i(f.line,f.character,Number.POSITIVE_INFINITY,c);for(let v=f.line+1;v<_.line;v++)a.unshift(()=>l(v,c));i(_.line,0,_.character,c)}}a.forEach(c=>c())}}}function Cy(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.start.offset>n.end.offset)throw new xe(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){const o=e[r],i=n.start.offset<o.start.offset&&o.start.offset<n.end.offset,l=n.start.offset<o.end.offset&&o.end.offset<n.end.offset,s=o.start.offset<n.start.offset&&n.start.offset<o.end.offset,a=o.start.offset<n.end.offset&&n.end.offset<o.end.offset;if(i||l||s||a){if(l&&l||s&&a)continue;throw new xe(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(o.start)} intersect.`)}}}}function Sp(e){return e.type==="text"?e.value:e.type==="element"?e.children.map(Sp).join(""):""}const Ly=[ky()];function si(e){return[...e.transformers||[],...Ly]}var Xt=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],_l={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function Py(e,t){const n=e.indexOf("\x1B[",t);if(n!==-1){const r=e.indexOf("m",n);return{sequence:e.substring(n+2,r).split(";"),startPosition:n,position:r+1}}return{position:e.length}}function dc(e,t){let n=1;const r=e[t+n++];let o;if(r==="2"){const i=[e[t+n++],e[t+n++],e[t+n]].map(l=>Number.parseInt(l));i.length===3&&!i.some(l=>Number.isNaN(l))&&(o={type:"rgb",rgb:i})}else if(r==="5"){const i=Number.parseInt(e[t+n]);Number.isNaN(i)||(o={type:"table",index:Number(i)})}return[n,o]}function Ry(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n],o=Number.parseInt(r);if(!Number.isNaN(o))if(o===0)t.push({type:"resetAll"});else if(o<=9)_l[o]&&t.push({type:"setDecoration",value:_l[o]});else if(o<=29){const i=_l[o-20];i&&t.push({type:"resetDecoration",value:i})}else if(o<=37)t.push({type:"setForegroundColor",value:{type:"named",name:Xt[o-30]}});else if(o===38){const[i,l]=dc(e,n);l&&t.push({type:"setForegroundColor",value:l}),n+=i}else if(o===39)t.push({type:"resetForegroundColor"});else if(o<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:Xt[o-40]}});else if(o===48){const[i,l]=dc(e,n);l&&t.push({type:"setBackgroundColor",value:l}),n+=i}else o===49?t.push({type:"resetBackgroundColor"}):o>=90&&o<=97?t.push({type:"setForegroundColor",value:{type:"named",name:Xt[o-90+8]}}):o>=100&&o<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:Xt[o-100+8]}})}return t}function Ty(){let e=null,t=null,n=new Set;return{parse(r){const o=[];let i=0;do{const l=Py(r,i),s=l.sequence?r.substring(i,l.startPosition):r.substring(i);if(s.length>0&&o.push({value:s,foreground:e,background:t,decorations:new Set(n)}),l.sequence){const a=Ry(l.sequence);for(const u of a)u.type==="resetAll"?(e=null,t=null,n.clear()):u.type==="resetForegroundColor"?e=null:u.type==="resetBackgroundColor"?t=null:u.type==="resetDecoration"&&n.delete(u.value);for(const u of a)u.type==="setForegroundColor"?e=u.value:u.type==="setBackgroundColor"?t=u.value:u.type==="setDecoration"&&n.add(u.value)}i=l.position}while(i<r.length);return o}}}var Ny={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function Ay(e=Ny){function t(s){return e[s]}function n(s){return`#${s.map(a=>Math.max(0,Math.min(a,255)).toString(16).padStart(2,"0")).join("")}`}let r;function o(){if(r)return r;r=[];for(let u=0;u<Xt.length;u++)r.push(t(Xt[u]));let s=[0,95,135,175,215,255];for(let u=0;u<6;u++)for(let d=0;d<6;d++)for(let c=0;c<6;c++)r.push(n([s[u],s[d],s[c]]));let a=8;for(let u=0;u<24;u++,a+=10)r.push(n([a,a,a]));return r}function i(s){return o()[s]}function l(s){switch(s.type){case"named":return t(s.name);case"rgb":return n(s.rgb);case"table":return i(s.index)}}return{value:l}}function Iy(e,t,n){const r=li(e,n),o=Ii(t),i=Ay(Object.fromEntries(Xt.map(s=>{var a;return[s,(a=e.colors)==null?void 0:a[`terminal.ansi${s[0].toUpperCase()}${s.substring(1)}`]]}))),l=Ty();return o.map(s=>l.parse(s[0]).map(a=>{let u,d;a.decorations.has("reverse")?(u=a.background?i.value(a.background):e.bg,d=a.foreground?i.value(a.foreground):e.fg):(u=a.foreground?i.value(a.foreground):e.fg,d=a.background?i.value(a.background):void 0),u=Yt(u,r),d=Yt(d,r),a.decorations.has("dim")&&(u=Oy(u));let c=pt.None;return a.decorations.has("bold")&&(c|=pt.Bold),a.decorations.has("italic")&&(c|=pt.Italic),a.decorations.has("underline")&&(c|=pt.Underline),{content:a.value,offset:s[1],color:u,bgColor:d,fontStyle:c}}))}function Oy(e){const t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){const r=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${r}`}else return t[2]?`#${t[1]}${t[2]}80`:`#${Array.from(t[1]).map(r=>`${r}${r}`).join("")}80`;const n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}function Ra(e,t,n={}){const{lang:r="text",theme:o=e.getLoadedThemes()[0]}=n;if(La(r)||Pa(o))return Ii(t).map(a=>[{content:a[0],offset:a[1]}]);const{theme:i,colorMap:l}=e.setTheme(o);if(r==="ansi")return Iy(i,t,n);const s=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==s.name)throw new ht(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${s.name}"`);if(!n.grammarState.themes.includes(i.name))throw new ht(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${i.name}"`)}return Dy(t,s,i,l,n)}function by(...e){if(e.length===2)return jr(e[1]);const[t,n,r={}]=e,{lang:o="text",theme:i=t.getLoadedThemes()[0]}=r;if(La(o)||Pa(i))throw new ht("Plain language does not have grammar state");if(o==="ansi")throw new ht("ANSI language does not have grammar state");const{theme:l,colorMap:s}=t.setTheme(i),a=t.getLanguage(o);return new Hn(ai(n,a,l,s,r).stateStack,a.name,l.name)}function Dy(e,t,n,r,o){const i=ai(e,t,n,r,o),l=new Hn(ai(e,t,n,r,o).stateStack,t.name,n.name);return Oi(i.tokens,l),i.tokens}function ai(e,t,n,r,o){const i=li(n,o),{tokenizeMaxLineLength:l=0,tokenizeTimeLimit:s=500}=o,a=Ii(e);let u=o.grammarState?wy(o.grammarState,n.name)??vs:o.grammarContextCode!=null?ai(o.grammarContextCode,t,n,r,{...o,grammarState:void 0,grammarContextCode:void 0}).stateStack:vs,d=[];const c=[];for(let f=0,_=a.length;f<_;f++){const[v,x]=a[f];if(v===""){d=[],c.push([]);continue}if(l>0&&v.length>=l){d=[],c.push([{content:v,offset:x,color:"",fontStyle:0}]);continue}let k,g,h;o.includeExplanation&&(k=t.tokenizeLine(v,u),g=k.tokens,h=0);const y=t.tokenizeLine2(v,u,s),E=y.tokens.length/2;for(let S=0;S<E;S++){const C=y.tokens[2*S],R=S+1<E?y.tokens[2*S+2]:v.length;if(C===R)continue;const T=y.tokens[2*S+1],$=Yt(r[Bn.getForeground(T)],i),b=Bn.getFontStyle(T),I={content:v.substring(C,R),offset:x+C,color:$,fontStyle:b};if(o.includeExplanation){const ee=[];if(o.includeExplanation!=="scopeName")for(const Ve of n.settings){let St;switch(typeof Ve.scope){case"string":St=Ve.scope.split(/,/).map(Ft=>Ft.trim());break;case"object":St=Ve.scope;break;default:continue}ee.push({settings:Ve,selectors:St.map(Ft=>Ft.split(/ /))})}I.explanation=[];let Me=0;for(;C+Me<R;){const Ve=g[h],St=v.substring(Ve.startIndex,Ve.endIndex);Me+=St.length,I.explanation.push({content:St,scopes:o.includeExplanation==="scopeName"?jy(Ve.scopes):My(ee,Ve.scopes)}),h+=1}}d.push(I)}c.push(d),d=[],u=y.ruleStack}return{tokens:c,stateStack:u}}function jy(e){return e.map(t=>({scopeName:t}))}function My(e,t){const n=[];for(let r=0,o=t.length;r<o;r++){const i=t[r];n[r]={scopeName:i,themeMatches:zy(e,i,t.slice(0,r))}}return n}function fc(e,t){return e===t||t.substring(0,e.length)===e&&t[e.length]==="."}function Vy(e,t,n){if(!fc(e[e.length-1],t))return!1;let r=e.length-2,o=n.length-1;for(;r>=0&&o>=0;)fc(e[r],n[o])&&(r-=1),o-=1;return r===-1}function zy(e,t,n){const r=[];for(const{selectors:o,settings:i}of e)for(const l of o)if(Vy(l,t,n)){r.push(i);break}return r}function Ep(e,t,n){const r=Object.entries(n.themes).filter(a=>a[1]).map(a=>({color:a[0],theme:a[1]})),o=r.map(a=>{const u=Ra(e,t,{...n,theme:a.theme}),d=jr(u),c=typeof a.theme=="string"?a.theme:a.theme.name;return{tokens:u,state:d,theme:c}}),i=By(...o.map(a=>a.tokens)),l=i[0].map((a,u)=>a.map((d,c)=>{const f={content:d.content,variants:{},offset:d.offset};return"includeExplanation"in n&&n.includeExplanation&&(f.explanation=d.explanation),i.forEach((_,v)=>{const{content:x,explanation:k,offset:g,...h}=_[u][c];f.variants[r[v].color]=h}),f})),s=o[0].state?new Hn(Object.fromEntries(o.map(a=>{var u;return[a.theme,(u=a.state)==null?void 0:u.getInternalStack(a.theme)]})),o[0].state.lang):void 0;return s&&Oi(l,s),l}function By(...e){const t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){const o=e.map(a=>a[r]),i=t.map(()=>[]);t.forEach((a,u)=>a.push(i[u]));const l=o.map(()=>0),s=o.map(a=>a[0]);for(;s.every(a=>a);){const a=Math.min(...s.map(u=>u.content.length));for(let u=0;u<n;u++){const d=s[u];d.content.length===a?(i[u].push(d),l[u]+=1,s[u]=o[u][l[u]]):(i[u].push({...d,content:d.content.slice(0,a)}),s[u]={...d,content:d.content.slice(a),offset:d.offset+a})}}}return t}function ui(e,t,n){let r,o,i,l,s,a;if("themes"in n){const{defaultColor:u="light",cssVariablePrefix:d="--shiki-"}=n,c=Object.entries(n.themes).filter(k=>k[1]).map(k=>({color:k[0],theme:k[1]})).sort((k,g)=>k.color===u?-1:g.color===u?1:0);if(c.length===0)throw new ht("`themes` option must not be empty");const f=Ep(e,t,n);if(a=jr(f),u&&!c.find(k=>k.color===u))throw new ht(`\`themes\` option must contain the defaultColor key \`${u}\``);const _=c.map(k=>e.getTheme(k.theme)),v=c.map(k=>k.color);i=f.map(k=>k.map(g=>$y(g,v,d,u))),a&&Oi(i,a);const x=c.map(k=>li(k.theme,n));o=c.map((k,g)=>(g===0&&u?"":`${d+k.color}:`)+(Yt(_[g].fg,x[g])||"inherit")).join(";"),r=c.map((k,g)=>(g===0&&u?"":`${d+k.color}-bg:`)+(Yt(_[g].bg,x[g])||"inherit")).join(";"),l=`shiki-themes ${_.map(k=>k.name).join(" ")}`,s=u?void 0:[o,r].join(";")}else if("theme"in n){const u=li(n.theme,n);i=Ra(e,t,n);const d=e.getTheme(n.theme);r=Yt(d.bg,u),o=Yt(d.fg,u),l=d.name,a=jr(i)}else throw new ht("Invalid options, either `theme` or `themes` must be provided");return{tokens:i,fg:o,bg:r,themeName:l,rootStyle:s,grammarState:a}}function $y(e,t,n,r){const o={content:e.content,explanation:e.explanation,offset:e.offset},i=t.map(a=>vp(e.variants[a])),l=new Set(i.flatMap(a=>Object.keys(a))),s={};return i.forEach((a,u)=>{for(const d of l){const c=a[d]||"inherit";if(u===0&&r)s[d]=c;else{const f=d==="color"?"":d==="background-color"?"-bg":`-${d}`,_=n+t[u]+(d==="color"?"":f);s[_]=c}}}),o.htmlStyle=s,o}function ci(e,t,n,r={meta:{},options:n,codeToHast:(o,i)=>ci(e,o,i),codeToTokens:(o,i)=>ui(e,o,i)}){var _,v;let o=t;for(const x of si(n))o=((_=x.preprocess)==null?void 0:_.call(r,o,n))||o;let{tokens:i,fg:l,bg:s,themeName:a,rootStyle:u,grammarState:d}=ui(e,o,n);const{mergeWhitespaces:c=!0}=n;c===!0?i=Uy(i):c==="never"&&(i=Gy(i));const f={...r,get source(){return o}};for(const x of si(n))i=((v=x.tokens)==null?void 0:v.call(f,i))||i;return Fy(i,{...n,fg:l,bg:s,themeName:a,rootStyle:u},f,d)}function Fy(e,t,n,r=jr(e)){var v,x,k;const o=si(t),i=[],l={type:"root",children:[]},{structure:s="classic",tabindex:a="0"}=t;let u={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...a!==!1&&a!=null?{tabindex:a.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([g])=>!g.startsWith("_")))},children:[]},d={type:"element",tagName:"code",properties:{},children:i};const c=[],f={...n,structure:s,addClassToHast:_p,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return l},get pre(){return u},get code(){return d},get lines(){return c}};if(e.forEach((g,h)=>{var S,C;h&&(s==="inline"?l.children.push({type:"element",tagName:"br",properties:{},children:[]}):s==="classic"&&i.push({type:"text",value:`
`}));let y={type:"element",tagName:"span",properties:{class:"line"},children:[]},E=0;for(const R of g){let T={type:"element",tagName:"span",properties:{...R.htmlAttrs},children:[{type:"text",value:R.content}]};R.htmlStyle;const $=Sy(R.htmlStyle||vp(R));$&&(T.properties.style=$);for(const b of o)T=((S=b==null?void 0:b.span)==null?void 0:S.call(f,T,h+1,E,y,R))||T;s==="inline"?l.children.push(T):s==="classic"&&y.children.push(T),E+=R.content.length}if(s==="classic"){for(const R of o)y=((C=R==null?void 0:R.line)==null?void 0:C.call(f,y,h+1))||y;c.push(y),i.push(y)}}),s==="classic"){for(const g of o)d=((v=g==null?void 0:g.code)==null?void 0:v.call(f,d))||d;u.children.push(d);for(const g of o)u=((x=g==null?void 0:g.pre)==null?void 0:x.call(f,u))||u;l.children.push(u)}let _=l;for(const g of o)_=((k=g==null?void 0:g.root)==null?void 0:k.call(f,_))||_;return r&&Oi(_,r),_}function Uy(e){return e.map(t=>{const n=[];let r="",o=0;return t.forEach((i,l)=>{const a=!(i.fontStyle&&i.fontStyle&pt.Underline);a&&i.content.match(/^\s+$/)&&t[l+1]?(o||(o=i.offset),r+=i.content):r?(a?n.push({...i,offset:o,content:r+i.content}):n.push({content:r,offset:o},i),o=0,r=""):n.push(i)}),n})}function Gy(e){return e.map(t=>t.flatMap(n=>{if(n.content.match(/^\s+$/))return n;const r=n.content.match(/^(\s*)(.*?)(\s*)$/);if(!r)return n;const[,o,i,l]=r;if(!o&&!l)return n;const s=[{...n,offset:n.offset+o.length,content:i}];return o&&s.unshift({content:o,offset:n.offset}),l&&s.push({content:l,offset:n.offset+o.length+i.length}),s}))}function Hy(e,t,n){var i;const r={meta:{},options:n,codeToHast:(l,s)=>ci(e,l,s),codeToTokens:(l,s)=>ui(e,l,s)};let o=hy(ci(e,t,n,r));for(const l of si(n))o=((i=l.postprocess)==null?void 0:i.call(r,o,n))||o;return o}const pc={light:"#333333",dark:"#bbbbbb"},mc={light:"#fffffe",dark:"#1e1e1e"},hc="__shiki_resolved";function Ta(e){var s,a,u,d,c;if(e!=null&&e[hc])return e;const t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||(t.type="dark"),t.colorReplacements={...t.colorReplacements},t.settings||(t.settings=[]);let{bg:n,fg:r}=t;if(!n||!r){const f=t.settings?t.settings.find(_=>!_.name&&!_.scope):void 0;(s=f==null?void 0:f.settings)!=null&&s.foreground&&(r=f.settings.foreground),(a=f==null?void 0:f.settings)!=null&&a.background&&(n=f.settings.background),!r&&((u=t==null?void 0:t.colors)!=null&&u["editor.foreground"])&&(r=t.colors["editor.foreground"]),!n&&((d=t==null?void 0:t.colors)!=null&&d["editor.background"])&&(n=t.colors["editor.background"]),r||(r=t.type==="light"?pc.light:pc.dark),n||(n=t.type==="light"?mc.light:mc.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let o=0;const i=new Map;function l(f){var v;if(i.has(f))return i.get(f);o+=1;const _=`#${o.toString(16).padStart(8,"0").toLowerCase()}`;return(v=t.colorReplacements)!=null&&v[`#${_}`]?l(f):(i.set(f,_),_)}t.settings=t.settings.map(f=>{var k,g;const _=((k=f.settings)==null?void 0:k.foreground)&&!f.settings.foreground.startsWith("#"),v=((g=f.settings)==null?void 0:g.background)&&!f.settings.background.startsWith("#");if(!_&&!v)return f;const x={...f,settings:{...f.settings}};if(_){const h=l(f.settings.foreground);t.colorReplacements[h]=f.settings.foreground,x.settings.foreground=h}if(v){const h=l(f.settings.background);t.colorReplacements[h]=f.settings.background,x.settings.background=h}return x});for(const f of Object.keys(t.colors||{}))if((f==="editor.foreground"||f==="editor.background"||f.startsWith("terminal.ansi"))&&!((c=t.colors[f])!=null&&c.startsWith("#"))){const _=l(t.colors[f]);t.colorReplacements[_]=t.colors[f],t.colors[f]=_}return Object.defineProperty(t,hc,{enumerable:!1,writable:!1,value:!0}),t}async function wp(e){return Array.from(new Set((await Promise.all(e.filter(t=>!hp(t)).map(async t=>await yp(t).then(n=>Array.isArray(n)?n:[n])))).flat()))}async function kp(e){return(await Promise.all(e.map(async n=>gp(n)?null:Ta(await yp(n))))).filter(n=>!!n)}class Wy extends t_{constructor(n,r,o,i={}){super(n);w(this,"_resolvedThemes",new Map);w(this,"_resolvedGrammars",new Map);w(this,"_langMap",new Map);w(this,"_langGraph",new Map);w(this,"_textmateThemeCache",new WeakMap);w(this,"_loadedThemesCache",null);w(this,"_loadedLanguagesCache",null);this._resolver=n,this._themes=r,this._langs=o,this._alias=i,this._themes.map(l=>this.loadTheme(l)),this.loadLanguages(this._langs)}getTheme(n){return typeof n=="string"?this._resolvedThemes.get(n):this.loadTheme(n)}loadTheme(n){const r=Ta(n);return r.name&&(this._resolvedThemes.set(r.name,r),this._loadedThemesCache=null),r}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(n){let r=this._textmateThemeCache.get(n);r||(r=ti.createFromRawTheme(n),this._textmateThemeCache.set(n,r)),this._syncRegistry.setTheme(r)}getGrammar(n){if(this._alias[n]){const r=new Set([n]);for(;this._alias[n];){if(n=this._alias[n],r.has(n))throw new xe(`Circular alias \`${Array.from(r).join(" -> ")} -> ${n}\``);r.add(n)}}return this._resolvedGrammars.get(n)}loadLanguage(n){var l,s,a,u;if(this.getGrammar(n.name))return;const r=new Set([...this._langMap.values()].filter(d=>{var c;return(c=d.embeddedLangsLazy)==null?void 0:c.includes(n.name)}));this._resolver.addLanguage(n);const o={balancedBracketSelectors:n.balancedBracketSelectors||["*"],unbalancedBracketSelectors:n.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(n.scopeName,n);const i=this.loadGrammarWithConfiguration(n.scopeName,1,o);if(i.name=n.name,this._resolvedGrammars.set(n.name,i),n.aliases&&n.aliases.forEach(d=>{this._alias[d]=n.name}),this._loadedLanguagesCache=null,r.size)for(const d of r)this._resolvedGrammars.delete(d.name),this._loadedLanguagesCache=null,(s=(l=this._syncRegistry)==null?void 0:l._injectionGrammars)==null||s.delete(d.scopeName),(u=(a=this._syncRegistry)==null?void 0:a._grammars)==null||u.delete(d.scopeName),this.loadLanguage(this._langMap.get(d.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(n){for(const i of n)this.resolveEmbeddedLanguages(i);const r=Array.from(this._langGraph.entries()),o=r.filter(([i,l])=>!l);if(o.length){const i=r.filter(([l,s])=>{var a;return s&&((a=s.embeddedLangs)==null?void 0:a.some(u=>o.map(([d])=>d).includes(u)))}).filter(l=>!o.includes(l));throw new xe(`Missing languages ${o.map(([l])=>`\`${l}\``).join(", ")}, required by ${i.map(([l])=>`\`${l}\``).join(", ")}`)}for(const[i,l]of r)this._resolver.addLanguage(l);for(const[i,l]of r)this.loadLanguage(l)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(n){if(this._langMap.set(n.name,n),this._langGraph.set(n.name,n),n.embeddedLangs)for(const r of n.embeddedLangs)this._langGraph.set(r,this._langMap.get(r))}}class Qy{constructor(t,n){w(this,"_langs",new Map);w(this,"_scopeToLang",new Map);w(this,"_injections",new Map);w(this,"_onigLib");this._onigLib={createOnigScanner:r=>t.createScanner(r),createOnigString:r=>t.createString(r)},n.forEach(r=>this.addLanguage(r))}get onigLib(){return this._onigLib}getLangRegistration(t){return this._langs.get(t)}loadGrammar(t){return this._scopeToLang.get(t)}addLanguage(t){this._langs.set(t.name,t),t.aliases&&t.aliases.forEach(n=>{this._langs.set(n,t)}),this._scopeToLang.set(t.scopeName,t),t.injectTo&&t.injectTo.forEach(n=>{this._injections.get(n)||this._injections.set(n,[]),this._injections.get(n).push(t.scopeName)})}getInjections(t){const n=t.split(".");let r=[];for(let o=1;o<=n.length;o++){const i=n.slice(0,o).join(".");r=[...r,...this._injections.get(i)||[]]}return r}}let er=0;function Ky(e){er+=1,e.warnings!==!1&&er>=10&&er%10===0&&console.warn(`[Shiki] ${er} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let t=!1;if(!e.engine)throw new xe("`engine` option is required for synchronous mode");const n=(e.langs||[]).flat(1),r=(e.themes||[]).flat(1).map(Ta),o=new Qy(e.engine,n),i=new Wy(o,r,n,e.langAlias);let l;function s(h){k();const y=i.getGrammar(typeof h=="string"?h:h.name);if(!y)throw new xe(`Language \`${h}\` not found, you may need to load it first`);return y}function a(h){if(h==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};k();const y=i.getTheme(h);if(!y)throw new xe(`Theme \`${h}\` not found, you may need to load it first`);return y}function u(h){k();const y=a(h);l!==h&&(i.setTheme(y),l=h);const E=i.getColorMap();return{theme:y,colorMap:E}}function d(){return k(),i.getLoadedThemes()}function c(){return k(),i.getLoadedLanguages()}function f(...h){k(),i.loadLanguages(h.flat(1))}async function _(...h){return f(await wp(h))}function v(...h){k();for(const y of h.flat(1))i.loadTheme(y)}async function x(...h){return k(),v(await kp(h))}function k(){if(t)throw new xe("Shiki instance has been disposed")}function g(){t||(t=!0,i.dispose(),er-=1)}return{setTheme:u,getTheme:a,getLanguage:s,getLoadedThemes:d,getLoadedLanguages:c,loadLanguage:_,loadLanguageSync:f,loadTheme:x,loadThemeSync:v,dispose:g,[Symbol.dispose]:g}}async function qy(e={}){e.loadWasm;const[t,n,r]=await Promise.all([kp(e.themes||[]),wp(e.langs||[]),e.engine||Bf(e.loadWasm||h0())]);return Ky({...e,themes:t,langs:n,engine:r})}async function Yy(e={}){const t=await qy(e);return{getLastGrammarState:(...n)=>by(t,...n),codeToTokensBase:(n,r)=>Ra(t,n,r),codeToTokensWithThemes:(n,r)=>Ep(t,n,r),codeToTokens:(n,r)=>ui(t,n,r),codeToHast:(n,r)=>ci(t,n,r),codeToHtml:(n,r)=>Hy(t,n,r),...t,getInternalContext:()=>t}}function Xy(e,t,n){let r,o,i;{const s=e;r=s.langs,o=s.themes,i=s.engine}async function l(s){function a(_){if(typeof _=="string"){if(hp(_))return[];const v=r[_];if(!v)throw new ht(`Language \`${_}\` is not included in this bundle. You may want to load it from external source.`);return v}return _}function u(_){if(gp(_))return"none";if(typeof _=="string"){const v=o[_];if(!v)throw new ht(`Theme \`${_}\` is not included in this bundle. You may want to load it from external source.`);return v}return _}const d=(s.themes??[]).map(_=>u(_)),c=(s.langs??[]).map(_=>a(_)),f=await Yy({engine:s.engine??i(),...s,themes:d,langs:c});return{...f,loadLanguage(..._){return f.loadLanguage(..._.map(a))},loadTheme(..._){return f.loadTheme(..._.map(u))}}}return l}const Jy=Xy({langs:qg,themes:Xg,engine:()=>Bf(p(()=>import("./assets/wasm-CG6Dc4jp.js"),[],import.meta.url))}),Zy=({content:e})=>{const[t,n]=O.useState(null);if(O.useEffect(()=>{Jy({themes:["github-dark"],langs:["javascript","typescript","python","bash","html","css"]}).then(n)},[]),!t)return m.jsx("pre",{className:"whitespace-pre-wrap",children:e});const r=e.split(/(```[\s\S]*?```)/g);return m.jsx(m.Fragment,{children:r.map((o,i)=>{if(o.startsWith("```")){const l=o.split(`
`),s=l[0].replace("```","").trim(),a=l.slice(1,-1).join(`
`);return m.jsxs("div",{className:"relative",children:[m.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[m.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(a),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:m.jsx(Dg,{size:14})}),m.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:m.jsx(jg,{size:14})})]}),m.jsx("div",{dangerouslySetInnerHTML:{__html:t.codeToHtml(a,{lang:s,theme:"github-dark"})}})]},i)}return m.jsx("div",{children:o},i)})})},ev=({message:e})=>{const t=e.role==="user";return m.jsx("div",{className:`flex gap-3 ${t?"justify-end":"justify-start"} mb-4`,children:m.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${t?"bg-[#404040] text-adobe-text-primary ml-8 rounded-br-md":"bg-[#2a2a2a] text-adobe-text-primary mr-8 rounded-bl-md border border-[#3a3a3a]"}`,children:m.jsx("div",{className:"whitespace-pre-wrap",children:m.jsx(Zy,{content:e.content})})})})},tv="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201024%201024'%20fill='%23121212'%3e%3cpath%20d='M534.3%20164c-.1%202.5%201.1%203.7%203.6%203.6%202.2%200%204.3.1%206.5.1%2044.5%200%2089%20.2%20133.5%200%2048-.2%2088.8%2034.2%2097.3%2082.9%201.7%209.7%201.1%2019.6%201.1%2029.4%200%203%201%204.5%204%205.2%2021.8%204.5%2036%2019.1%2047.6%2036.7%2010.3%2015.5%2015.2%2033%2015.1%2051.4-.1%2022.4-7.2%2042.7-21.4%2060.4-7.8%209.7-17%2017.7-27.9%2023.7-3.7%202-7.5%203.6-11.7%204-4.4.4-5.7%202.5-5.7%206.9.2%2038%20.1%2076%20.1%20114%200%2011.3-4.7%2020-14.5%2025.5-9.5%205.4-19.2%204.7-28.4-1.1-12.9-8.2-25.2-17.4-38.5-25-14-8-28.7-12.4-45-12.4-100.3.2-200.6.4-301%200-34.5-.1-61.5-15.9-81.3-44-12-17-17-36.3-17-56.9%200-4.5-1.4-6.3-6-7.2-13.3-2.6-23.4-11.1-32.5-20.3-13.3-13.4-22.2-29.5-25.7-48.2-6.5-34.3%202.1-64.2%2027.2-88.9%209.3-9.1%2019.6-16.5%2032.9-18.4%203.3-.5%204-2.8%204-5.8-.1-10.8-.1-21.5%201.8-32.3%206.6-37.4%2038.2-70%2075.7-77.6%206.7-1.3%2013.4-2.1%2020.3-2%2045.8.1%2091.7.1%20137.5%200%206.7%200%206.7%200%206.7-6.5%200-8.7-.1-17.3.1-26%200-1.7-.2-3-.8-4.1-.6-1.1-1.6-2.1-3.2-2.9-19.6-10.6-29.3-27.7-28.9-49.4.6-26.8%2018.4-48.5%2046.5-52%2035.6-4.5%2063.9%2024%2060.8%2058.5-1.7%2019.1-11.5%2033.8-28.6%2042.9-1.7.9-2.8%201.9-3.5%203.1-.6%201.2-.8%202.6-.8%204.4M720%20244.4c-8.8-15.8-22.1-25.8-40.4-25.9-110.7-.3-221.3-.2-332-.1-23.9%200-45.3%2021.6-45.3%2045.5%200%2068.7%200%20137.3.1%20206%200%2025.8%2022%2047.7%2047.8%2047.7%2096.2%200%20192.3%200%20288.5.1%2010.1%200%2020.3-.5%2030.4.9%2013.8%201.9%2027.1%204.8%2039.4%2011.5%205.1%202.8%2010.1%205.6%2015.2%208.5%201.6-2.9.9-5.6.9-8.2%200-88.2%200-176.3.1-264.5%200-7.3-1.3-14.2-4.7-21.4M497.9%2071.5c-1.5%203.2-2.9%206.4-2.4%2010%201%208.6%208.4%2015.8%2016.4%2016.1%2010%20.5%2017.1-4.9%2019.5-14.8%201.7-7.4-3.3-15.9-11.4-19.4-7.6-3.2-16.7-.1-22%208.1z'/%3e%3cpath%20d='M665.2%20863.6c.6%208.2.5%2018-6.9%2023.4-10.8%208.2-25.2%203.9-37.6%204.9-64.3.1-128.7.3-193%200-11.4.4-21.8-9.8-20.3-21.4.3-15.5-.5-31.1.4-46.6%202.2-12.4%2015.9-16.4%2026.8-14.8%2071.3%200%20142.5-.2%20213.8.1%2011.1%201%2018.3%2012.4%2016.8%2022.9%200%2010.5%200%2020.9%200%2031.4zM426.3%20705.1c73.4.1%20146.8%200%20220.1%200%2011.7.4%2020.7%2012%2018.8%2023.4-.6%2014.4.5%2028.8-.4%2043.1-2%2013.2-16.4%2017.4-27.8%2015.7-70.7%200-141.5.2-212.2-.1-12-.8-19.1-13.4-17.3-24.5.2-13.8-.4-27.7.3-41.6%201.2-9.2%209.5-15.5%2018.5-16zM426.3%20600c73.4.1%20146.8%200%20220.1%200%2011.7.4%2020.7%2012%2018.8%2023.4-.6%2014.4.5%2028.8-.4%2043.1-2%2013.2-16.4%2017.4-27.8%2015.7-70.7%200-141.5.2-212.2-.1-12-.8-19.1-13.4-17.3-24.5.2-13.8-.4-27.7.3-41.6%201.2-9.2%209.5-15.5%2018.5-16zM309%20669.8c-9.1-10.4-14-25-10.2-38.6%204-14.8%2016.1-28%2031.6-30.9%2012.5-2.1%2026.5.5%2035.9%209.6%2012.5%2011.5%2017.9%2030.9%2011.2%2046.8-5.8%2014.9-20.7%2025.8-36.8%2026.2-11.9.6-23.6-4.4-31.7-13.1zM319.8%20887.5c-10.8-6-19.6-16.3-21.5-28.8-2.6-14.5%202.6-30.3%2014.1-39.8%2011.9-10.2%2029.8-13.1%2044-6%2016.5%207.8%2026.9%2027.1%2023.4%2045.2-3.1%2017.7-18.8%2033-37.1%2033.8-7.8.4-15.9-.7-22.9-4.4zM350.9%20706.6c16.2%204.9%2028.5%2020.3%2029.4%2037.2%201.2%2014.6-6.3%2029.4-18.7%2037.2-13.7%209.1-32.8%208.9-46.2-.5-14-9.2-20.5-27.4-16.6-43.5%203.5-15.9%2017.2-29%2033.3-31.6%206.3-1.2%2012.7-.5%2018.8%201.2z'/%3e%3cpath%20d='M547.7%20474c-23.5%208.4-50.3%207.8-72.7-3.4-21.2-9.9-38.2-28-47.8-49.2-5.2-7.6%204-18.2%2012-12.6%2014.2%207.7%2029%2014.7%2045%2017.8%2031.5%206.9%2065.4%202.6%2093.9-12.6%205.7-3.4%2013.6-7%2019.6-2.2%206.4%206.2-.8%2014.8-3.5%2021-10.4%2018.2-26.6%2033.6-46.5%2041.2z'/%3e%3cpath%20d='M595.3%20330.6c7.2-14.9%2024.4-23.9%2040.4-20.6%2016.3%203%2030.1%2017.9%2030.9%2035.1%201.1%2012.7-4.3%2026-14.4%2033.6-11.6%209.2-28.5%2010.7-41.3%203.1-12.6-6.9-20.6-21.5-19.6-36.1.3-5.2%201.7-10.4%204-15z'/%3e%3cpath%20d='M383.7%20376.7c-5.5%205-13.8%206.6-20.5%203.3-6.6-2.5-10-9.6-10.4-16.4-1.3-12.6%201.6-25.9%209.7-35.8%2010.5-13.5%2029.1-20.2%2045.7-16%2015%203.8%2028.8%2014.7%2033.3%2029.9%202.6%208.7%202.9%2018.1%201.1%2026.9-2.2%208.6-11.9%2014.1-20.5%2012-6.6-.9-12-6.3-13-12.8-1.1-5.3-.1-11.3-3.1-16.2-4.2-5-13.8-4.4-16.3%202.1-2.9%205.4.1%2011.6-2.5%2017.1-.8%202.1-2.1%204.1-3.5%205.9z'/%3e%3c/svg%3e",nv=()=>{const{messages:e,isLoading:t,currentSession:n}=Ni(),r=O.useRef(null),o=O.useRef(null),[i,l]=O.useState(!1),s=O.useRef();O.useEffect(()=>{var u;(u=r.current)==null||u.scrollIntoView({behavior:"smooth"})},[e,t]),O.useEffect(()=>{const u=o.current;if(!u)return;const d=()=>{clearTimeout(s.current);const{scrollTop:c,scrollHeight:f,clientHeight:_}=u,v=f-(c+_)<100;l(!v),s.current=setTimeout(()=>{l(!1)},2e3)};return u.addEventListener("scroll",d),()=>{u.removeEventListener("scroll",d),clearTimeout(s.current)}},[]);const a=()=>{var u;(u=r.current)==null||u.scrollIntoView({behavior:"smooth"})};return m.jsxs("div",{ref:o,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!n||e.length===0)&&m.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[m.jsx("div",{className:"w-20 h-20 mb-2 flex items-center justify-center",children:m.jsx("img",{src:tv,alt:"SahAI Logo",className:"w-20 h-20 brightness-0 invert"})}),m.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),m.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),e.map(u=>m.jsx(ev,{message:u},u.id)),t&&m.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:m.jsx("span",{children:"AI is thinking..."})}),m.jsx("div",{ref:r}),i&&m.jsx("button",{onClick:a,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:m.jsx(Ag,{size:18})})]})},rv=Rs.memo(({onAttachFile:e,onVoiceInput:t})=>{const[n,r]=O.useState(""),[o,i]=O.useState(!1),l=O.useRef(null),{addMessage:s,isLoading:a,setLoading:u,currentSession:d,createNewSession:c}=Ni(),f=4e3,_=!n.trim(),v=n.length>f*.9;O.useEffect(()=>{const y=l.current;y&&(y.style.height="72px")},[]);const x=O.useCallback(()=>{const y=l.current;y&&(y.style.height="auto",y.style.height=`${Math.min(Math.max(y.scrollHeight,72),200)}px`)},[]),k=O.useCallback(y=>{r(y.target.value),x()},[x]),g=O.useCallback(async()=>{const y=n.trim();if(!(!y||a)){r(""),l.current&&(l.current.style.height="72px");try{u(!0),d||c(),s({content:y,role:"user"}),setTimeout(()=>{s({content:`Echo: ${y}`,role:"assistant"}),u(!1)},1e3)}catch{r(y),u(!1)}}},[n,a,d,s,u,c]),h=O.useCallback(y=>{y.key==="Enter"&&!y.shiftKey&&!o&&(y.preventDefault(),g())},[g,o]);return m.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[m.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border border-adobe-text-secondary/50 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[m.jsx("div",{className:"flex items-center pl-3",children:m.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:a,children:m.jsx(zg,{size:18})})}),m.jsx("textarea",{ref:l,rows:3,maxLength:f,value:n,onChange:k,onKeyDown:h,onCompositionStart:()=>i(!0),onCompositionEnd:()=>i(!1),placeholder:"Type a message...",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            min-h-[72px] max-h-[200px] leading-relaxed overflow-y-auto chat-messages-scrollbar`}),m.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[m.jsx("button",{onClick:t,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:a,children:m.jsx(Vg,{size:18})}),m.jsx("button",{onClick:g,disabled:_||a,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:a?m.jsx(ei,{size:18,className:"animate-spin"}):m.jsx($g,{size:18})})]})]}),m.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[m.jsxs("span",{className:`text-xs ${v?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[n.length,"/",f]}),m.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),ov=({provider:e,size:t=16,className:n="",...r})=>{const o={width:t,height:t,viewBox:"0 0 24 24",fill:"currentColor",className:`provider-logo ${n}`,...r};switch(e){case"openai":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"})});case"anthropic":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"})});case"gemini":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"groq":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})});case"deepseek":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"})});case"mistral":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"moonshot":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"})});case"openrouter":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"})});case"perplexity":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"})});case"qwen":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"together":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"})});case"vertex":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"xai":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"})});case"ollama":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"})});case"lmstudio":return m.jsx("svg",{...o,children:m.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})});default:return m.jsx("svg",{...o,children:m.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}},yl=[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"gemini",label:"Google Gemini"},{value:"groq",label:"Groq"},{value:"deepseek",label:"DeepSeek"},{value:"mistral",label:"Mistral"},{value:"moonshot",label:"Moonshot AI"},{value:"openrouter",label:"OpenRouter"},{value:"perplexity",label:"Perplexity"},{value:"qwen",label:"Alibaba Qwen"},{value:"together",label:"Together AI"},{value:"vertex",label:"Google Vertex AI"},{value:"xai",label:"xAI"},{value:"ollama",label:"Ollama"},{value:"lmstudio",label:"LM Studio"}],iv=()=>{const{closeModal:e}=Fr(),{providers:t,saveProviderSelection:n,loadModelsForProvider:r}=Ti(),[o,i]=O.useState(""),[l,s]=O.useState(""),[a,u]=O.useState(""),[d,c]=O.useState(""),[f,_]=O.useState(""),[v,x]=O.useState(!1),[k,g]=O.useState(!1),h=O.useRef(null),y=O.useRef(null),E=yl.filter(I=>I.label.toLowerCase().includes(d.toLowerCase())),S=t.find(I=>I.id===o),C=(S==null?void 0:S.models)||[],R=C.filter(I=>I.name.toLowerCase().includes(f.toLowerCase()));yl.find(I=>I.value===o),O.useEffect(()=>{o&&a&&t.find(ee=>ee.id===o)&&(n(o,{apiKey:a}),r(o))},[o,a]);const T=I=>{var ee;i(I),s(""),_(""),c(((ee=yl.find(Me=>Me.value===I))==null?void 0:ee.label)||""),x(!1),a&&r(I)},$=I=>{s(I);const ee=C.find(Me=>Me.id===I);_((ee==null?void 0:ee.name)||""),g(!1)},b=()=>{o&&l&&a&&(n(o,{apiKey:a,selectedModelId:l}),e())};return O.useEffect(()=>{const I=ee=>{h.current&&!h.current.contains(ee.target)&&x(!1),y.current&&!y.current.contains(ee.target)&&g(!1)};return document.addEventListener("mousedown",I),()=>document.removeEventListener("mousedown",I)},[]),m.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:m.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl",children:[m.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"AI Provider Configuration"}),m.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]})}),m.jsxs("div",{className:"p-6 space-y-6",children:[m.jsxs("div",{className:"relative",ref:h,children:[m.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Provider"}),m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",value:d,onChange:I=>{c(I.target.value),x(!0)},onFocus:()=>x(!0),placeholder:"Search providers...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"}),m.jsx(fs,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18})]}),v&&m.jsx("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:E.map(I=>m.jsxs("div",{className:"px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>T(I.value),children:[m.jsx(ov,{provider:I.value,size:16}),m.jsx("span",{className:"font-medium",children:I.label})]},I.value))})]}),m.jsxs("div",{className:"relative",ref:y,children:[m.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Model"}),m.jsxs("div",{className:"relative",children:[m.jsx("input",{type:"text",value:f,onChange:I=>{_(I.target.value),g(!0)},onFocus:()=>o&&g(!0),placeholder:o?"Search models...":"Select a provider first",disabled:!o,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"}),m.jsx(fs,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18}),(S==null?void 0:S.isLoading)&&m.jsx(ei,{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary animate-spin",size:16})]}),k&&o&&m.jsxs("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:[(S==null?void 0:S.isLoading)&&C.length===0&&m.jsxs("div",{className:"px-4 py-3 text-adobe-text-secondary flex items-center space-x-2",children:[m.jsx(ei,{className:"animate-spin",size:16}),m.jsx("span",{children:"Loading models..."})]}),(S==null?void 0:S.error)&&C.length===0&&m.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:S.error}),R.map(I=>m.jsxs("div",{className:"px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>$(I.id),children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("span",{className:"font-medium",children:I.name}),I.isRecommended&&m.jsx("span",{className:"text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded",children:"Recommended"})]}),I.description&&m.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1",children:I.description})]},I.id))]})]}),m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"API Key"}),m.jsx("input",{type:"password",value:a,onChange:I=>u(I.target.value),placeholder:"Enter your API key",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"})]})]}),m.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[m.jsx("button",{onClick:e,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),m.jsx("button",{onClick:b,disabled:!o||!l||!a,className:"px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:"Save & Close"})]})]})})},lv=()=>{const{closeModal:e}=Fr(),[t,n]=O.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[r,o]=O.useState("settings"),[i,l]=O.useState(!1),[s,a]=O.useState(!0);O.useEffect(()=>{(async()=>{try{const f=await en.load();f.appSettings&&n(f.appSettings)}catch(f){console.error("Failed to load settings:",f)}finally{a(!1)}})()},[]);const u=[{id:"settings",title:"General Settings",description:"Configure application preferences",icon:m.jsx(Wu,{size:16,className:"text-adobe-accent"})},{id:"analytics",title:"Analytics",description:"View usage statistics and performance metrics",icon:m.jsx(Uu,{size:16,className:"text-adobe-accent"})},{id:"help",title:"Help & Support",description:"Get help and find answers to common questions",icon:m.jsx(bg,{size:16,className:"text-adobe-accent"})},{id:"about",title:"About",description:"Learn more about SahAI Extension",icon:m.jsx(Gu,{size:16,className:"text-adobe-accent"})}],d=async()=>{l(!0);try{const f={...await en.load(),appSettings:t};await en.save(f),e()}catch(c){console.error("Failed to save settings:",c)}finally{l(!1)}};return m.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:m.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[m.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),m.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:m.jsx(ps,{size:20})})]})}),m.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[m.jsx("div",{className:"w-1/3 border-r border-adobe-border bg-adobe-bg-secondary p-4 overflow-y-auto",children:m.jsx("div",{className:"space-y-1",children:u.map(c=>m.jsx("button",{onClick:()=>o(c.id),className:`w-full text-left p-3 rounded-md transition-colors ${r===c.id?"bg-adobe-accent/10 text-adobe-text-primary":"text-adobe-text-secondary hover:bg-adobe-bg-tertiary"}`,children:m.jsxs("div",{className:"flex items-center gap-3",children:[m.jsx("div",{className:"p-1.5 rounded-md bg-adobe-bg-tertiary",children:c.icon}),m.jsxs("div",{children:[m.jsx("div",{className:"font-medium text-sm",children:c.title}),m.jsx("div",{className:"text-xs mt-1",children:c.description})]})]})},c.id))})}),m.jsxs("div",{className:"w-2/3 p-6 overflow-y-auto",children:[s?m.jsx("div",{className:"flex items-center justify-center h-full",children:m.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):r==="settings"&&m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),m.jsx("div",{className:"space-y-3",children:m.jsxs("div",{children:[m.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),m.jsxs("div",{className:"relative",children:[m.jsxs("select",{value:t.theme,onChange:c=>n(f=>({...f,theme:c.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-8 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none appearance-none",children:[m.jsx("option",{value:"auto",children:"Auto (System)"}),m.jsx("option",{value:"light",children:"Light"}),m.jsx("option",{value:"dark",children:"Dark"})]}),m.jsx(xa,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]})]})})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),m.jsxs("div",{className:"space-y-3",children:[m.jsxs("label",{className:"flex items-center gap-3",children:[m.jsx("input",{type:"checkbox",checked:t.autoSave,onChange:c=>n(f=>({...f,autoSave:c.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),m.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),m.jsxs("label",{className:"flex items-center gap-3",children:[m.jsx("input",{type:"checkbox",checked:t.showNotifications,onChange:c=>n(f=>({...f,showNotifications:c.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),m.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),m.jsxs("div",{children:[m.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",t.maxHistoryItems,")"]}),m.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:t.maxHistoryItems,onChange:c=>n(f=>({...f,maxHistoryItems:parseInt(c.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),m.jsx("div",{className:"space-y-3",children:m.jsxs("label",{className:"flex items-center gap-3",children:[m.jsx("input",{type:"checkbox",checked:t.debugMode,onChange:c=>n(f=>({...f,debugMode:c.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),m.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]})]}),r==="analytics"&&m.jsx("div",{className:"flex items-center justify-center h-full text-adobe-text-secondary",children:m.jsxs("div",{className:"text-center",children:[m.jsx(Uu,{size:48,className:"mx-auto mb-4 opacity-50"}),m.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Analytics"}),m.jsx("p",{className:"text-sm",children:"Usage statistics and performance metrics will appear here"})]})}),r==="help"&&m.jsxs("div",{className:"space-y-4",children:[m.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"Help & Support"}),m.jsxs("div",{className:"space-y-3",children:[m.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[m.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Documentation"}),m.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Read our comprehensive documentation for detailed guides."})]}),m.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[m.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"FAQ"}),m.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Find answers to frequently asked questions."})]}),m.jsxs("div",{className:"p-4 bg-adobe-bg-secondary rounded-md",children:[m.jsx("h4",{className:"font-medium text-adobe-text-primary mb-2",children:"Contact Support"}),m.jsx("p",{className:"text-sm text-adobe-text-secondary",children:"Email <NAME_EMAIL> for assistance."})]})]})]}),r==="about"&&m.jsx("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary",children:m.jsxs("div",{className:"text-center",children:[m.jsx(Gu,{size:48,className:"mx-auto mb-4 opacity-50"}),m.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary mb-2",children:"About SahAI"}),m.jsx("p",{className:"text-sm mb-4",children:"Version 2.0.0"}),m.jsx("p",{className:"text-sm",children:"AI-powered assistant for Adobe Creative Suite"})]})})]})]}),m.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[m.jsxs("div",{className:"text-xs text-adobe-text-secondary",children:[r==="settings"&&"Application Preferences",r==="analytics"&&"Usage Statistics",r==="help"&&"Help Resources",r==="about"&&"About SahAI"]}),r==="settings"&&m.jsxs("button",{onClick:d,disabled:i,className:"flex items-center gap-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[m.jsx(Wu,{size:16}),m.jsx("span",{children:i?"Saving...":"Save Settings"})]})]})]})})},sv=Ri((e,t)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{e({isLoading:!0,error:null});try{const n=await ft("loadHistory()");if(n.success&&n.data){const r=Array.isArray(n.data)?n.data:[];e({sessions:r,isLoading:!1})}else e({sessions:[],isLoading:!1})}catch(n){console.error("Failed to load history:",n),e({error:n.message||"Failed to load chat history",isLoading:!1,sessions:[]})}},saveSession:async n=>{try{e(o=>({sessions:o.sessions.some(i=>i.id===n.id)?o.sessions.map(i=>i.id===n.id?n:i):[...o.sessions,n]}));const r=t().sessions;await ft(`saveHistory(${JSON.stringify(r)})`)}catch(r){console.error("Failed to save session:",r),e({error:r.message||"Failed to save session"})}},deleteSession:async n=>{try{e(o=>({sessions:o.sessions.filter(i=>i.id!==n),currentSessionId:o.currentSessionId===n?null:o.currentSessionId}));const r=t().sessions;await ft(`saveHistory(${JSON.stringify(r)})`)}catch(r){console.error("Failed to delete session:",r),e({error:r.message||"Failed to delete session"})}},clearHistory:async()=>{try{e({sessions:[],currentSessionId:null}),await ft("saveHistory([])")}catch(n){console.error("Failed to clear history:",n),e({error:n.message||"Failed to clear history"})}},createSession:n=>{const r={id:crypto.randomUUID(),title:n||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};return e(o=>({sessions:[r,...o.sessions],currentSessionId:r.id})),r},updateSession:(n,r)=>{e(o=>({sessions:o.sessions.map(i=>i.id===n?{...i,...r,updatedAt:Date.now()}:i)}))},setCurrentSession:n=>{e({currentSessionId:n})},getCurrentSession:()=>{const{sessions:n,currentSessionId:r}=t();return n.find(o=>o.id===r)||null},getSessionById:n=>{const{sessions:r}=t();return r.find(o=>o.id===n)||null},getSortedSessions:()=>{const{sessions:n}=t();return[...n].sort((r,o)=>o.updatedAt-r.updatedAt)}})),av=()=>{const{closeModal:e}=Fr(),{sessions:t,isLoading:n,error:r,loadHistory:o,deleteSession:i,clearHistory:l,setCurrentSession:s,getSortedSessions:a}=sv(),{messages:u}=Ni(),[d,c]=O.useState(""),[f,_]=O.useState(null),[v,x]=O.useState("recent");O.useEffect(()=>{o()},[o]);const k=a().filter(S=>S.title.toLowerCase().includes(d.toLowerCase())||S.messages.some(C=>C.content.toLowerCase().includes(d.toLowerCase()))).sort((S,C)=>v==="alphabetical"?S.title.localeCompare(C.title):v==="oldest"?S.createdAt-C.createdAt:C.createdAt-S.createdAt),g=async(S,C)=>{C.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await i(S)},h=async()=>{confirm("Are you sure you want to delete all chat history? This action cannot be undone.")&&await l()},y=S=>{const C=new Date(S),T=(new Date().getTime()-C.getTime())/(1e3*60*60);return T<24?C.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):T<24*7?C.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):C.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},E=S=>{const C=S.messages[S.messages.length-1];if(!C)return"No messages";const R=C.content.slice(0,100);return R.length<C.content.length?`${R}...`:R};return m.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:m.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[m.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),m.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:m.jsx(ps,{size:20})})]})}),m.jsx("div",{className:"p-4 border-b border-adobe-border",children:m.jsxs("div",{className:"flex items-center gap-3",children:[m.jsxs("div",{className:"relative flex-1",children:[m.jsx("input",{type:"text",value:d,onChange:S=>c(S.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pr-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none text-sm"}),m.jsx("button",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary hover:text-adobe-text-primary",onClick:()=>c(""),children:d?m.jsx(ps,{size:16}):m.jsx(fs,{size:16})})]}),m.jsxs("div",{className:"relative",children:[m.jsxs("select",{value:v,onChange:S=>x(S.target.value),className:"appearance-none bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 pl-3 pr-8 text-adobe-text-primary text-sm focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none cursor-pointer",children:[m.jsx("option",{value:"recent",children:"Most Recent"}),m.jsx("option",{value:"oldest",children:"Oldest First"}),m.jsx("option",{value:"alphabetical",children:"Alphabetical"})]}),m.jsx(xa,{size:16,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary pointer-events-none"})]}),m.jsxs("button",{onClick:h,disabled:t.length===0,className:"px-3 py-2 bg-adobe-error text-white rounded-md hover:bg-opacity-90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm",children:[m.jsx(Qu,{size:16}),m.jsx("span",{children:"Clear All"})]})]})}),m.jsx("div",{className:"flex-1 overflow-hidden",children:n?m.jsx("div",{className:"flex items-center justify-center h-full",children:m.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-adobe-accent"})}):r?m.jsxs("div",{className:"p-4 text-center text-adobe-error",children:[m.jsx("p",{children:"Error loading history:"}),m.jsx("p",{className:"text-sm",children:r})]}):k.length===0?m.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-2",children:[m.jsx(Hu,{size:48,className:"opacity-50"}),m.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:d?"No matching sessions found":"No chat history yet"}),m.jsx("p",{className:"text-sm",children:d?"Try a different search term":"Start a new conversation to see it here"})]}):m.jsx("div",{className:"h-full overflow-y-auto p-2 space-y-2",children:k.map(S=>m.jsxs("div",{className:`p-3 rounded-md cursor-pointer transition-colors ${(f==null?void 0:f.id)===S.id?"bg-adobe-accent/10 border-l-2 border-adobe-accent":"bg-adobe-bg-secondary hover:bg-adobe-bg-tertiary"}`,onClick:()=>_(S),children:[m.jsxs("div",{className:"flex justify-between items-start gap-2",children:[m.jsxs("div",{className:"flex-1 min-w-0",children:[m.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:S.title}),m.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:E(S)})]}),m.jsxs("div",{className:"flex items-center gap-3",children:[m.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[m.jsx(Hu,{size:12}),m.jsx("span",{children:S.messages.length})]}),m.jsx("button",{onClick:C=>g(S.id,C),className:"text-adobe-text-secondary hover:text-adobe-error transition-colors p-1",title:"Delete session",children:m.jsx(Qu,{size:14})})]})]}),m.jsxs("div",{className:"flex justify-between items-center mt-2",children:[m.jsxs("div",{className:"flex items-center gap-1 text-xs text-adobe-text-secondary",children:[m.jsx(Ig,{size:12}),m.jsx("span",{children:y(S.createdAt)})]}),S.provider&&m.jsx("span",{className:"text-xs px-2 py-1 rounded-full bg-adobe-success/10 text-adobe-success",children:S.provider})]})]},S.id))})}),m.jsxs("div",{className:"p-3 border-t border-adobe-border text-xs text-adobe-text-secondary text-right",children:["Showing ",k.length," of ",t.length," chat sessions"]})]})})},uv=()=>{const{modal:e}=Fr();if(!e)return null;switch(e){case"provider":return m.jsx(iv,{});case"settings":return m.jsx(lv,{});case"chat-history":return m.jsx(av,{});default:return null}},cv=()=>m.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[m.jsx(Wg,{}),m.jsx(nv,{}),m.jsx(rv,{}),m.jsx(uv,{})]});Df();Ti.getState().loadSettings();vl.createRoot(document.getElementById("root")).render(m.jsx(Rs.StrictMode,{children:m.jsx(cv,{})}));
