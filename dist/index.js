const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./assets/angular-html-LfdN0zeE.js","./assets/html-C2L_23MC.js","./assets/javascript-ySlJ1b_l.js","./assets/css-BPhBrDlE.js","./assets/angular-ts-CKsD7JZE.js","./assets/scss-C31hgJw-.js","./assets/apl-BBq3IX1j.js","./assets/xml-e3z08dGr.js","./assets/java-xI-RfyKK.js","./assets/json-BQoSv7ci.js","./assets/astro-CqkE3fuf.js","./assets/typescript-Dj6nwHGl.js","./assets/postcss-B3ZDOciz.js","./assets/blade-a8OxSdnT.js","./assets/sql-COK4E0Yg.js","./assets/bsl-Dgyn0ogV.js","./assets/sdbl-BLhTXw86.js","./assets/cairo--RitsXJZ.js","./assets/python-DhUJRlN_.js","./assets/cobol-PTqiYgYu.js","./assets/coffee-dyiR41kL.js","./assets/cpp-BksuvNSY.js","./assets/regexp-DWJ3fJO_.js","./assets/glsl-DBO2IWDn.js","./assets/c-C3t2pwGQ.js","./assets/crystal-DtDmRg-F.js","./assets/shellscript-atvbtKCR.js","./assets/edge-D5gP-w-T.js","./assets/html-derivative-CSfWNPLT.js","./assets/elixir-CLiX3zqd.js","./assets/elm-CmHSxxaM.js","./assets/erb-BYTLMnw6.js","./assets/ruby-DeZ3UC14.js","./assets/haml-B2EZWmdv.js","./assets/graphql-cDcHW_If.js","./assets/jsx-BAng5TT0.js","./assets/tsx-B6W0miNI.js","./assets/lua-CvWAzNxB.js","./assets/yaml-CVw76BM1.js","./assets/fortran-fixed-form-TqA4NnZg.js","./assets/fortran-free-form-DKXYxT9g.js","./assets/fsharp-XplgxFYe.js","./assets/markdown-UIAJJxZW.js","./assets/gdresource-BHYsBjWJ.js","./assets/gdshader-SKMF96pI.js","./assets/gdscript-DfxzS6Rs.js","./assets/git-commit-i4q6IMui.js","./assets/diff-BgYniUM_.js","./assets/git-rebase-B-v9cOL2.js","./assets/glimmer-js-D-cwc0-E.js","./assets/glimmer-ts-pgjy16dm.js","./assets/hack-D1yCygmZ.js","./assets/handlebars-BQGss363.js","./assets/http-FRrOvY1W.js","./assets/hxml-TIA70rKU.js","./assets/haxe-C5wWYbrZ.js","./assets/imba-bv_oIlVt.js","./assets/jinja-DGy0s7-h.js","./assets/jison-BqZprYcd.js","./assets/julia-BBuGR-5E.js","./assets/r-CwjWoCRV.js","./assets/latex-C-cWTeAZ.js","./assets/tex-rYs2v40G.js","./assets/liquid-D3W5UaiH.js","./assets/marko-z0MBrx5-.js","./assets/less-BfCpw3nA.js","./assets/mdc-DB_EDNY_.js","./assets/nginx-D_VnBJ67.js","./assets/nim-ZlGxZxc3.js","./assets/perl-CHQXSrWU.js","./assets/php-B5ebYQev.js","./assets/pug-CM9l7STV.js","./assets/qml-D8XfuvdV.js","./assets/razor-CNLDkMZG.js","./assets/csharp-D9R-vmeu.js","./assets/rst-4NLicBqY.js","./assets/cmake-DbXoA79R.js","./assets/sas-BmTFh92c.js","./assets/shaderlab-B7qAK45m.js","./assets/hlsl-ifBTmRxC.js","./assets/shellsession-C_rIy8kc.js","./assets/soy-C-lX7w71.js","./assets/sparql-bYkjHRlG.js","./assets/turtle-BMR_PYu6.js","./assets/stata-DorPZHa4.js","./assets/svelte-MSaWC3Je.js","./assets/templ-dwX3ZSMB.js","./assets/go-B1SYOhNW.js","./assets/ts-tags-CipyTH0X.js","./assets/twig-NC5TFiHP.js","./assets/vue-BuYVFjOK.js","./assets/vue-html-xdeiXROB.js","./assets/xsl-Dd0NUgwM.js"])))=>i.map(i=>d[i]);
var Sp=Object.defineProperty;var Ep=(e,t,n)=>t in e?Sp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var E=(e,t,n)=>Ep(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function dc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var fc={exports:{}},di={},pc={exports:{}},V={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vr=Symbol.for("react.element"),wp=Symbol.for("react.portal"),kp=Symbol.for("react.fragment"),Cp=Symbol.for("react.strict_mode"),Lp=Symbol.for("react.profiler"),Pp=Symbol.for("react.provider"),Rp=Symbol.for("react.context"),Tp=Symbol.for("react.forward_ref"),Np=Symbol.for("react.suspense"),Ip=Symbol.for("react.memo"),Ap=Symbol.for("react.lazy"),Ta=Symbol.iterator;function Op(e){return e===null||typeof e!="object"?null:(e=Ta&&e[Ta]||e["@@iterator"],typeof e=="function"?e:null)}var mc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},hc=Object.assign,gc={};function $n(e,t,n){this.props=e,this.context=t,this.refs=gc,this.updater=n||mc}$n.prototype.isReactComponent={};$n.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};$n.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function _c(){}_c.prototype=$n.prototype;function ks(e,t,n){this.props=e,this.context=t,this.refs=gc,this.updater=n||mc}var Cs=ks.prototype=new _c;Cs.constructor=ks;hc(Cs,$n.prototype);Cs.isPureReactComponent=!0;var Na=Array.isArray,yc=Object.prototype.hasOwnProperty,Ls={current:null},vc={key:!0,ref:!0,__self:!0,__source:!0};function xc(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)yc.call(t,r)&&!vc.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:Vr,type:e,key:i,ref:l,props:o,_owner:Ls.current}}function bp(e,t){return{$$typeof:Vr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ps(e){return typeof e=="object"&&e!==null&&e.$$typeof===Vr}function Dp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ia=/\/+/g;function ji(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Dp(""+e.key):t.toString(36)}function _o(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Vr:case wp:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+ji(l,0):r,Na(o)?(n="",e!=null&&(n=e.replace(Ia,"$&/")+"/"),_o(o,t,n,"",function(u){return u})):o!=null&&(Ps(o)&&(o=bp(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Ia,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Na(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+ji(i,s);l+=_o(i,t,n,a,o)}else if(a=Op(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+ji(i,s++),l+=_o(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function qr(e,t,n){if(e==null)return e;var r=[],o=0;return _o(e,r,"","",function(i){return t.call(n,i,o++)}),r}function jp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ee={current:null},yo={transition:null},Mp={ReactCurrentDispatcher:Ee,ReactCurrentBatchConfig:yo,ReactCurrentOwner:Ls};function Sc(){throw Error("act(...) is not supported in production builds of React.")}V.Children={map:qr,forEach:function(e,t,n){qr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return qr(e,function(){t++}),t},toArray:function(e){return qr(e,function(t){return t})||[]},only:function(e){if(!Ps(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=$n;V.Fragment=kp;V.Profiler=Lp;V.PureComponent=ks;V.StrictMode=Cp;V.Suspense=Np;V.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mp;V.act=Sc;V.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=hc({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Ls.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)yc.call(t,a)&&!vc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:Vr,type:e.type,key:o,ref:i,props:r,_owner:l}};V.createContext=function(e){return e={$$typeof:Rp,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Pp,_context:e},e.Consumer=e};V.createElement=xc;V.createFactory=function(e){var t=xc.bind(null,e);return t.type=e,t};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:Tp,render:e}};V.isValidElement=Ps;V.lazy=function(e){return{$$typeof:Ap,_payload:{_status:-1,_result:e},_init:jp}};V.memo=function(e,t){return{$$typeof:Ip,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=yo.transition;yo.transition={};try{e()}finally{yo.transition=t}};V.unstable_act=Sc;V.useCallback=function(e,t){return Ee.current.useCallback(e,t)};V.useContext=function(e){return Ee.current.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e){return Ee.current.useDeferredValue(e)};V.useEffect=function(e,t){return Ee.current.useEffect(e,t)};V.useId=function(){return Ee.current.useId()};V.useImperativeHandle=function(e,t,n){return Ee.current.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Ee.current.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Ee.current.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Ee.current.useMemo(e,t)};V.useReducer=function(e,t,n){return Ee.current.useReducer(e,t,n)};V.useRef=function(e){return Ee.current.useRef(e)};V.useState=function(e){return Ee.current.useState(e)};V.useSyncExternalStore=function(e,t,n){return Ee.current.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Ee.current.useTransition()};V.version="18.3.1";pc.exports=V;var O=pc.exports;const Rs=dc(O);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vp=O,zp=Symbol.for("react.element"),Bp=Symbol.for("react.fragment"),$p=Object.prototype.hasOwnProperty,Fp=Vp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Up={key:!0,ref:!0,__self:!0,__source:!0};function Ec(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)$p.call(t,r)&&!Up.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:zp,type:e,key:i,ref:l,props:o,_owner:Fp.current}}di.Fragment=Bp;di.jsx=Ec;di.jsxs=Ec;fc.exports=di;var _=fc.exports,vl={},wc={exports:{}},De={},kc={exports:{}},Cc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,D){var M=I.length;I.push(D);e:for(;0<M;){var X=M-1>>>1,se=I[X];if(0<o(se,D))I[X]=D,I[M]=se,M=X;else break e}}function n(I){return I.length===0?null:I[0]}function r(I){if(I.length===0)return null;var D=I[0],M=I.pop();if(M!==D){I[0]=M;e:for(var X=0,se=I.length,Qr=se>>>1;X<Qr;){var Gt=2*(X+1)-1,Di=I[Gt],Ht=Gt+1,Kr=I[Ht];if(0>o(Di,M))Ht<se&&0>o(Kr,Di)?(I[X]=Kr,I[Ht]=M,X=Ht):(I[X]=Di,I[Gt]=M,X=Gt);else if(Ht<se&&0>o(Kr,M))I[X]=Kr,I[Ht]=M,X=Ht;else break e}}return D}function o(I,D){var M=I.sortIndex-D.sortIndex;return M!==0?M:I.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],c=1,p=null,d=3,h=!1,v=!1,S=!1,k=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,m=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(I){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=I)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function x(I){if(S=!1,y(I),!v)if(n(a)!==null)v=!0,Ut(w);else{var D=n(u);D!==null&&bi(x,D.startTime-I)}}function w(I,D){v=!1,S&&(S=!1,g(N),N=-1),h=!0;var M=d;try{for(y(D),p=n(a);p!==null&&(!(p.expirationTime>D)||I&&!A());){var X=p.callback;if(typeof X=="function"){p.callback=null,d=p.priorityLevel;var se=X(p.expirationTime<=D);D=e.unstable_now(),typeof se=="function"?p.callback=se:p===n(a)&&r(a),y(D)}else r(a);p=n(a)}if(p!==null)var Qr=!0;else{var Gt=n(u);Gt!==null&&bi(x,Gt.startTime-D),Qr=!1}return Qr}finally{p=null,d=M,h=!1}}var P=!1,R=null,N=-1,$=5,b=-1;function A(){return!(e.unstable_now()-b<$)}function Z(){if(R!==null){var I=e.unstable_now();b=I;var D=!0;try{D=R(!0,I)}finally{D?Me():(P=!1,R=null)}}else P=!1}var Me;if(typeof m=="function")Me=function(){m(Z)};else if(typeof MessageChannel<"u"){var Ve=new MessageChannel,St=Ve.port2;Ve.port1.onmessage=Z,Me=function(){St.postMessage(null)}}else Me=function(){k(Z,0)};function Ut(I){R=I,P||(P=!0,Me())}function bi(I,D){N=k(function(){I(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){v||h||(v=!0,Ut(w))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(I){switch(d){case 1:case 2:case 3:var D=3;break;default:D=d}var M=d;d=D;try{return I()}finally{d=M}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,D){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var M=d;d=I;try{return D()}finally{d=M}},e.unstable_scheduleCallback=function(I,D,M){var X=e.unstable_now();switch(typeof M=="object"&&M!==null?(M=M.delay,M=typeof M=="number"&&0<M?X+M:X):M=X,I){case 1:var se=-1;break;case 2:se=250;break;case 5:se=**********;break;case 4:se=1e4;break;default:se=5e3}return se=M+se,I={id:c++,callback:D,priorityLevel:I,startTime:M,expirationTime:se,sortIndex:-1},M>X?(I.sortIndex=M,t(u,I),n(a)===null&&I===n(u)&&(S?(g(N),N=-1):S=!0,bi(x,M-X))):(I.sortIndex=se,t(a,I),v||h||(v=!0,Ut(w))),I},e.unstable_shouldYield=A,e.unstable_wrapCallback=function(I){var D=d;return function(){var M=d;d=D;try{return I.apply(this,arguments)}finally{d=M}}}})(Cc);kc.exports=Cc;var Gp=kc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hp=O,be=Gp;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Lc=new Set,gr={};function sn(e,t){An(e,t),An(e+"Capture",t)}function An(e,t){for(gr[e]=t,e=0;e<t.length;e++)Lc.add(t[e])}var gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),xl=Object.prototype.hasOwnProperty,Wp=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Aa={},Oa={};function Qp(e){return xl.call(Oa,e)?!0:xl.call(Aa,e)?!1:Wp.test(e)?Oa[e]=!0:(Aa[e]=!0,!1)}function Kp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function qp(e,t,n,r){if(t===null||typeof t>"u"||Kp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){pe[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];pe[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){pe[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){pe[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){pe[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){pe[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){pe[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){pe[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){pe[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ts=/[\-:]([a-z])/g;function Ns(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ts,Ns);pe[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){pe[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});pe.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){pe[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Is(e,t,n,r){var o=pe.hasOwnProperty(t)?pe[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(qp(t,n,o,r)&&(n=null),r||o===null?Qp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=Hp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Yr=Symbol.for("react.element"),dn=Symbol.for("react.portal"),fn=Symbol.for("react.fragment"),As=Symbol.for("react.strict_mode"),Sl=Symbol.for("react.profiler"),Pc=Symbol.for("react.provider"),Rc=Symbol.for("react.context"),Os=Symbol.for("react.forward_ref"),El=Symbol.for("react.suspense"),wl=Symbol.for("react.suspense_list"),bs=Symbol.for("react.memo"),wt=Symbol.for("react.lazy"),Tc=Symbol.for("react.offscreen"),ba=Symbol.iterator;function Wn(e){return e===null||typeof e!="object"?null:(e=ba&&e[ba]||e["@@iterator"],typeof e=="function"?e:null)}var q=Object.assign,Mi;function tr(e){if(Mi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Mi=t&&t[1]||""}return`
`+Mi+e}var Vi=!1;function zi(e,t){if(!e||Vi)return"";Vi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Vi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?tr(e):""}function Yp(e){switch(e.tag){case 5:return tr(e.type);case 16:return tr("Lazy");case 13:return tr("Suspense");case 19:return tr("SuspenseList");case 0:case 2:case 15:return e=zi(e.type,!1),e;case 11:return e=zi(e.type.render,!1),e;case 1:return e=zi(e.type,!0),e;default:return""}}function kl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case fn:return"Fragment";case dn:return"Portal";case Sl:return"Profiler";case As:return"StrictMode";case El:return"Suspense";case wl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Rc:return(e.displayName||"Context")+".Consumer";case Pc:return(e._context.displayName||"Context")+".Provider";case Os:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case bs:return t=e.displayName||null,t!==null?t:kl(e.type)||"Memo";case wt:t=e._payload,e=e._init;try{return kl(e(t))}catch{}}return null}function Xp(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return kl(t);case 8:return t===As?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Vt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Nc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jp(e){var t=Nc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Xr(e){e._valueTracker||(e._valueTracker=Jp(e))}function Ic(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Nc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Io(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Cl(e,t){var n=t.checked;return q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Da(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Vt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ac(e,t){t=t.checked,t!=null&&Is(e,"checked",t,!1)}function Ll(e,t){Ac(e,t);var n=Vt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Pl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Pl(e,t.type,Vt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ja(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Pl(e,t,n){(t!=="number"||Io(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var nr=Array.isArray;function wn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Vt(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Rl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ma(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(nr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Vt(n)}}function Oc(e,t){var n=Vt(t.value),r=Vt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Va(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function bc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Tl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?bc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Jr,Dc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Jr=Jr||document.createElement("div"),Jr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Jr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var lr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Zp=["Webkit","ms","Moz","O"];Object.keys(lr).forEach(function(e){Zp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),lr[t]=lr[e]})});function jc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||lr.hasOwnProperty(e)&&lr[e]?(""+t).trim():t+"px"}function Mc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=jc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var em=q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Nl(e,t){if(t){if(em[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function Il(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Al=null;function Ds(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ol=null,kn=null,Cn=null;function za(e){if(e=$r(e)){if(typeof Ol!="function")throw Error(C(280));var t=e.stateNode;t&&(t=gi(t),Ol(e.stateNode,e.type,t))}}function Vc(e){kn?Cn?Cn.push(e):Cn=[e]:kn=e}function zc(){if(kn){var e=kn,t=Cn;if(Cn=kn=null,za(e),t)for(e=0;e<t.length;e++)za(t[e])}}function Bc(e,t){return e(t)}function $c(){}var Bi=!1;function Fc(e,t,n){if(Bi)return e(t,n);Bi=!0;try{return Bc(e,t,n)}finally{Bi=!1,(kn!==null||Cn!==null)&&($c(),zc())}}function yr(e,t){var n=e.stateNode;if(n===null)return null;var r=gi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var bl=!1;if(gt)try{var Qn={};Object.defineProperty(Qn,"passive",{get:function(){bl=!0}}),window.addEventListener("test",Qn,Qn),window.removeEventListener("test",Qn,Qn)}catch{bl=!1}function tm(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var sr=!1,Ao=null,Oo=!1,Dl=null,nm={onError:function(e){sr=!0,Ao=e}};function rm(e,t,n,r,o,i,l,s,a){sr=!1,Ao=null,tm.apply(nm,arguments)}function om(e,t,n,r,o,i,l,s,a){if(rm.apply(this,arguments),sr){if(sr){var u=Ao;sr=!1,Ao=null}else throw Error(C(198));Oo||(Oo=!0,Dl=u)}}function an(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Uc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ba(e){if(an(e)!==e)throw Error(C(188))}function im(e){var t=e.alternate;if(!t){if(t=an(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Ba(o),e;if(i===r)return Ba(o),t;i=i.sibling}throw Error(C(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Gc(e){return e=im(e),e!==null?Hc(e):null}function Hc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Hc(e);if(t!==null)return t;e=e.sibling}return null}var Wc=be.unstable_scheduleCallback,$a=be.unstable_cancelCallback,lm=be.unstable_shouldYield,sm=be.unstable_requestPaint,J=be.unstable_now,am=be.unstable_getCurrentPriorityLevel,js=be.unstable_ImmediatePriority,Qc=be.unstable_UserBlockingPriority,bo=be.unstable_NormalPriority,um=be.unstable_LowPriority,Kc=be.unstable_IdlePriority,fi=null,it=null;function cm(e){if(it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(fi,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:pm,dm=Math.log,fm=Math.LN2;function pm(e){return e>>>=0,e===0?32:31-(dm(e)/fm|0)|0}var Zr=64,eo=4194304;function rr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Do(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=rr(s):(i&=l,i!==0&&(r=rr(i)))}else l=n&~o,l!==0?r=rr(l):i!==0&&(r=rr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),o=1<<n,r|=e[n],t&=~o;return r}function mm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function hm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-Xe(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=mm(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function jl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function qc(){var e=Zr;return Zr<<=1,!(Zr&4194240)&&(Zr=64),e}function $i(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function zr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function gm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Xe(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Ms(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var B=0;function Yc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Xc,Vs,Jc,Zc,ed,Ml=!1,to=[],Nt=null,It=null,At=null,vr=new Map,xr=new Map,Ct=[],_m="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fa(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":It=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":vr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":xr.delete(t.pointerId)}}function Kn(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=$r(t),t!==null&&Vs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function ym(e,t,n,r,o){switch(t){case"focusin":return Nt=Kn(Nt,e,t,n,r,o),!0;case"dragenter":return It=Kn(It,e,t,n,r,o),!0;case"mouseover":return At=Kn(At,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return vr.set(i,Kn(vr.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,xr.set(i,Kn(xr.get(i)||null,e,t,n,r,o)),!0}return!1}function td(e){var t=Kt(e.target);if(t!==null){var n=an(t);if(n!==null){if(t=n.tag,t===13){if(t=Uc(n),t!==null){e.blockedOn=t,ed(e.priority,function(){Jc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function vo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Vl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Al=r,n.target.dispatchEvent(r),Al=null}else return t=$r(n),t!==null&&Vs(t),e.blockedOn=n,!1;t.shift()}return!0}function Ua(e,t,n){vo(e)&&n.delete(t)}function vm(){Ml=!1,Nt!==null&&vo(Nt)&&(Nt=null),It!==null&&vo(It)&&(It=null),At!==null&&vo(At)&&(At=null),vr.forEach(Ua),xr.forEach(Ua)}function qn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ml||(Ml=!0,be.unstable_scheduleCallback(be.unstable_NormalPriority,vm)))}function Sr(e){function t(o){return qn(o,e)}if(0<to.length){qn(to[0],e);for(var n=1;n<to.length;n++){var r=to[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nt!==null&&qn(Nt,e),It!==null&&qn(It,e),At!==null&&qn(At,e),vr.forEach(t),xr.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)td(n),n.blockedOn===null&&Ct.shift()}var Ln=xt.ReactCurrentBatchConfig,jo=!0;function xm(e,t,n,r){var o=B,i=Ln.transition;Ln.transition=null;try{B=1,zs(e,t,n,r)}finally{B=o,Ln.transition=i}}function Sm(e,t,n,r){var o=B,i=Ln.transition;Ln.transition=null;try{B=4,zs(e,t,n,r)}finally{B=o,Ln.transition=i}}function zs(e,t,n,r){if(jo){var o=Vl(e,t,n,r);if(o===null)Xi(e,t,r,Mo,n),Fa(e,r);else if(ym(o,e,t,n,r))r.stopPropagation();else if(Fa(e,r),t&4&&-1<_m.indexOf(e)){for(;o!==null;){var i=$r(o);if(i!==null&&Xc(i),i=Vl(e,t,n,r),i===null&&Xi(e,t,r,Mo,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Xi(e,t,r,null,n)}}var Mo=null;function Vl(e,t,n,r){if(Mo=null,e=Ds(r),e=Kt(e),e!==null)if(t=an(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Uc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Mo=e,null}function nd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(am()){case js:return 1;case Qc:return 4;case bo:case um:return 16;case Kc:return 536870912;default:return 16}default:return 16}}var Pt=null,Bs=null,xo=null;function rd(){if(xo)return xo;var e,t=Bs,n=t.length,r,o="value"in Pt?Pt.value:Pt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return xo=o.slice(e,1<r?1-r:void 0)}function So(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function no(){return!0}function Ga(){return!1}function je(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?no:Ga,this.isPropagationStopped=Ga,this}return q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=no)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=no)},persist:function(){},isPersistent:no}),t}var Fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$s=je(Fn),Br=q({},Fn,{view:0,detail:0}),Em=je(Br),Fi,Ui,Yn,pi=q({},Br,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yn&&(Yn&&e.type==="mousemove"?(Fi=e.screenX-Yn.screenX,Ui=e.screenY-Yn.screenY):Ui=Fi=0,Yn=e),Fi)},movementY:function(e){return"movementY"in e?e.movementY:Ui}}),Ha=je(pi),wm=q({},pi,{dataTransfer:0}),km=je(wm),Cm=q({},Br,{relatedTarget:0}),Gi=je(Cm),Lm=q({},Fn,{animationName:0,elapsedTime:0,pseudoElement:0}),Pm=je(Lm),Rm=q({},Fn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Tm=je(Rm),Nm=q({},Fn,{data:0}),Wa=je(Nm),Im={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Am={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Om={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Om[e])?!!t[e]:!1}function Fs(){return bm}var Dm=q({},Br,{key:function(e){if(e.key){var t=Im[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=So(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Am[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fs,charCode:function(e){return e.type==="keypress"?So(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?So(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jm=je(Dm),Mm=q({},pi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qa=je(Mm),Vm=q({},Br,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fs}),zm=je(Vm),Bm=q({},Fn,{propertyName:0,elapsedTime:0,pseudoElement:0}),$m=je(Bm),Fm=q({},pi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Um=je(Fm),Gm=[9,13,27,32],Us=gt&&"CompositionEvent"in window,ar=null;gt&&"documentMode"in document&&(ar=document.documentMode);var Hm=gt&&"TextEvent"in window&&!ar,od=gt&&(!Us||ar&&8<ar&&11>=ar),Ka=" ",qa=!1;function id(e,t){switch(e){case"keyup":return Gm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ld(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var pn=!1;function Wm(e,t){switch(e){case"compositionend":return ld(t);case"keypress":return t.which!==32?null:(qa=!0,Ka);case"textInput":return e=t.data,e===Ka&&qa?null:e;default:return null}}function Qm(e,t){if(pn)return e==="compositionend"||!Us&&id(e,t)?(e=rd(),xo=Bs=Pt=null,pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return od&&t.locale!=="ko"?null:t.data;default:return null}}var Km={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ya(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Km[e.type]:t==="textarea"}function sd(e,t,n,r){Vc(r),t=Vo(t,"onChange"),0<t.length&&(n=new $s("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ur=null,Er=null;function qm(e){yd(e,0)}function mi(e){var t=gn(e);if(Ic(t))return e}function Ym(e,t){if(e==="change")return t}var ad=!1;if(gt){var Hi;if(gt){var Wi="oninput"in document;if(!Wi){var Xa=document.createElement("div");Xa.setAttribute("oninput","return;"),Wi=typeof Xa.oninput=="function"}Hi=Wi}else Hi=!1;ad=Hi&&(!document.documentMode||9<document.documentMode)}function Ja(){ur&&(ur.detachEvent("onpropertychange",ud),Er=ur=null)}function ud(e){if(e.propertyName==="value"&&mi(Er)){var t=[];sd(t,Er,e,Ds(e)),Fc(qm,t)}}function Xm(e,t,n){e==="focusin"?(Ja(),ur=t,Er=n,ur.attachEvent("onpropertychange",ud)):e==="focusout"&&Ja()}function Jm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return mi(Er)}function Zm(e,t){if(e==="click")return mi(t)}function eh(e,t){if(e==="input"||e==="change")return mi(t)}function th(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:th;function wr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!xl.call(t,o)||!Ze(e[o],t[o]))return!1}return!0}function Za(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function eu(e,t){var n=Za(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Za(n)}}function cd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?cd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function dd(){for(var e=window,t=Io();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Io(e.document)}return t}function Gs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function nh(e){var t=dd(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&cd(n.ownerDocument.documentElement,n)){if(r!==null&&Gs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=eu(n,i);var l=eu(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var rh=gt&&"documentMode"in document&&11>=document.documentMode,mn=null,zl=null,cr=null,Bl=!1;function tu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Bl||mn==null||mn!==Io(r)||(r=mn,"selectionStart"in r&&Gs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),cr&&wr(cr,r)||(cr=r,r=Vo(zl,"onSelect"),0<r.length&&(t=new $s("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=mn)))}function ro(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hn={animationend:ro("Animation","AnimationEnd"),animationiteration:ro("Animation","AnimationIteration"),animationstart:ro("Animation","AnimationStart"),transitionend:ro("Transition","TransitionEnd")},Qi={},fd={};gt&&(fd=document.createElement("div").style,"AnimationEvent"in window||(delete hn.animationend.animation,delete hn.animationiteration.animation,delete hn.animationstart.animation),"TransitionEvent"in window||delete hn.transitionend.transition);function hi(e){if(Qi[e])return Qi[e];if(!hn[e])return e;var t=hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in fd)return Qi[e]=t[n];return e}var pd=hi("animationend"),md=hi("animationiteration"),hd=hi("animationstart"),gd=hi("transitionend"),_d=new Map,nu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Bt(e,t){_d.set(e,t),sn(t,[e])}for(var Ki=0;Ki<nu.length;Ki++){var qi=nu[Ki],oh=qi.toLowerCase(),ih=qi[0].toUpperCase()+qi.slice(1);Bt(oh,"on"+ih)}Bt(pd,"onAnimationEnd");Bt(md,"onAnimationIteration");Bt(hd,"onAnimationStart");Bt("dblclick","onDoubleClick");Bt("focusin","onFocus");Bt("focusout","onBlur");Bt(gd,"onTransitionEnd");An("onMouseEnter",["mouseout","mouseover"]);An("onMouseLeave",["mouseout","mouseover"]);An("onPointerEnter",["pointerout","pointerover"]);An("onPointerLeave",["pointerout","pointerover"]);sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));sn("onBeforeInput",["compositionend","keypress","textInput","paste"]);sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),lh=new Set("cancel close invalid load scroll toggle".split(" ").concat(or));function ru(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,om(r,t,void 0,e),e.currentTarget=null}function yd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;ru(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;ru(o,s,u),i=a}}}if(Oo)throw e=Dl,Oo=!1,Dl=null,e}function G(e,t){var n=t[Hl];n===void 0&&(n=t[Hl]=new Set);var r=e+"__bubble";n.has(r)||(vd(t,e,2,!1),n.add(r))}function Yi(e,t,n){var r=0;t&&(r|=4),vd(n,e,r,t)}var oo="_reactListening"+Math.random().toString(36).slice(2);function kr(e){if(!e[oo]){e[oo]=!0,Lc.forEach(function(n){n!=="selectionchange"&&(lh.has(n)||Yi(n,!1,e),Yi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[oo]||(t[oo]=!0,Yi("selectionchange",!1,t))}}function vd(e,t,n,r){switch(nd(t)){case 1:var o=xm;break;case 4:o=Sm;break;default:o=zs}n=o.bind(null,t,n,e),o=void 0,!bl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Xi(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=Kt(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}Fc(function(){var u=i,c=Ds(n),p=[];e:{var d=_d.get(e);if(d!==void 0){var h=$s,v=e;switch(e){case"keypress":if(So(n)===0)break e;case"keydown":case"keyup":h=jm;break;case"focusin":v="focus",h=Gi;break;case"focusout":v="blur",h=Gi;break;case"beforeblur":case"afterblur":h=Gi;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":h=Ha;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":h=km;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":h=zm;break;case pd:case md:case hd:h=Pm;break;case gd:h=$m;break;case"scroll":h=Em;break;case"wheel":h=Um;break;case"copy":case"cut":case"paste":h=Tm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":h=Qa}var S=(t&4)!==0,k=!S&&e==="scroll",g=S?d!==null?d+"Capture":null:d;S=[];for(var m=u,y;m!==null;){y=m;var x=y.stateNode;if(y.tag===5&&x!==null&&(y=x,g!==null&&(x=yr(m,g),x!=null&&S.push(Cr(m,x,y)))),k)break;m=m.return}0<S.length&&(d=new h(d,v,null,n,c),p.push({event:d,listeners:S}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",h=e==="mouseout"||e==="pointerout",d&&n!==Al&&(v=n.relatedTarget||n.fromElement)&&(Kt(v)||v[_t]))break e;if((h||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,h?(v=n.relatedTarget||n.toElement,h=u,v=v?Kt(v):null,v!==null&&(k=an(v),v!==k||v.tag!==5&&v.tag!==6)&&(v=null)):(h=null,v=u),h!==v)){if(S=Ha,x="onMouseLeave",g="onMouseEnter",m="mouse",(e==="pointerout"||e==="pointerover")&&(S=Qa,x="onPointerLeave",g="onPointerEnter",m="pointer"),k=h==null?d:gn(h),y=v==null?d:gn(v),d=new S(x,m+"leave",h,n,c),d.target=k,d.relatedTarget=y,x=null,Kt(c)===u&&(S=new S(g,m+"enter",v,n,c),S.target=y,S.relatedTarget=k,x=S),k=x,h&&v)t:{for(S=h,g=v,m=0,y=S;y;y=cn(y))m++;for(y=0,x=g;x;x=cn(x))y++;for(;0<m-y;)S=cn(S),m--;for(;0<y-m;)g=cn(g),y--;for(;m--;){if(S===g||g!==null&&S===g.alternate)break t;S=cn(S),g=cn(g)}S=null}else S=null;h!==null&&ou(p,d,h,S,!1),v!==null&&k!==null&&ou(p,k,v,S,!0)}}e:{if(d=u?gn(u):window,h=d.nodeName&&d.nodeName.toLowerCase(),h==="select"||h==="input"&&d.type==="file")var w=Ym;else if(Ya(d))if(ad)w=eh;else{w=Jm;var P=Xm}else(h=d.nodeName)&&h.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(w=Zm);if(w&&(w=w(e,u))){sd(p,w,n,c);break e}P&&P(e,d,u),e==="focusout"&&(P=d._wrapperState)&&P.controlled&&d.type==="number"&&Pl(d,"number",d.value)}switch(P=u?gn(u):window,e){case"focusin":(Ya(P)||P.contentEditable==="true")&&(mn=P,zl=u,cr=null);break;case"focusout":cr=zl=mn=null;break;case"mousedown":Bl=!0;break;case"contextmenu":case"mouseup":case"dragend":Bl=!1,tu(p,n,c);break;case"selectionchange":if(rh)break;case"keydown":case"keyup":tu(p,n,c)}var R;if(Us)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else pn?id(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(od&&n.locale!=="ko"&&(pn||N!=="onCompositionStart"?N==="onCompositionEnd"&&pn&&(R=rd()):(Pt=c,Bs="value"in Pt?Pt.value:Pt.textContent,pn=!0)),P=Vo(u,N),0<P.length&&(N=new Wa(N,e,null,n,c),p.push({event:N,listeners:P}),R?N.data=R:(R=ld(n),R!==null&&(N.data=R)))),(R=Hm?Wm(e,n):Qm(e,n))&&(u=Vo(u,"onBeforeInput"),0<u.length&&(c=new Wa("onBeforeInput","beforeinput",null,n,c),p.push({event:c,listeners:u}),c.data=R))}yd(p,t)})}function Cr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vo(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=yr(e,n),i!=null&&r.unshift(Cr(e,i,o)),i=yr(e,t),i!=null&&r.push(Cr(e,i,o))),e=e.return}return r}function cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ou(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=yr(n,i),a!=null&&l.unshift(Cr(n,a,s))):o||(a=yr(n,i),a!=null&&l.push(Cr(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var sh=/\r\n?/g,ah=/\u0000|\uFFFD/g;function iu(e){return(typeof e=="string"?e:""+e).replace(sh,`
`).replace(ah,"")}function io(e,t,n){if(t=iu(t),iu(e)!==t&&n)throw Error(C(425))}function zo(){}var $l=null,Fl=null;function Ul(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Gl=typeof setTimeout=="function"?setTimeout:void 0,uh=typeof clearTimeout=="function"?clearTimeout:void 0,lu=typeof Promise=="function"?Promise:void 0,ch=typeof queueMicrotask=="function"?queueMicrotask:typeof lu<"u"?function(e){return lu.resolve(null).then(e).catch(dh)}:Gl;function dh(e){setTimeout(function(){throw e})}function Ji(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Sr(t)}function Ot(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function su(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Un=Math.random().toString(36).slice(2),ot="__reactFiber$"+Un,Lr="__reactProps$"+Un,_t="__reactContainer$"+Un,Hl="__reactEvents$"+Un,fh="__reactListeners$"+Un,ph="__reactHandles$"+Un;function Kt(e){var t=e[ot];if(t)return t;for(var n=e.parentNode;n;){if(t=n[_t]||n[ot]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=su(e);e!==null;){if(n=e[ot])return n;e=su(e)}return t}e=n,n=e.parentNode}return null}function $r(e){return e=e[ot]||e[_t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function gn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function gi(e){return e[Lr]||null}var Wl=[],_n=-1;function $t(e){return{current:e}}function H(e){0>_n||(e.current=Wl[_n],Wl[_n]=null,_n--)}function U(e,t){_n++,Wl[_n]=e.current,e.current=t}var zt={},ye=$t(zt),Le=$t(!1),tn=zt;function On(e,t){var n=e.type.contextTypes;if(!n)return zt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Pe(e){return e=e.childContextTypes,e!=null}function Bo(){H(Le),H(ye)}function au(e,t,n){if(ye.current!==zt)throw Error(C(168));U(ye,t),U(Le,n)}function xd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(C(108,Xp(e)||"Unknown",o));return q({},n,r)}function $o(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||zt,tn=ye.current,U(ye,e),U(Le,Le.current),!0}function uu(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=xd(e,t,tn),r.__reactInternalMemoizedMergedChildContext=e,H(Le),H(ye),U(ye,e)):H(Le),U(Le,n)}var at=null,_i=!1,Zi=!1;function Sd(e){at===null?at=[e]:at.push(e)}function mh(e){_i=!0,Sd(e)}function Ft(){if(!Zi&&at!==null){Zi=!0;var e=0,t=B;try{var n=at;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}at=null,_i=!1}catch(o){throw at!==null&&(at=at.slice(e+1)),Wc(js,Ft),o}finally{B=t,Zi=!1}}return null}var yn=[],vn=0,Fo=null,Uo=0,Be=[],$e=0,nn=null,ct=1,dt="";function Wt(e,t){yn[vn++]=Uo,yn[vn++]=Fo,Fo=e,Uo=t}function Ed(e,t,n){Be[$e++]=ct,Be[$e++]=dt,Be[$e++]=nn,nn=e;var r=ct;e=dt;var o=32-Xe(r)-1;r&=~(1<<o),n+=1;var i=32-Xe(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,ct=1<<32-Xe(t)+o|n<<o|r,dt=i+e}else ct=1<<i|n<<o|r,dt=e}function Hs(e){e.return!==null&&(Wt(e,1),Ed(e,1,0))}function Ws(e){for(;e===Fo;)Fo=yn[--vn],yn[vn]=null,Uo=yn[--vn],yn[vn]=null;for(;e===nn;)nn=Be[--$e],Be[$e]=null,dt=Be[--$e],Be[$e]=null,ct=Be[--$e],Be[$e]=null}var Oe=null,Ae=null,W=!1,Ye=null;function wd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function cu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Oe=e,Ae=Ot(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Oe=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nn!==null?{id:ct,overflow:dt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Oe=e,Ae=null,!0):!1;default:return!1}}function Ql(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Kl(e){if(W){var t=Ae;if(t){var n=t;if(!cu(e,t)){if(Ql(e))throw Error(C(418));t=Ot(n.nextSibling);var r=Oe;t&&cu(e,t)?wd(r,n):(e.flags=e.flags&-4097|2,W=!1,Oe=e)}}else{if(Ql(e))throw Error(C(418));e.flags=e.flags&-4097|2,W=!1,Oe=e}}}function du(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Oe=e}function lo(e){if(e!==Oe)return!1;if(!W)return du(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ul(e.type,e.memoizedProps)),t&&(t=Ae)){if(Ql(e))throw kd(),Error(C(418));for(;t;)wd(e,t),t=Ot(t.nextSibling)}if(du(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=Ot(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Oe?Ot(e.stateNode.nextSibling):null;return!0}function kd(){for(var e=Ae;e;)e=Ot(e.nextSibling)}function bn(){Ae=Oe=null,W=!1}function Qs(e){Ye===null?Ye=[e]:Ye.push(e)}var hh=xt.ReactCurrentBatchConfig;function Xn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function so(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function fu(e){var t=e._init;return t(e._payload)}function Cd(e){function t(g,m){if(e){var y=g.deletions;y===null?(g.deletions=[m],g.flags|=16):y.push(m)}}function n(g,m){if(!e)return null;for(;m!==null;)t(g,m),m=m.sibling;return null}function r(g,m){for(g=new Map;m!==null;)m.key!==null?g.set(m.key,m):g.set(m.index,m),m=m.sibling;return g}function o(g,m){return g=Mt(g,m),g.index=0,g.sibling=null,g}function i(g,m,y){return g.index=y,e?(y=g.alternate,y!==null?(y=y.index,y<m?(g.flags|=2,m):y):(g.flags|=2,m)):(g.flags|=1048576,m)}function l(g){return e&&g.alternate===null&&(g.flags|=2),g}function s(g,m,y,x){return m===null||m.tag!==6?(m=ll(y,g.mode,x),m.return=g,m):(m=o(m,y),m.return=g,m)}function a(g,m,y,x){var w=y.type;return w===fn?c(g,m,y.props.children,x,y.key):m!==null&&(m.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===wt&&fu(w)===m.type)?(x=o(m,y.props),x.ref=Xn(g,m,y),x.return=g,x):(x=Ro(y.type,y.key,y.props,null,g.mode,x),x.ref=Xn(g,m,y),x.return=g,x)}function u(g,m,y,x){return m===null||m.tag!==4||m.stateNode.containerInfo!==y.containerInfo||m.stateNode.implementation!==y.implementation?(m=sl(y,g.mode,x),m.return=g,m):(m=o(m,y.children||[]),m.return=g,m)}function c(g,m,y,x,w){return m===null||m.tag!==7?(m=en(y,g.mode,x,w),m.return=g,m):(m=o(m,y),m.return=g,m)}function p(g,m,y){if(typeof m=="string"&&m!==""||typeof m=="number")return m=ll(""+m,g.mode,y),m.return=g,m;if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Yr:return y=Ro(m.type,m.key,m.props,null,g.mode,y),y.ref=Xn(g,null,m),y.return=g,y;case dn:return m=sl(m,g.mode,y),m.return=g,m;case wt:var x=m._init;return p(g,x(m._payload),y)}if(nr(m)||Wn(m))return m=en(m,g.mode,y,null),m.return=g,m;so(g,m)}return null}function d(g,m,y,x){var w=m!==null?m.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return w!==null?null:s(g,m,""+y,x);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Yr:return y.key===w?a(g,m,y,x):null;case dn:return y.key===w?u(g,m,y,x):null;case wt:return w=y._init,d(g,m,w(y._payload),x)}if(nr(y)||Wn(y))return w!==null?null:c(g,m,y,x,null);so(g,y)}return null}function h(g,m,y,x,w){if(typeof x=="string"&&x!==""||typeof x=="number")return g=g.get(y)||null,s(m,g,""+x,w);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Yr:return g=g.get(x.key===null?y:x.key)||null,a(m,g,x,w);case dn:return g=g.get(x.key===null?y:x.key)||null,u(m,g,x,w);case wt:var P=x._init;return h(g,m,y,P(x._payload),w)}if(nr(x)||Wn(x))return g=g.get(y)||null,c(m,g,x,w,null);so(m,x)}return null}function v(g,m,y,x){for(var w=null,P=null,R=m,N=m=0,$=null;R!==null&&N<y.length;N++){R.index>N?($=R,R=null):$=R.sibling;var b=d(g,R,y[N],x);if(b===null){R===null&&(R=$);break}e&&R&&b.alternate===null&&t(g,R),m=i(b,m,N),P===null?w=b:P.sibling=b,P=b,R=$}if(N===y.length)return n(g,R),W&&Wt(g,N),w;if(R===null){for(;N<y.length;N++)R=p(g,y[N],x),R!==null&&(m=i(R,m,N),P===null?w=R:P.sibling=R,P=R);return W&&Wt(g,N),w}for(R=r(g,R);N<y.length;N++)$=h(R,g,N,y[N],x),$!==null&&(e&&$.alternate!==null&&R.delete($.key===null?N:$.key),m=i($,m,N),P===null?w=$:P.sibling=$,P=$);return e&&R.forEach(function(A){return t(g,A)}),W&&Wt(g,N),w}function S(g,m,y,x){var w=Wn(y);if(typeof w!="function")throw Error(C(150));if(y=w.call(y),y==null)throw Error(C(151));for(var P=w=null,R=m,N=m=0,$=null,b=y.next();R!==null&&!b.done;N++,b=y.next()){R.index>N?($=R,R=null):$=R.sibling;var A=d(g,R,b.value,x);if(A===null){R===null&&(R=$);break}e&&R&&A.alternate===null&&t(g,R),m=i(A,m,N),P===null?w=A:P.sibling=A,P=A,R=$}if(b.done)return n(g,R),W&&Wt(g,N),w;if(R===null){for(;!b.done;N++,b=y.next())b=p(g,b.value,x),b!==null&&(m=i(b,m,N),P===null?w=b:P.sibling=b,P=b);return W&&Wt(g,N),w}for(R=r(g,R);!b.done;N++,b=y.next())b=h(R,g,N,b.value,x),b!==null&&(e&&b.alternate!==null&&R.delete(b.key===null?N:b.key),m=i(b,m,N),P===null?w=b:P.sibling=b,P=b);return e&&R.forEach(function(Z){return t(g,Z)}),W&&Wt(g,N),w}function k(g,m,y,x){if(typeof y=="object"&&y!==null&&y.type===fn&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Yr:e:{for(var w=y.key,P=m;P!==null;){if(P.key===w){if(w=y.type,w===fn){if(P.tag===7){n(g,P.sibling),m=o(P,y.props.children),m.return=g,g=m;break e}}else if(P.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===wt&&fu(w)===P.type){n(g,P.sibling),m=o(P,y.props),m.ref=Xn(g,P,y),m.return=g,g=m;break e}n(g,P);break}else t(g,P);P=P.sibling}y.type===fn?(m=en(y.props.children,g.mode,x,y.key),m.return=g,g=m):(x=Ro(y.type,y.key,y.props,null,g.mode,x),x.ref=Xn(g,m,y),x.return=g,g=x)}return l(g);case dn:e:{for(P=y.key;m!==null;){if(m.key===P)if(m.tag===4&&m.stateNode.containerInfo===y.containerInfo&&m.stateNode.implementation===y.implementation){n(g,m.sibling),m=o(m,y.children||[]),m.return=g,g=m;break e}else{n(g,m);break}else t(g,m);m=m.sibling}m=sl(y,g.mode,x),m.return=g,g=m}return l(g);case wt:return P=y._init,k(g,m,P(y._payload),x)}if(nr(y))return v(g,m,y,x);if(Wn(y))return S(g,m,y,x);so(g,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,m!==null&&m.tag===6?(n(g,m.sibling),m=o(m,y),m.return=g,g=m):(n(g,m),m=ll(y,g.mode,x),m.return=g,g=m),l(g)):n(g,m)}return k}var Dn=Cd(!0),Ld=Cd(!1),Go=$t(null),Ho=null,xn=null,Ks=null;function qs(){Ks=xn=Ho=null}function Ys(e){var t=Go.current;H(Go),e._currentValue=t}function ql(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pn(e,t){Ho=e,Ks=xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(Ks!==e)if(e={context:e,memoizedValue:t,next:null},xn===null){if(Ho===null)throw Error(C(308));xn=e,Ho.dependencies={lanes:0,firstContext:e}}else xn=xn.next=e;return t}var qt=null;function Xs(e){qt===null?qt=[e]:qt.push(e)}function Pd(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Xs(t)):(n.next=o.next,o.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function Js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Rd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function mt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,yt(e,n)}return o=r.interleaved,o===null?(t.next=t,Xs(r)):(t.next=o.next,o.next=t),r.interleaved=t,yt(e,n)}function Eo(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}function pu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wo(e,t,n,r){var o=e.updateQueue;kt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var c=e.alternate;c!==null&&(c=c.updateQueue,s=c.lastBaseUpdate,s!==l&&(s===null?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=a))}if(i!==null){var p=o.baseState;l=0,c=u=a=null,s=i;do{var d=s.lane,h=s.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var v=e,S=s;switch(d=t,h=n,S.tag){case 1:if(v=S.payload,typeof v=="function"){p=v.call(h,p,d);break e}p=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=S.payload,d=typeof v=="function"?v.call(h,p,d):v,d==null)break e;p=q({},p,d);break e;case 2:kt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[s]:d.push(s))}else h={eventTime:h,lane:d,tag:s.tag,payload:s.payload,callback:s.callback,next:null},c===null?(u=c=h,a=p):c=c.next=h,l|=d;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;d=s,s=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(c===null&&(a=p),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=c,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);on|=l,e.lanes=l,e.memoizedState=p}}function mu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(C(191,o));o.call(r)}}}var Fr={},lt=$t(Fr),Pr=$t(Fr),Rr=$t(Fr);function Yt(e){if(e===Fr)throw Error(C(174));return e}function Zs(e,t){switch(U(Rr,t),U(Pr,e),U(lt,Fr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Tl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Tl(t,e)}H(lt),U(lt,t)}function jn(){H(lt),H(Pr),H(Rr)}function Td(e){Yt(Rr.current);var t=Yt(lt.current),n=Tl(t,e.type);t!==n&&(U(Pr,e),U(lt,n))}function ea(e){Pr.current===e&&(H(lt),H(Pr))}var Q=$t(0);function Qo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var el=[];function ta(){for(var e=0;e<el.length;e++)el[e]._workInProgressVersionPrimary=null;el.length=0}var wo=xt.ReactCurrentDispatcher,tl=xt.ReactCurrentBatchConfig,rn=0,K=null,re=null,ue=null,Ko=!1,dr=!1,Tr=0,gh=0;function me(){throw Error(C(321))}function na(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function ra(e,t,n,r,o,i){if(rn=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,wo.current=e===null||e.memoizedState===null?xh:Sh,e=n(r,o),dr){i=0;do{if(dr=!1,Tr=0,25<=i)throw Error(C(301));i+=1,ue=re=null,t.updateQueue=null,wo.current=Eh,e=n(r,o)}while(dr)}if(wo.current=qo,t=re!==null&&re.next!==null,rn=0,ue=re=K=null,Ko=!1,t)throw Error(C(300));return e}function oa(){var e=Tr!==0;return Tr=0,e}function rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ue===null?K.memoizedState=ue=e:ue=ue.next=e,ue}function We(){if(re===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=ue===null?K.memoizedState:ue.next;if(t!==null)ue=t,re=e;else{if(e===null)throw Error(C(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},ue===null?K.memoizedState=ue=e:ue=ue.next=e}return ue}function Nr(e,t){return typeof t=="function"?t(e):t}function nl(e){var t=We(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=re,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var c=u.lane;if((rn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var p={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=p,l=r):a=a.next=p,K.lanes|=c,on|=c}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,Ze(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,K.lanes|=i,on|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function rl(e){var t=We(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);Ze(i,t.memoizedState)||(Ce=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Nd(){}function Id(e,t){var n=K,r=We(),o=t(),i=!Ze(r.memoizedState,o);if(i&&(r.memoizedState=o,Ce=!0),r=r.queue,ia(bd.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ue!==null&&ue.memoizedState.tag&1){if(n.flags|=2048,Ir(9,Od.bind(null,n,r,o,t),void 0,null),ce===null)throw Error(C(349));rn&30||Ad(n,t,o)}return o}function Ad(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Od(e,t,n,r){t.value=n,t.getSnapshot=r,Dd(t)&&jd(e)}function bd(e,t,n){return n(function(){Dd(t)&&jd(e)})}function Dd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function jd(e){var t=yt(e,1);t!==null&&Je(t,e,1,-1)}function hu(e){var t=rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Nr,lastRenderedState:e},t.queue=e,e=e.dispatch=vh.bind(null,K,e),[t.memoizedState,e]}function Ir(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Md(){return We().memoizedState}function ko(e,t,n,r){var o=rt();K.flags|=e,o.memoizedState=Ir(1|t,n,void 0,r===void 0?null:r)}function yi(e,t,n,r){var o=We();r=r===void 0?null:r;var i=void 0;if(re!==null){var l=re.memoizedState;if(i=l.destroy,r!==null&&na(r,l.deps)){o.memoizedState=Ir(t,n,i,r);return}}K.flags|=e,o.memoizedState=Ir(1|t,n,i,r)}function gu(e,t){return ko(8390656,8,e,t)}function ia(e,t){return yi(2048,8,e,t)}function Vd(e,t){return yi(4,2,e,t)}function zd(e,t){return yi(4,4,e,t)}function Bd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function $d(e,t,n){return n=n!=null?n.concat([e]):null,yi(4,4,Bd.bind(null,t,e),n)}function la(){}function Fd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ud(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&na(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Gd(e,t,n){return rn&21?(Ze(n,t)||(n=qc(),K.lanes|=n,on|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function _h(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=tl.transition;tl.transition={};try{e(!1),t()}finally{B=n,tl.transition=r}}function Hd(){return We().memoizedState}function yh(e,t,n){var r=jt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wd(e))Qd(t,n);else if(n=Pd(e,t,n,r),n!==null){var o=Se();Je(n,e,r,o),Kd(n,t,r)}}function vh(e,t,n){var r=jt(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wd(e))Qd(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,Ze(s,l)){var a=t.interleaved;a===null?(o.next=o,Xs(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Pd(e,t,o,r),n!==null&&(o=Se(),Je(n,e,r,o),Kd(n,t,r))}}function Wd(e){var t=e.alternate;return e===K||t!==null&&t===K}function Qd(e,t){dr=Ko=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}var qo={readContext:He,useCallback:me,useContext:me,useEffect:me,useImperativeHandle:me,useInsertionEffect:me,useLayoutEffect:me,useMemo:me,useReducer:me,useRef:me,useState:me,useDebugValue:me,useDeferredValue:me,useTransition:me,useMutableSource:me,useSyncExternalStore:me,useId:me,unstable_isNewReconciler:!1},xh={readContext:He,useCallback:function(e,t){return rt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:gu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ko(4194308,4,Bd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ko(4194308,4,e,t)},useInsertionEffect:function(e,t){return ko(4,2,e,t)},useMemo:function(e,t){var n=rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=yh.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=rt();return e={current:e},t.memoizedState=e},useState:hu,useDebugValue:la,useDeferredValue:function(e){return rt().memoizedState=e},useTransition:function(){var e=hu(!1),t=e[0];return e=_h.bind(null,e[1]),rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,o=rt();if(W){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),ce===null)throw Error(C(349));rn&30||Ad(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,gu(bd.bind(null,r,i,e),[e]),r.flags|=2048,Ir(9,Od.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=rt(),t=ce.identifierPrefix;if(W){var n=dt,r=ct;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Tr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=gh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Sh={readContext:He,useCallback:Fd,useContext:He,useEffect:ia,useImperativeHandle:$d,useInsertionEffect:Vd,useLayoutEffect:zd,useMemo:Ud,useReducer:nl,useRef:Md,useState:function(){return nl(Nr)},useDebugValue:la,useDeferredValue:function(e){var t=We();return Gd(t,re.memoizedState,e)},useTransition:function(){var e=nl(Nr)[0],t=We().memoizedState;return[e,t]},useMutableSource:Nd,useSyncExternalStore:Id,useId:Hd,unstable_isNewReconciler:!1},Eh={readContext:He,useCallback:Fd,useContext:He,useEffect:ia,useImperativeHandle:$d,useInsertionEffect:Vd,useLayoutEffect:zd,useMemo:Ud,useReducer:rl,useRef:Md,useState:function(){return rl(Nr)},useDebugValue:la,useDeferredValue:function(e){var t=We();return re===null?t.memoizedState=e:Gd(t,re.memoizedState,e)},useTransition:function(){var e=rl(Nr)[0],t=We().memoizedState;return[e,t]},useMutableSource:Nd,useSyncExternalStore:Id,useId:Hd,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Yl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var vi={isMounted:function(e){return(e=e._reactInternals)?an(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Se(),o=jt(e),i=mt(r,o);i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,o),t!==null&&(Je(t,e,o,r),Eo(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Se(),o=jt(e),i=mt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,o),t!==null&&(Je(t,e,o,r),Eo(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Se(),r=jt(e),o=mt(n,r);o.tag=2,t!=null&&(o.callback=t),t=bt(e,o,r),t!==null&&(Je(t,e,r,n),Eo(t,e,r))}};function _u(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!wr(n,r)||!wr(o,i):!0}function qd(e,t,n){var r=!1,o=zt,i=t.contextType;return typeof i=="object"&&i!==null?i=He(i):(o=Pe(t)?tn:ye.current,r=t.contextTypes,i=(r=r!=null)?On(e,o):zt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=vi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function yu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&vi.enqueueReplaceState(t,t.state,null)}function Xl(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Js(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=He(i):(i=Pe(t)?tn:ye.current,o.context=On(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Yl(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&vi.enqueueReplaceState(o,o.state,null),Wo(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Mn(e,t){try{var n="",r=t;do n+=Yp(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function ol(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Jl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var wh=typeof WeakMap=="function"?WeakMap:Map;function Yd(e,t,n){n=mt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Xo||(Xo=!0,as=r),Jl(e,t)},n}function Xd(e,t,n){n=mt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Jl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Jl(e,t),typeof r!="function"&&(Dt===null?Dt=new Set([this]):Dt.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function vu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new wh;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Mh.bind(null,e,t,n),t.then(e,e))}function xu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Su(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=mt(-1,1),t.tag=2,bt(n,t,1))),n.lanes|=1),e)}var kh=xt.ReactCurrentOwner,Ce=!1;function ve(e,t,n,r){t.child=e===null?Ld(t,null,n,r):Dn(t,e.child,n,r)}function Eu(e,t,n,r,o){n=n.render;var i=t.ref;return Pn(t,o),r=ra(e,t,n,r,i,o),n=oa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&n&&Hs(t),t.flags|=1,ve(e,t,r,o),t.child)}function wu(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!ma(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Jd(e,t,i,r,o)):(e=Ro(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:wr,n(l,r)&&e.ref===t.ref)return vt(e,t,o)}return t.flags|=1,e=Mt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Jd(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(wr(i,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,vt(e,t,o)}return Zl(e,t,n,r,o)}function Zd(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(En,Ie),Ie|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(En,Ie),Ie|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(En,Ie),Ie|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(En,Ie),Ie|=r;return ve(e,t,o,n),t.child}function ef(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Zl(e,t,n,r,o){var i=Pe(n)?tn:ye.current;return i=On(t,i),Pn(t,o),n=ra(e,t,n,r,i,o),r=oa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,vt(e,t,o)):(W&&r&&Hs(t),t.flags|=1,ve(e,t,n,o),t.child)}function ku(e,t,n,r,o){if(Pe(n)){var i=!0;$o(t)}else i=!1;if(Pn(t,o),t.stateNode===null)Co(e,t),qd(t,n,r),Xl(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Pe(n)?tn:ye.current,u=On(t,u));var c=n.getDerivedStateFromProps,p=typeof c=="function"||typeof l.getSnapshotBeforeUpdate=="function";p||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&yu(t,l,r,u),kt=!1;var d=t.memoizedState;l.state=d,Wo(t,r,l,o),a=t.memoizedState,s!==r||d!==a||Le.current||kt?(typeof c=="function"&&(Yl(t,n,c,r),a=t.memoizedState),(s=kt||_u(t,n,s,r,d,a,u))?(p||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Rd(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:Ke(t.type,s),l.props=u,p=t.pendingProps,d=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Pe(n)?tn:ye.current,a=On(t,a));var h=n.getDerivedStateFromProps;(c=typeof h=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==p||d!==a)&&yu(t,l,r,a),kt=!1,d=t.memoizedState,l.state=d,Wo(t,r,l,o);var v=t.memoizedState;s!==p||d!==v||Le.current||kt?(typeof h=="function"&&(Yl(t,n,h,r),v=t.memoizedState),(u=kt||_u(t,n,u,r,d,v,a)||!1)?(c||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,v,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,v,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),l.props=r,l.state=v,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return es(e,t,n,r,i,o)}function es(e,t,n,r,o,i){ef(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&uu(t,n,!1),vt(e,t,i);r=t.stateNode,kh.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Dn(t,e.child,null,i),t.child=Dn(t,null,s,i)):ve(e,t,s,i),t.memoizedState=r.state,o&&uu(t,n,!0),t.child}function tf(e){var t=e.stateNode;t.pendingContext?au(e,t.pendingContext,t.pendingContext!==t.context):t.context&&au(e,t.context,!1),Zs(e,t.containerInfo)}function Cu(e,t,n,r,o){return bn(),Qs(o),t.flags|=256,ve(e,t,n,r),t.child}var ts={dehydrated:null,treeContext:null,retryLane:0};function ns(e){return{baseLanes:e,cachePool:null,transitions:null}}function nf(e,t,n){var r=t.pendingProps,o=Q.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),U(Q,o&1),e===null)return Kl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Ei(l,r,0,null),e=en(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ns(n),t.memoizedState=ts,e):sa(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return Ch(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Mt(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=Mt(s,i):(i=en(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?ns(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=ts,r}return i=e.child,e=i.sibling,r=Mt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function sa(e,t){return t=Ei({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ao(e,t,n,r){return r!==null&&Qs(r),Dn(t,e.child,null,n),e=sa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Ch(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=ol(Error(C(422))),ao(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ei({mode:"visible",children:r.children},o,0,null),i=en(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Dn(t,e.child,null,l),t.child.memoizedState=ns(l),t.memoizedState=ts,i);if(!(t.mode&1))return ao(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(C(419)),r=ol(i,r,void 0),ao(e,t,l,r)}if(s=(l&e.childLanes)!==0,Ce||s){if(r=ce,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,yt(e,o),Je(r,e,o,-1))}return pa(),r=ol(Error(C(421))),ao(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Vh.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,Ae=Ot(o.nextSibling),Oe=t,W=!0,Ye=null,e!==null&&(Be[$e++]=ct,Be[$e++]=dt,Be[$e++]=nn,ct=e.id,dt=e.overflow,nn=t),t=sa(t,r.children),t.flags|=4096,t)}function Lu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ql(e.return,t,n)}function il(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function rf(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(ve(e,t,r.children,n),r=Q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Lu(e,n,t);else if(e.tag===19)Lu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(Q,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Qo(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),il(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Qo(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}il(t,!0,n,null,i);break;case"together":il(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Co(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),on|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Mt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Mt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Lh(e,t,n){switch(t.tag){case 3:tf(t),bn();break;case 5:Td(t);break;case 1:Pe(t.type)&&$o(t);break;case 4:Zs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;U(Go,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(Q,Q.current&1),t.flags|=128,null):n&t.child.childLanes?nf(e,t,n):(U(Q,Q.current&1),e=vt(e,t,n),e!==null?e.sibling:null);U(Q,Q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return rf(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),U(Q,Q.current),r)break;return null;case 22:case 23:return t.lanes=0,Zd(e,t,n)}return vt(e,t,n)}var of,rs,lf,sf;of=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};rs=function(){};lf=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Yt(lt.current);var i=null;switch(n){case"input":o=Cl(e,o),r=Cl(e,r),i=[];break;case"select":o=q({},o,{value:void 0}),r=q({},r,{value:void 0}),i=[];break;case"textarea":o=Rl(e,o),r=Rl(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=zo)}Nl(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(gr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(gr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&G("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};sf=function(e,t,n,r){n!==r&&(t.flags|=4)};function Jn(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ph(e,t,n){var r=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return Pe(t.type)&&Bo(),he(t),null;case 3:return r=t.stateNode,jn(),H(Le),H(ye),ta(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(lo(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(ds(Ye),Ye=null))),rs(e,t),he(t),null;case 5:ea(t);var o=Yt(Rr.current);if(n=t.type,e!==null&&t.stateNode!=null)lf(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return he(t),null}if(e=Yt(lt.current),lo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[ot]=t,r[Lr]=i,e=(t.mode&1)!==0,n){case"dialog":G("cancel",r),G("close",r);break;case"iframe":case"object":case"embed":G("load",r);break;case"video":case"audio":for(o=0;o<or.length;o++)G(or[o],r);break;case"source":G("error",r);break;case"img":case"image":case"link":G("error",r),G("load",r);break;case"details":G("toggle",r);break;case"input":Da(r,i),G("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},G("invalid",r);break;case"textarea":Ma(r,i),G("invalid",r)}Nl(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&io(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&io(r.textContent,s,e),o=["children",""+s]):gr.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&G("scroll",r)}switch(n){case"input":Xr(r),ja(r,i,!0);break;case"textarea":Xr(r),Va(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=zo)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=bc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[ot]=t,e[Lr]=r,of(e,t,!1,!1),t.stateNode=e;e:{switch(l=Il(n,r),n){case"dialog":G("cancel",e),G("close",e),o=r;break;case"iframe":case"object":case"embed":G("load",e),o=r;break;case"video":case"audio":for(o=0;o<or.length;o++)G(or[o],e);o=r;break;case"source":G("error",e),o=r;break;case"img":case"image":case"link":G("error",e),G("load",e),o=r;break;case"details":G("toggle",e),o=r;break;case"input":Da(e,r),o=Cl(e,r),G("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=q({},r,{value:void 0}),G("invalid",e);break;case"textarea":Ma(e,r),o=Rl(e,r),G("invalid",e);break;default:o=r}Nl(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?Mc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Dc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&_r(e,a):typeof a=="number"&&_r(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(gr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&G("scroll",e):a!=null&&Is(e,i,a,l))}switch(n){case"input":Xr(e),ja(e,r,!1);break;case"textarea":Xr(e),Va(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Vt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?wn(e,!!r.multiple,i,!1):r.defaultValue!=null&&wn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=zo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return he(t),null;case 6:if(e&&t.stateNode!=null)sf(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=Yt(Rr.current),Yt(lt.current),lo(t)){if(r=t.stateNode,n=t.memoizedProps,r[ot]=t,(i=r.nodeValue!==n)&&(e=Oe,e!==null))switch(e.tag){case 3:io(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&io(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ot]=t,t.stateNode=r}return he(t),null;case 13:if(H(Q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ae!==null&&t.mode&1&&!(t.flags&128))kd(),bn(),t.flags|=98560,i=!1;else if(i=lo(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(C(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(C(317));i[ot]=t}else bn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),i=!1}else Ye!==null&&(ds(Ye),Ye=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?ie===0&&(ie=3):pa())),t.updateQueue!==null&&(t.flags|=4),he(t),null);case 4:return jn(),rs(e,t),e===null&&kr(t.stateNode.containerInfo),he(t),null;case 10:return Ys(t.type._context),he(t),null;case 17:return Pe(t.type)&&Bo(),he(t),null;case 19:if(H(Q),i=t.memoizedState,i===null)return he(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Jn(i,!1);else{if(ie!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=Qo(e),l!==null){for(t.flags|=128,Jn(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(Q,Q.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>Vn&&(t.flags|=128,r=!0,Jn(i,!1),t.lanes=4194304)}else{if(!r)if(e=Qo(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Jn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!W)return he(t),null}else 2*J()-i.renderingStartTime>Vn&&n!==1073741824&&(t.flags|=128,r=!0,Jn(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=Q.current,U(Q,r?n&1|2:n&1),t):(he(t),null);case 22:case 23:return fa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ie&1073741824&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Rh(e,t){switch(Ws(t),t.tag){case 1:return Pe(t.type)&&Bo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return jn(),H(Le),H(ye),ta(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ea(t),null;case 13:if(H(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(Q),null;case 4:return jn(),null;case 10:return Ys(t.type._context),null;case 22:case 23:return fa(),null;case 24:return null;default:return null}}var uo=!1,_e=!1,Th=typeof WeakSet=="function"?WeakSet:Set,T=null;function Sn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Y(e,t,r)}else n.current=null}function os(e,t,n){try{n()}catch(r){Y(e,t,r)}}var Pu=!1;function Nh(e,t){if($l=jo,e=dd(),Gs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,c=0,p=e,d=null;t:for(;;){for(var h;p!==n||o!==0&&p.nodeType!==3||(s=l+o),p!==i||r!==0&&p.nodeType!==3||(a=l+r),p.nodeType===3&&(l+=p.nodeValue.length),(h=p.firstChild)!==null;)d=p,p=h;for(;;){if(p===e)break t;if(d===n&&++u===o&&(s=l),d===i&&++c===r&&(a=l),(h=p.nextSibling)!==null)break;p=d,d=p.parentNode}p=h}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fl={focusedElem:e,selectionRange:n},jo=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var S=v.memoizedProps,k=v.memoizedState,g=t.stateNode,m=g.getSnapshotBeforeUpdate(t.elementType===t.type?S:Ke(t.type,S),k);g.__reactInternalSnapshotBeforeUpdate=m}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(x){Y(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return v=Pu,Pu=!1,v}function fr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&os(t,n,i)}o=o.next}while(o!==r)}}function xi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function is(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function af(e){var t=e.alternate;t!==null&&(e.alternate=null,af(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ot],delete t[Lr],delete t[Hl],delete t[fh],delete t[ph])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function uf(e){return e.tag===5||e.tag===3||e.tag===4}function Ru(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||uf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ls(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=zo));else if(r!==4&&(e=e.child,e!==null))for(ls(e,t,n),e=e.sibling;e!==null;)ls(e,t,n),e=e.sibling}function ss(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ss(e,t,n),e=e.sibling;e!==null;)ss(e,t,n),e=e.sibling}var de=null,qe=!1;function Et(e,t,n){for(n=n.child;n!==null;)cf(e,t,n),n=n.sibling}function cf(e,t,n){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(fi,n)}catch{}switch(n.tag){case 5:_e||Sn(n,t);case 6:var r=de,o=qe;de=null,Et(e,t,n),de=r,qe=o,de!==null&&(qe?(e=de,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):de.removeChild(n.stateNode));break;case 18:de!==null&&(qe?(e=de,n=n.stateNode,e.nodeType===8?Ji(e.parentNode,n):e.nodeType===1&&Ji(e,n),Sr(e)):Ji(de,n.stateNode));break;case 4:r=de,o=qe,de=n.stateNode.containerInfo,qe=!0,Et(e,t,n),de=r,qe=o;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&os(n,t,l),o=o.next}while(o!==r)}Et(e,t,n);break;case 1:if(!_e&&(Sn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Y(n,t,s)}Et(e,t,n);break;case 21:Et(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,Et(e,t,n),_e=r):Et(e,t,n);break;default:Et(e,t,n)}}function Tu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Th),t.forEach(function(r){var o=zh.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Qe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:de=s.stateNode,qe=!1;break e;case 3:de=s.stateNode.containerInfo,qe=!0;break e;case 4:de=s.stateNode.containerInfo,qe=!0;break e}s=s.return}if(de===null)throw Error(C(160));cf(i,l,o),de=null,qe=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){Y(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)df(t,e),t=t.sibling}function df(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Qe(t,e),et(e),r&4){try{fr(3,e,e.return),xi(3,e)}catch(S){Y(e,e.return,S)}try{fr(5,e,e.return)}catch(S){Y(e,e.return,S)}}break;case 1:Qe(t,e),et(e),r&512&&n!==null&&Sn(n,n.return);break;case 5:if(Qe(t,e),et(e),r&512&&n!==null&&Sn(n,n.return),e.flags&32){var o=e.stateNode;try{_r(o,"")}catch(S){Y(e,e.return,S)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&Ac(o,i),Il(s,l);var u=Il(s,i);for(l=0;l<a.length;l+=2){var c=a[l],p=a[l+1];c==="style"?Mc(o,p):c==="dangerouslySetInnerHTML"?Dc(o,p):c==="children"?_r(o,p):Is(o,c,p,u)}switch(s){case"input":Ll(o,i);break;case"textarea":Oc(o,i);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;h!=null?wn(o,!!i.multiple,h,!1):d!==!!i.multiple&&(i.defaultValue!=null?wn(o,!!i.multiple,i.defaultValue,!0):wn(o,!!i.multiple,i.multiple?[]:"",!1))}o[Lr]=i}catch(S){Y(e,e.return,S)}}break;case 6:if(Qe(t,e),et(e),r&4){if(e.stateNode===null)throw Error(C(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(S){Y(e,e.return,S)}}break;case 3:if(Qe(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Sr(t.containerInfo)}catch(S){Y(e,e.return,S)}break;case 4:Qe(t,e),et(e);break;case 13:Qe(t,e),et(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(ca=J())),r&4&&Tu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||c,Qe(t,e),_e=u):Qe(t,e),et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(T=e,c=e.child;c!==null;){for(p=T=c;T!==null;){switch(d=T,h=d.child,d.tag){case 0:case 11:case 14:case 15:fr(4,d,d.return);break;case 1:Sn(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(S){Y(r,n,S)}}break;case 5:Sn(d,d.return);break;case 22:if(d.memoizedState!==null){Iu(p);continue}}h!==null?(h.return=d,T=h):Iu(p)}c=c.sibling}e:for(c=null,p=e;;){if(p.tag===5){if(c===null){c=p;try{o=p.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=p.stateNode,a=p.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=jc("display",l))}catch(S){Y(e,e.return,S)}}}else if(p.tag===6){if(c===null)try{p.stateNode.nodeValue=u?"":p.memoizedProps}catch(S){Y(e,e.return,S)}}else if((p.tag!==22&&p.tag!==23||p.memoizedState===null||p===e)&&p.child!==null){p.child.return=p,p=p.child;continue}if(p===e)break e;for(;p.sibling===null;){if(p.return===null||p.return===e)break e;c===p&&(c=null),p=p.return}c===p&&(c=null),p.sibling.return=p.return,p=p.sibling}}break;case 19:Qe(t,e),et(e),r&4&&Tu(e);break;case 21:break;default:Qe(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(uf(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(_r(o,""),r.flags&=-33);var i=Ru(e);ss(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Ru(e);ls(e,s,l);break;default:throw Error(C(161))}}catch(a){Y(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Ih(e,t,n){T=e,ff(e)}function ff(e,t,n){for(var r=(e.mode&1)!==0;T!==null;){var o=T,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||uo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||_e;s=uo;var u=_e;if(uo=l,(_e=a)&&!u)for(T=o;T!==null;)l=T,a=l.child,l.tag===22&&l.memoizedState!==null?Au(o):a!==null?(a.return=l,T=a):Au(o);for(;i!==null;)T=i,ff(i),i=i.sibling;T=o,uo=s,_e=u}Nu(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,T=i):Nu(e)}}function Nu(e){for(;T!==null;){var t=T;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||xi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&mu(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}mu(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var p=c.dehydrated;p!==null&&Sr(p)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}_e||t.flags&512&&is(t)}catch(d){Y(t,t.return,d)}}if(t===e){T=null;break}if(n=t.sibling,n!==null){n.return=t.return,T=n;break}T=t.return}}function Iu(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var n=t.sibling;if(n!==null){n.return=t.return,T=n;break}T=t.return}}function Au(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{xi(4,t)}catch(a){Y(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){Y(t,o,a)}}var i=t.return;try{is(t)}catch(a){Y(t,i,a)}break;case 5:var l=t.return;try{is(t)}catch(a){Y(t,l,a)}}}catch(a){Y(t,t.return,a)}if(t===e){T=null;break}var s=t.sibling;if(s!==null){s.return=t.return,T=s;break}T=t.return}}var Ah=Math.ceil,Yo=xt.ReactCurrentDispatcher,aa=xt.ReactCurrentOwner,Ge=xt.ReactCurrentBatchConfig,z=0,ce=null,te=null,fe=0,Ie=0,En=$t(0),ie=0,Ar=null,on=0,Si=0,ua=0,pr=null,ke=null,ca=0,Vn=1/0,st=null,Xo=!1,as=null,Dt=null,co=!1,Rt=null,Jo=0,mr=0,us=null,Lo=-1,Po=0;function Se(){return z&6?J():Lo!==-1?Lo:Lo=J()}function jt(e){return e.mode&1?z&2&&fe!==0?fe&-fe:hh.transition!==null?(Po===0&&(Po=qc()),Po):(e=B,e!==0||(e=window.event,e=e===void 0?16:nd(e.type)),e):1}function Je(e,t,n,r){if(50<mr)throw mr=0,us=null,Error(C(185));zr(e,n,r),(!(z&2)||e!==ce)&&(e===ce&&(!(z&2)&&(Si|=n),ie===4&&Lt(e,fe)),Re(e,r),n===1&&z===0&&!(t.mode&1)&&(Vn=J()+500,_i&&Ft()))}function Re(e,t){var n=e.callbackNode;hm(e,t);var r=Do(e,e===ce?fe:0);if(r===0)n!==null&&$a(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&$a(n),t===1)e.tag===0?mh(Ou.bind(null,e)):Sd(Ou.bind(null,e)),ch(function(){!(z&6)&&Ft()}),n=null;else{switch(Yc(r)){case 1:n=js;break;case 4:n=Qc;break;case 16:n=bo;break;case 536870912:n=Kc;break;default:n=bo}n=xf(n,pf.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function pf(e,t){if(Lo=-1,Po=0,z&6)throw Error(C(327));var n=e.callbackNode;if(Rn()&&e.callbackNode!==n)return null;var r=Do(e,e===ce?fe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Zo(e,r);else{t=r;var o=z;z|=2;var i=hf();(ce!==e||fe!==t)&&(st=null,Vn=J()+500,Zt(e,t));do try{Dh();break}catch(s){mf(e,s)}while(!0);qs(),Yo.current=i,z=o,te!==null?t=0:(ce=null,fe=0,t=ie)}if(t!==0){if(t===2&&(o=jl(e),o!==0&&(r=o,t=cs(e,o))),t===1)throw n=Ar,Zt(e,0),Lt(e,r),Re(e,J()),n;if(t===6)Lt(e,r);else{if(o=e.current.alternate,!(r&30)&&!Oh(o)&&(t=Zo(e,r),t===2&&(i=jl(e),i!==0&&(r=i,t=cs(e,i))),t===1))throw n=Ar,Zt(e,0),Lt(e,r),Re(e,J()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Qt(e,ke,st);break;case 3:if(Lt(e,r),(r&130023424)===r&&(t=ca+500-J(),10<t)){if(Do(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Se(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Gl(Qt.bind(null,e,ke,st),t);break}Qt(e,ke,st);break;case 4:if(Lt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-Xe(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ah(r/1960))-r,10<r){e.timeoutHandle=Gl(Qt.bind(null,e,ke,st),r);break}Qt(e,ke,st);break;case 5:Qt(e,ke,st);break;default:throw Error(C(329))}}}return Re(e,J()),e.callbackNode===n?pf.bind(null,e):null}function cs(e,t){var n=pr;return e.current.memoizedState.isDehydrated&&(Zt(e,t).flags|=256),e=Zo(e,t),e!==2&&(t=ke,ke=n,t!==null&&ds(t)),e}function ds(e){ke===null?ke=e:ke.push.apply(ke,e)}function Oh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Ze(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Lt(e,t){for(t&=~ua,t&=~Si,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function Ou(e){if(z&6)throw Error(C(327));Rn();var t=Do(e,0);if(!(t&1))return Re(e,J()),null;var n=Zo(e,t);if(e.tag!==0&&n===2){var r=jl(e);r!==0&&(t=r,n=cs(e,r))}if(n===1)throw n=Ar,Zt(e,0),Lt(e,t),Re(e,J()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Qt(e,ke,st),Re(e,J()),null}function da(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Vn=J()+500,_i&&Ft())}}function ln(e){Rt!==null&&Rt.tag===0&&!(z&6)&&Rn();var t=z;z|=1;var n=Ge.transition,r=B;try{if(Ge.transition=null,B=1,e)return e()}finally{B=r,Ge.transition=n,z=t,!(z&6)&&Ft()}}function fa(){Ie=En.current,H(En)}function Zt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,uh(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(Ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Bo();break;case 3:jn(),H(Le),H(ye),ta();break;case 5:ea(r);break;case 4:jn();break;case 13:H(Q);break;case 19:H(Q);break;case 10:Ys(r.type._context);break;case 22:case 23:fa()}n=n.return}if(ce=e,te=e=Mt(e.current,null),fe=Ie=t,ie=0,Ar=null,ua=Si=on=0,ke=pr=null,qt!==null){for(t=0;t<qt.length;t++)if(n=qt[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}qt=null}return e}function mf(e,t){do{var n=te;try{if(qs(),wo.current=qo,Ko){for(var r=K.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ko=!1}if(rn=0,ue=re=K=null,dr=!1,Tr=0,aa.current=null,n===null||n.return===null){ie=1,Ar=t,te=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=fe,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=s,p=c.tag;if(!(c.mode&1)&&(p===0||p===11||p===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var h=xu(l);if(h!==null){h.flags&=-257,Su(h,l,s,i,t),h.mode&1&&vu(i,u,t),t=h,a=u;var v=t.updateQueue;if(v===null){var S=new Set;S.add(a),t.updateQueue=S}else v.add(a);break e}else{if(!(t&1)){vu(i,u,t),pa();break e}a=Error(C(426))}}else if(W&&s.mode&1){var k=xu(l);if(k!==null){!(k.flags&65536)&&(k.flags|=256),Su(k,l,s,i,t),Qs(Mn(a,s));break e}}i=a=Mn(a,s),ie!==4&&(ie=2),pr===null?pr=[i]:pr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var g=Yd(i,a,t);pu(i,g);break e;case 1:s=a;var m=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof m.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Dt===null||!Dt.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Xd(i,s,t);pu(i,x);break e}}i=i.return}while(i!==null)}_f(n)}catch(w){t=w,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function hf(){var e=Yo.current;return Yo.current=qo,e===null?qo:e}function pa(){(ie===0||ie===3||ie===2)&&(ie=4),ce===null||!(on&268435455)&&!(Si&268435455)||Lt(ce,fe)}function Zo(e,t){var n=z;z|=2;var r=hf();(ce!==e||fe!==t)&&(st=null,Zt(e,t));do try{bh();break}catch(o){mf(e,o)}while(!0);if(qs(),z=n,Yo.current=r,te!==null)throw Error(C(261));return ce=null,fe=0,ie}function bh(){for(;te!==null;)gf(te)}function Dh(){for(;te!==null&&!lm();)gf(te)}function gf(e){var t=vf(e.alternate,e,Ie);e.memoizedProps=e.pendingProps,t===null?_f(e):te=t,aa.current=null}function _f(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Rh(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ie=6,te=null;return}}else if(n=Ph(n,t,Ie),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);ie===0&&(ie=5)}function Qt(e,t,n){var r=B,o=Ge.transition;try{Ge.transition=null,B=1,jh(e,t,n,r)}finally{Ge.transition=o,B=r}return null}function jh(e,t,n,r){do Rn();while(Rt!==null);if(z&6)throw Error(C(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(gm(e,i),e===ce&&(te=ce=null,fe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||co||(co=!0,xf(bo,function(){return Rn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ge.transition,Ge.transition=null;var l=B;B=1;var s=z;z|=4,aa.current=null,Nh(e,n),df(n,e),nh(Fl),jo=!!$l,Fl=$l=null,e.current=n,Ih(n),sm(),z=s,B=l,Ge.transition=i}else e.current=n;if(co&&(co=!1,Rt=e,Jo=o),i=e.pendingLanes,i===0&&(Dt=null),cm(n.stateNode),Re(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Xo)throw Xo=!1,e=as,as=null,e;return Jo&1&&e.tag!==0&&Rn(),i=e.pendingLanes,i&1?e===us?mr++:(mr=0,us=e):mr=0,Ft(),null}function Rn(){if(Rt!==null){var e=Yc(Jo),t=Ge.transition,n=B;try{if(Ge.transition=null,B=16>e?16:e,Rt===null)var r=!1;else{if(e=Rt,Rt=null,Jo=0,z&6)throw Error(C(331));var o=z;for(z|=4,T=e.current;T!==null;){var i=T,l=i.child;if(T.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(T=u;T!==null;){var c=T;switch(c.tag){case 0:case 11:case 15:fr(8,c,i)}var p=c.child;if(p!==null)p.return=c,T=p;else for(;T!==null;){c=T;var d=c.sibling,h=c.return;if(af(c),c===u){T=null;break}if(d!==null){d.return=h,T=d;break}T=h}}}var v=i.alternate;if(v!==null){var S=v.child;if(S!==null){v.child=null;do{var k=S.sibling;S.sibling=null,S=k}while(S!==null)}}T=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,T=l;else e:for(;T!==null;){if(i=T,i.flags&2048)switch(i.tag){case 0:case 11:case 15:fr(9,i,i.return)}var g=i.sibling;if(g!==null){g.return=i.return,T=g;break e}T=i.return}}var m=e.current;for(T=m;T!==null;){l=T;var y=l.child;if(l.subtreeFlags&2064&&y!==null)y.return=l,T=y;else e:for(l=m;T!==null;){if(s=T,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:xi(9,s)}}catch(w){Y(s,s.return,w)}if(s===l){T=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,T=x;break e}T=s.return}}if(z=o,Ft(),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(fi,e)}catch{}r=!0}return r}finally{B=n,Ge.transition=t}}return!1}function bu(e,t,n){t=Mn(n,t),t=Yd(e,t,1),e=bt(e,t,1),t=Se(),e!==null&&(zr(e,1,t),Re(e,t))}function Y(e,t,n){if(e.tag===3)bu(e,e,n);else for(;t!==null;){if(t.tag===3){bu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Dt===null||!Dt.has(r))){e=Mn(n,e),e=Xd(t,e,1),t=bt(t,e,1),e=Se(),t!==null&&(zr(t,1,e),Re(t,e));break}}t=t.return}}function Mh(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Se(),e.pingedLanes|=e.suspendedLanes&n,ce===e&&(fe&n)===n&&(ie===4||ie===3&&(fe&130023424)===fe&&500>J()-ca?Zt(e,0):ua|=n),Re(e,t)}function yf(e,t){t===0&&(e.mode&1?(t=eo,eo<<=1,!(eo&130023424)&&(eo=4194304)):t=1);var n=Se();e=yt(e,t),e!==null&&(zr(e,t,n),Re(e,n))}function Vh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),yf(e,n)}function zh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),yf(e,n)}var vf;vf=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Le.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,Lh(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,W&&t.flags&1048576&&Ed(t,Uo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Co(e,t),e=t.pendingProps;var o=On(t,ye.current);Pn(t,n),o=ra(null,t,r,e,o,n);var i=oa();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(i=!0,$o(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Js(t),o.updater=vi,t.stateNode=o,o._reactInternals=t,Xl(t,r,e,n),t=es(null,t,r,!0,i,n)):(t.tag=0,W&&i&&Hs(t),ve(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Co(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=$h(r),e=Ke(r,e),o){case 0:t=Zl(null,t,r,e,n);break e;case 1:t=ku(null,t,r,e,n);break e;case 11:t=Eu(null,t,r,e,n);break e;case 14:t=wu(null,t,r,Ke(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),Zl(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),ku(e,t,r,o,n);case 3:e:{if(tf(t),e===null)throw Error(C(387));r=t.pendingProps,i=t.memoizedState,o=i.element,Rd(e,t),Wo(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Mn(Error(C(423)),t),t=Cu(e,t,r,n,o);break e}else if(r!==o){o=Mn(Error(C(424)),t),t=Cu(e,t,r,n,o);break e}else for(Ae=Ot(t.stateNode.containerInfo.firstChild),Oe=t,W=!0,Ye=null,n=Ld(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(bn(),r===o){t=vt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Td(t),e===null&&Kl(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Ul(r,o)?l=null:i!==null&&Ul(r,i)&&(t.flags|=32),ef(e,t),ve(e,t,l,n),t.child;case 6:return e===null&&Kl(t),null;case 13:return nf(e,t,n);case 4:return Zs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Dn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),Eu(e,t,r,o,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,U(Go,r._currentValue),r._currentValue=l,i!==null)if(Ze(i.value,l)){if(i.children===o.children&&!Le.current){t=vt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=mt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),ql(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(C(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),ql(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ve(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Pn(t,n),o=He(o),r=r(o),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,o=Ke(r,t.pendingProps),o=Ke(r.type,o),wu(e,t,r,o,n);case 15:return Jd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ke(r,o),Co(e,t),t.tag=1,Pe(r)?(e=!0,$o(t)):e=!1,Pn(t,n),qd(t,r,o),Xl(t,r,o,n),es(null,t,r,!0,e,n);case 19:return rf(e,t,n);case 22:return Zd(e,t,n)}throw Error(C(156,t.tag))};function xf(e,t){return Wc(e,t)}function Bh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new Bh(e,t,n,r)}function ma(e){return e=e.prototype,!(!e||!e.isReactComponent)}function $h(e){if(typeof e=="function")return ma(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Os)return 11;if(e===bs)return 14}return 2}function Mt(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ro(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")ma(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case fn:return en(n.children,o,i,t);case As:l=8,o|=8;break;case Sl:return e=Ue(12,n,t,o|2),e.elementType=Sl,e.lanes=i,e;case El:return e=Ue(13,n,t,o),e.elementType=El,e.lanes=i,e;case wl:return e=Ue(19,n,t,o),e.elementType=wl,e.lanes=i,e;case Tc:return Ei(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Pc:l=10;break e;case Rc:l=9;break e;case Os:l=11;break e;case bs:l=14;break e;case wt:l=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Ue(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function en(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Ei(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Tc,e.lanes=n,e.stateNode={isHidden:!1},e}function ll(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function sl(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fh(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=$i(0),this.expirationTimes=$i(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$i(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function ha(e,t,n,r,o,i,l,s,a){return e=new Fh(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ue(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Js(i),e}function Uh(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:dn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Sf(e){if(!e)return zt;e=e._reactInternals;e:{if(an(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Pe(n))return xd(e,n,t)}return t}function Ef(e,t,n,r,o,i,l,s,a){return e=ha(n,r,!0,e,o,i,l,s,a),e.context=Sf(null),n=e.current,r=Se(),o=jt(n),i=mt(r,o),i.callback=t??null,bt(n,i,o),e.current.lanes=o,zr(e,o,r),Re(e,r),e}function wi(e,t,n,r){var o=t.current,i=Se(),l=jt(o);return n=Sf(n),t.context===null?t.context=n:t.pendingContext=n,t=mt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bt(o,t,l),e!==null&&(Je(e,o,l,i),Eo(e,o,l)),l}function ei(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Du(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ga(e,t){Du(e,t),(e=e.alternate)&&Du(e,t)}function Gh(){return null}var wf=typeof reportError=="function"?reportError:function(e){console.error(e)};function _a(e){this._internalRoot=e}ki.prototype.render=_a.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));wi(e,t,null,null)};ki.prototype.unmount=_a.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ln(function(){wi(null,e,null,null)}),t[_t]=null}};function ki(e){this._internalRoot=e}ki.prototype.unstable_scheduleHydration=function(e){if(e){var t=Zc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&td(e)}};function ya(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ci(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ju(){}function Hh(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=ei(l);i.call(u)}}var l=Ef(t,r,e,0,null,!1,!1,"",ju);return e._reactRootContainer=l,e[_t]=l.current,kr(e.nodeType===8?e.parentNode:e),ln(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=ei(a);s.call(u)}}var a=ha(e,0,!1,null,null,!1,!1,"",ju);return e._reactRootContainer=a,e[_t]=a.current,kr(e.nodeType===8?e.parentNode:e),ln(function(){wi(t,a,n,r)}),a}function Li(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=ei(l);s.call(a)}}wi(t,l,e,o)}else l=Hh(n,t,e,o,r);return ei(l)}Xc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=rr(t.pendingLanes);n!==0&&(Ms(t,n|1),Re(t,J()),!(z&6)&&(Vn=J()+500,Ft()))}break;case 13:ln(function(){var r=yt(e,1);if(r!==null){var o=Se();Je(r,e,1,o)}}),ga(e,1)}};Vs=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=Se();Je(t,e,134217728,n)}ga(e,134217728)}};Jc=function(e){if(e.tag===13){var t=jt(e),n=yt(e,t);if(n!==null){var r=Se();Je(n,e,t,r)}ga(e,t)}};Zc=function(){return B};ed=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};Ol=function(e,t,n){switch(t){case"input":if(Ll(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=gi(r);if(!o)throw Error(C(90));Ic(r),Ll(r,o)}}}break;case"textarea":Oc(e,n);break;case"select":t=n.value,t!=null&&wn(e,!!n.multiple,t,!1)}};Bc=da;$c=ln;var Wh={usingClientEntryPoint:!1,Events:[$r,gn,gi,Vc,zc,da]},Zn={findFiberByHostInstance:Kt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Qh={bundleType:Zn.bundleType,version:Zn.version,rendererPackageName:Zn.rendererPackageName,rendererConfig:Zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Gc(e),e===null?null:e.stateNode},findFiberByHostInstance:Zn.findFiberByHostInstance||Gh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fo.isDisabled&&fo.supportsFiber)try{fi=fo.inject(Qh),it=fo}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Wh;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ya(t))throw Error(C(200));return Uh(e,t,null,n)};De.createRoot=function(e,t){if(!ya(e))throw Error(C(299));var n=!1,r="",o=wf;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=ha(e,1,!1,null,null,n,!1,r,o),e[_t]=t.current,kr(e.nodeType===8?e.parentNode:e),new _a(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Gc(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return ln(e)};De.hydrate=function(e,t,n){if(!Ci(t))throw Error(C(200));return Li(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!ya(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=wf;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Ef(t,null,e,1,n??null,o,!1,i,l),e[_t]=t.current,kr(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ki(t)};De.render=function(e,t,n){if(!Ci(t))throw Error(C(200));return Li(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!Ci(e))throw Error(C(40));return e._reactRootContainer?(ln(function(){Li(null,null,e,!1,function(){e._reactRootContainer=null,e[_t]=null})}),!0):!1};De.unstable_batchedUpdates=da;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ci(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return Li(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function kf(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(kf)}catch(e){console.error(e)}}kf(),wc.exports=De;var Kh=wc.exports,Mu=Kh;vl.createRoot=Mu.createRoot,vl.hydrateRoot=Mu.hydrateRoot;const qh="modulepreload",Yh=function(e,t){return new URL(e,t).href},Vu={},f=function(t,n,r){let o=Promise.resolve();if(n&&n.length>0){const l=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=Yh(u,r),u in Vu)return;Vu[u]=!0;const c=u.endsWith(".css"),p=c?'[rel="stylesheet"]':"";if(!!r)for(let v=l.length-1;v>=0;v--){const S=l[v];if(S.href===u&&(!c||S.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${u}"]${p}`))return;const h=document.createElement("link");if(h.rel=c?"stylesheet":qh,c||(h.as="script"),h.crossOrigin="",h.href=u,a&&h.setAttribute("nonce",a),document.head.appendChild(h),c)return new Promise((v,S)=>{h.addEventListener("load",v),h.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(l){const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=l,window.dispatchEvent(s),!s.defaultPrevented)throw l}return o.then(l=>{for(const s of l||[])s.status==="rejected"&&i(s.reason);return t().catch(i)})},Xh={},zu=e=>{let t;const n=new Set,r=(c,p)=>{const d=typeof c=="function"?c(t):c;if(!Object.is(d,t)){const h=t;t=p??(typeof d!="object"||d===null)?d:Object.assign({},t,d),n.forEach(v=>v(t,h))}},o=()=>t,a={setState:r,getState:o,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{(Xh?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,o,a);return a},Jh=e=>e?zu(e):zu;var Cf={exports:{}},Lf={},Pf={exports:{}},Rf={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zn=O;function Zh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var eg=typeof Object.is=="function"?Object.is:Zh,tg=zn.useState,ng=zn.useEffect,rg=zn.useLayoutEffect,og=zn.useDebugValue;function ig(e,t){var n=t(),r=tg({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return rg(function(){o.value=n,o.getSnapshot=t,al(o)&&i({inst:o})},[e,n,t]),ng(function(){return al(o)&&i({inst:o}),e(function(){al(o)&&i({inst:o})})},[e]),og(n),n}function al(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!eg(e,n)}catch{return!0}}function lg(e,t){return t()}var sg=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?lg:ig;Rf.useSyncExternalStore=zn.useSyncExternalStore!==void 0?zn.useSyncExternalStore:sg;Pf.exports=Rf;var ag=Pf.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pi=O,ug=ag;function cg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var dg=typeof Object.is=="function"?Object.is:cg,fg=ug.useSyncExternalStore,pg=Pi.useRef,mg=Pi.useEffect,hg=Pi.useMemo,gg=Pi.useDebugValue;Lf.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=pg(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=hg(function(){function a(h){if(!u){if(u=!0,c=h,h=r(h),o!==void 0&&l.hasValue){var v=l.value;if(o(v,h))return p=v}return p=h}if(v=p,dg(c,h))return v;var S=r(h);return o!==void 0&&o(v,S)?(c=h,v):(c=h,p=S)}var u=!1,c,p,d=n===void 0?null:n;return[function(){return a(t())},d===null?void 0:function(){return a(d())}]},[t,n,r,o]);var s=fg(e,i[0],i[1]);return mg(function(){l.hasValue=!0,l.value=s},[s]),gg(s),s};Cf.exports=Lf;var _g=Cf.exports;const yg=dc(_g),Tf={},{useDebugValue:vg}=Rs,{useSyncExternalStoreWithSelector:xg}=yg;let Bu=!1;const Sg=e=>e;function Eg(e,t=Sg,n){(Tf?"production":void 0)!=="production"&&n&&!Bu&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Bu=!0);const r=xg(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return vg(r),r}const $u=e=>{(Tf?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Jh(e):e,n=(r,o)=>Eg(t,r,o);return Object.assign(n,t),n},Ri=e=>e?$u(e):$u;let ul=null;const Tn=()=>typeof window<"u"&&!!window.CSInterface,va=()=>{if(!ul&&Tn())try{ul=new window.CSInterface,console.log("CSInterface initialized successfully")}catch(e){console.error("Failed to initialize CSInterface:",e)}return ul},Nf=()=>{if(!Tn()){console.warn("Not running in CEP environment");return}const e=va();if(!e)return;e.addEventListener("com.adobe.csxs.events.ThemeColorChanged",n=>{console.log("Theme changed:",n)});const t=e.getHostEnvironment();console.log("Host environment:",t),e.evalScript("SahAI.getAppInfo()",n=>{try{if(!n||n.trim()===""){console.warn("Empty response from ExtendScript");return}const r=JSON.parse(n);console.log("ExtendScript response:",r)}catch(r){console.error("Failed to parse ExtendScript response:",r,"Raw result:",n)}})},ft=(e,t=3e4)=>new Promise((n,r)=>{const o=va();if(!o){r(new Error("CSInterface not available - not running in CEP environment"));return}const i=setTimeout(()=>{r(new Error(`ExtendScript execution timed out after ${t}ms`))},t);try{o.evalScript(e,l=>{clearTimeout(i);try{if(typeof l=="string"&&l.startsWith("EvalScript error")){r(new Error(`ExtendScript Error: ${l}`));return}if(!l||l.trim()===""){r(new Error("Empty response from ExtendScript"));return}let s;try{s=JSON.parse(l)}catch{s={success:!0,data:l}}typeof s=="object"&&s!==null?s.success===!1?r(new Error(s.message||"ExtendScript execution failed")):n(s):n({success:!0,data:s})}catch(s){r(new Error(`Failed to process ExtendScript response: ${s}`))}})}catch(l){clearTimeout(i),r(new Error(`Failed to execute ExtendScript: ${l}`))}});class Tt{static async save(t){const n=JSON.stringify(t);try{if(Tn())try{const r=await ft(`saveSettings(${JSON.stringify(t)})`,1e4);if(r.success)console.log("Settings saved to CEP storage successfully");else throw new Error(r.message||"CEP save failed")}catch(r){console.warn("CEP storage save failed, falling back to localStorage:",r)}localStorage.setItem(this.SETTINGS_KEY,n),console.log("Settings saved to localStorage successfully")}catch(r){console.error("All settings save methods failed:",r);try{localStorage.setItem(this.SETTINGS_KEY,n)}catch(o){throw new Error(`Failed to save settings: ${r}. LocalStorage also failed: ${o}`)}}}static async load(){try{if(Tn())try{const n=await ft("loadSettings()",1e4);if(n.success&&n.data)return console.log("Settings loaded from CEP storage successfully"),n.data}catch(n){console.warn("CEP storage load failed, falling back to localStorage:",n)}const t=localStorage.getItem(this.SETTINGS_KEY);if(t){const n=JSON.parse(t);return console.log("Settings loaded from localStorage successfully"),n}return console.log("No existing settings found, returning defaults"),{providers:[]}}catch(t){return console.error("All settings load methods failed:",t),{providers:[]}}}static async exportSettings(){const t=await this.load();return JSON.stringify(t,null,2)}static async importSettings(t){try{const n=JSON.parse(t);await this.save(n)}catch{throw new Error("Invalid settings format")}}static async clearSettings(){try{if(Tn())try{await ft("saveSettings({})",1e4)}catch(t){console.warn("Failed to clear CEP storage:",t)}localStorage.removeItem(this.SETTINGS_KEY),console.log("Settings cleared successfully")}catch(t){throw new Error(`Failed to clear settings: ${t}`)}}}E(Tt,"SETTINGS_KEY","sahAI_settings");class If{static async checkProviderStatus(t,n){const r=Date.now();try{const{ProviderBridge:o}=await f(async()=>{const{ProviderBridge:l}=await Promise.resolve().then(()=>Af);return{ProviderBridge:l}},void 0,import.meta.url);return{isOnline:(await o.listModels(t,n.baseURL,n.apiKey)).length>0,latency:Date.now()-r}}catch(o){return{isOnline:!1,error:o.message||String(o),latency:Date.now()-r}}}}const wg={async listModels(e,t,n){try{const r=`listModels('${e}', '${t||""}', '${n||""}')`,o=await ft(r,15e3);if(o&&typeof o=="object"){if(o.success&&o.data){const i=typeof o.data=="string"?JSON.parse(o.data):o.data;return i.ok?i.models:this.getFallbackModels(e)}if(o.ok)return o.models||[]}return console.warn(`Failed to fetch models for ${e}, using fallback`),this.getFallbackModels(e)}catch(r){return console.error(`Error fetching models for ${e}:`,r),this.getFallbackModels(e)}},getFallbackModels(e){return{openai:[{id:"gpt-4o",name:"GPT-4o",description:"Most capable OpenAI model",contextLength:128e3,isRecommended:!0},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster, more affordable",contextLength:128e3},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Legacy model",contextLength:16384}],anthropic:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Anthropic's most capable model",contextLength:2e5,isRecommended:!0},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fast and efficient",contextLength:2e5},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Powerful reasoning",contextLength:2e5}],gemini:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Google's most capable model",contextLength:2e6,isRecommended:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6}],groq:[{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Fast inference",contextLength:131072},{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Balanced performance",contextLength:131072,isRecommended:!0},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Large context",contextLength:32768}],deepseek:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose",contextLength:128e3,isRecommended:!0},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Code-focused",contextLength:128e3}],mistral:[{id:"mistral-large-latest",name:"Mistral Large",description:"Most capable",contextLength:128e3,isRecommended:!0},{id:"mistral-medium-latest",name:"Mistral Medium",description:"Balanced",contextLength:32e3},{id:"mistral-small-latest",name:"Mistral Small",description:"Fast and efficient",contextLength:32e3}],ollama:[{id:"llama2",name:"Llama 2",description:"Open source LLM",contextLength:4096},{id:"mistral",name:"Mistral",description:"Efficient transformer",contextLength:8192},{id:"codellama",name:"Code Llama",description:"Code-focused",contextLength:16384}],lmstudio:[{id:"local-model",name:"Local Model",description:"Your local model",contextLength:4096}]}[e]||[]}},Af=Object.freeze(Object.defineProperty({__proto__:null,CEPSettings:Tt,ProviderBridge:wg,ProviderStatusChecker:If,executeExtendScript:ft,getCSInterface:va,initializeCEP:Nf,isCEPEnvironment:Tn},Symbol.toStringTag,{value:"Module"})),Ti=Ri((e,t)=>({providers:[{id:"openai",name:"OpenAI",isConfigured:!1,models:[]},{id:"anthropic",name:"Anthropic",isConfigured:!1,models:[]},{id:"gemini",name:"Google Gemini",isConfigured:!1,models:[]},{id:"groq",name:"Groq",isConfigured:!1,models:[]},{id:"deepseek",name:"DeepSeek",isConfigured:!1,models:[]},{id:"mistral",name:"Mistral",isConfigured:!1,models:[]},{id:"moonshot",name:"Moonshot AI",isConfigured:!1,models:[]},{id:"openrouter",name:"OpenRouter",isConfigured:!1,models:[]},{id:"perplexity",name:"Perplexity",isConfigured:!1,models:[]},{id:"qwen",name:"Alibaba Qwen",isConfigured:!1,models:[]},{id:"together",name:"Together AI",isConfigured:!1,models:[]},{id:"vertex",name:"Google Vertex AI",isConfigured:!1,models:[]},{id:"xai",name:"xAI",isConfigured:!1,models:[]},{id:"ollama",name:"Ollama",isConfigured:!1,models:[]},{id:"lmstudio",name:"LM Studio",isConfigured:!1,models:[]}],activeProviderId:void 0,isLoadingModels:!1,setActiveProvider:n=>{e({activeProviderId:n}),t().persistSettings()},updateProviderConfig:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,...r,isConfigured:!!r.apiKey}:i)})),t().persistSettings()},setProviderModels:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,models:r,isLoading:!1,error:void 0}:i)}))},setSelectedModel:(n,r)=>{e(o=>({providers:o.providers.map(i=>i.id===n?{...i,selectedModelId:r}:i)})),t().persistSettings()},updateProviderKey:(n,r,o)=>{e(i=>({providers:i.providers.map(l=>l.id===n?{...l,apiKey:r,isConfigured:!!r,selectedModelId:o||l.selectedModelId}:l)})),t().persistSettings()},saveProviderSelection:(n,r)=>{e(o=>({activeProviderId:n,providers:o.providers.map(i=>i.id===n?{...i,...r,isConfigured:!!(r.apiKey||i.baseURL)}:i)})),t().persistSettings()},loadModelsForProvider:async n=>{const r=t().providers.find(o=>o.id===n);if(r!=null&&r.isConfigured){e(o=>({providers:o.providers.map(i=>i.id===n?{...i,isLoading:!0,error:void 0}:i)}));try{const{ProviderBridge:o}=await f(async()=>{const{ProviderBridge:s}=await Promise.resolve().then(()=>Af);return{ProviderBridge:s}},void 0,import.meta.url),l=(await o.listModels(n,r.baseURL,r.apiKey)).map(s=>({id:s.id,name:s.name,description:s.description,contextLength:s.contextLength,isRecommended:s.isRecommended}));t().setProviderModels(n,l)}catch(o){e(i=>({providers:i.providers.map(l=>l.id===n?{...l,isLoading:!1,error:o.message||String(o)}:l)}))}}},persistSettings:()=>{const{activeProviderId:n,providers:r}=t();Tt.save({activeProviderId:n,providers:r.map(o=>({id:o.id,isConfigured:o.isConfigured,apiKey:o.apiKey,baseURL:o.baseURL,selectedModelId:o.selectedModelId,settings:o.settings}))})},loadSettings:async()=>{try{const n=await Tt.load();n.activeProviderId&&e({activeProviderId:n.activeProviderId}),n.providers&&Array.isArray(n.providers)&&e(r=>({providers:r.providers.map(o=>{var l;const i=(l=n.providers)==null?void 0:l.find(s=>s.id===o.id);return i?{...o,...i}:o})}))}catch(n){console.error("Failed to load CEP settings:",n)}},getActiveProvider:()=>{const{providers:n,activeProviderId:r}=t();return n.find(o=>o.id===r)||null},getActiveModel:()=>{const n=t().getActiveProvider();return n!=null&&n.selectedModelId&&n.models.find(r=>r.id===n.selectedModelId)||null}})),Ur=Ri(e=>({modal:null,openModal:t=>e({modal:t}),closeModal:()=>e({modal:null})})),Ni=Ri(e=>({messages:[],isLoading:!1,addMessage:t=>e(n=>({messages:[...n.messages,{...t,id:crypto.randomUUID(),timestamp:Date.now()}]})),setLoading:t=>e({isLoading:t}),createNewSession:()=>e({messages:[],currentSession:crypto.randomUUID()})}));/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kg=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Of=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Cg={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lg=O.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>O.createElement("svg",{ref:a,...Cg,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Of("lucide",o),...s},[...l.map(([u,c])=>O.createElement(u,c)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ne=(e,t)=>{const n=O.forwardRef(({className:r,...o},i)=>O.createElement(Lg,{ref:i,iconNode:t,className:Of(`lucide-${kg(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pg=ne("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rg=ne("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tg=ne("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ng=ne("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ig=ne("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bf=ne("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ag=ne("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Or=ne("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fu=ne("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Og=ne("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bg=ne("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dg=ne("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jg=ne("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=ne("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mg=ne("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vg=ne("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ps=ne("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zg=ne("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bg=ne("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.408.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $g=ne("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]),Fg=()=>{const{getActiveProvider:e}=Ti(),[t,n]=O.useState({isOnline:null,isChecking:!1}),r=e();O.useEffect(()=>{let s;const a=async()=>{if(!(r!=null&&r.isConfigured)){n({isOnline:null,isChecking:!1});return}n(u=>({...u,isChecking:!0,error:void 0}));try{const u=await If.checkProviderStatus(r.id,{apiKey:r.apiKey,baseURL:r.baseURL});n({isOnline:u.isOnline,latency:u.latency,isChecking:!1})}catch(u){n({isOnline:!1,isChecking:!1,error:u.message})}};return r!=null&&r.isConfigured&&(a(),s=setInterval(a,3e4)),()=>{s&&clearInterval(s)}},[r]);const o=()=>t.isChecking?_.jsx(Or,{size:12,className:"animate-spin"}):t.isOnline===!0?_.jsx($g,{size:12}):t.isOnline===!1?_.jsx(Bg,{size:12}):_.jsx(Ng,{size:12}),i=()=>t.isChecking?"text-yellow-500":t.isOnline===!0?"text-green-500":"text-red-500",l=()=>r?t.isChecking?"Checking connection…":t.isOnline===!0?`${r.name} online${t.latency?` (${t.latency} ms)`:""}`:t.error?`${r.name} error: ${t.error}`:`${r.name} offline`:"No provider selected";return _.jsxs("div",{className:`flex items-center space-x-1.5 ${i()}`,title:l(),children:[o(),_.jsx("span",{className:"text-xs font-medium hidden sm:inline",children:t.isOnline===!0?"Online":t.isOnline===!1?"Offline":"Unknown"})]})},Ug=()=>{const{getActiveProvider:e,getActiveModel:t,loadSettings:n}=Ti(),{openModal:r}=Ur(),{createNewSession:o}=Ni(),i=e(),l=t(),s=O.useMemo(()=>i?l?`${i.name} • ${l.name}`:`${i.name} • Select Model`:"Select AI Provider & Model",[i,l]);return O.useEffect(()=>{n()},[n]),_.jsxs("header",{className:"flex items-center justify-between px-4 py-3 border-b border-adobe-border bg-adobe-bg-secondary shadow-sm",children:[_.jsxs("div",{className:"flex items-center space-x-3",children:[_.jsx(Fg,{}),_.jsx("div",{className:"h-4 w-px bg-adobe-border"}),_.jsxs("button",{onClick:()=>r("provider"),className:"flex items-center space-x-2 text-sm font-medium text-adobe-text-primary hover:text-adobe-accent transition-colors group",title:"Select AI Provider & Model",children:[_.jsx("span",{className:"max-w-[300px] truncate",children:s}),_.jsx(Tg,{size:14,className:"text-adobe-text-secondary group-hover:text-adobe-accent transition-colors"})]})]}),_.jsxs("div",{className:"flex items-center space-x-2",children:[_.jsx("button",{onClick:()=>r("chat-history"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Chat History",children:_.jsx(Ag,{size:16})}),_.jsx("button",{onClick:()=>r("settings"),className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"Settings",children:_.jsx(Vg,{size:16})}),_.jsx("div",{className:"h-4 w-px bg-adobe-border"}),_.jsx("button",{onClick:o,className:"p-2 text-adobe-text-secondary hover:text-adobe-text-primary hover:bg-adobe-bg-tertiary rounded-md transition-all",title:"New Chat",children:_.jsx(Dg,{size:16})})]})]})},Df=[{id:"abap",name:"ABAP",import:()=>f(()=>import("./assets/abap-DsBKuouk.js"),[],import.meta.url)},{id:"actionscript-3",name:"ActionScript",import:()=>f(()=>import("./assets/actionscript-3-D_z4Izcz.js"),[],import.meta.url)},{id:"ada",name:"Ada",import:()=>f(()=>import("./assets/ada-727ZlQH0.js"),[],import.meta.url)},{id:"angular-html",name:"Angular HTML",import:()=>f(()=>import("./assets/angular-html-LfdN0zeE.js").then(e=>e.f),__vite__mapDeps([0,1,2,3]),import.meta.url)},{id:"angular-ts",name:"Angular TypeScript",import:()=>f(()=>import("./assets/angular-ts-CKsD7JZE.js"),__vite__mapDeps([4,0,1,2,3,5]),import.meta.url)},{id:"apache",name:"Apache Conf",import:()=>f(()=>import("./assets/apache-Dn00JSTd.js"),[],import.meta.url)},{id:"apex",name:"Apex",import:()=>f(()=>import("./assets/apex-COJ4H7py.js"),[],import.meta.url)},{id:"apl",name:"APL",import:()=>f(()=>import("./assets/apl-BBq3IX1j.js"),__vite__mapDeps([6,1,2,3,7,8,9]),import.meta.url)},{id:"applescript",name:"AppleScript",import:()=>f(()=>import("./assets/applescript-Bu5BbsvL.js"),[],import.meta.url)},{id:"ara",name:"Ara",import:()=>f(()=>import("./assets/ara-7O62HKoU.js"),[],import.meta.url)},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>f(()=>import("./assets/asciidoc-BPT9niGB.js"),[],import.meta.url)},{id:"asm",name:"Assembly",import:()=>f(()=>import("./assets/asm-Dhn9LcZ4.js"),[],import.meta.url)},{id:"astro",name:"Astro",import:()=>f(()=>import("./assets/astro-CqkE3fuf.js"),__vite__mapDeps([10,9,2,11,3,12]),import.meta.url)},{id:"awk",name:"AWK",import:()=>f(()=>import("./assets/awk-eg146-Ew.js"),[],import.meta.url)},{id:"ballerina",name:"Ballerina",import:()=>f(()=>import("./assets/ballerina-Du268qiB.js"),[],import.meta.url)},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>f(()=>import("./assets/bat-fje9CFhw.js"),[],import.meta.url)},{id:"beancount",name:"Beancount",import:()=>f(()=>import("./assets/beancount-BwXTMy5W.js"),[],import.meta.url)},{id:"berry",name:"Berry",aliases:["be"],import:()=>f(()=>import("./assets/berry-3xVqZejG.js"),[],import.meta.url)},{id:"bibtex",name:"BibTeX",import:()=>f(()=>import("./assets/bibtex-xW4inM5L.js"),[],import.meta.url)},{id:"bicep",name:"Bicep",import:()=>f(()=>import("./assets/bicep-DHo0CJ0O.js"),[],import.meta.url)},{id:"blade",name:"Blade",import:()=>f(()=>import("./assets/blade-a8OxSdnT.js"),__vite__mapDeps([13,1,2,3,7,8,14,9]),import.meta.url)},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>f(()=>import("./assets/bsl-Dgyn0ogV.js"),__vite__mapDeps([15,16]),import.meta.url)},{id:"c",name:"C",import:()=>f(()=>import("./assets/c-C3t2pwGQ.js"),[],import.meta.url)},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>f(()=>import("./assets/cadence-DNquZEk8.js"),[],import.meta.url)},{id:"cairo",name:"Cairo",import:()=>f(()=>import("./assets/cairo--RitsXJZ.js"),__vite__mapDeps([17,18]),import.meta.url)},{id:"clarity",name:"Clarity",import:()=>f(()=>import("./assets/clarity-BHOwM8T6.js"),[],import.meta.url)},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>f(()=>import("./assets/clojure-DxSadP1t.js"),[],import.meta.url)},{id:"cmake",name:"CMake",import:()=>f(()=>import("./assets/cmake-DbXoA79R.js"),[],import.meta.url)},{id:"cobol",name:"COBOL",import:()=>f(()=>import("./assets/cobol-PTqiYgYu.js"),__vite__mapDeps([19,1,2,3,8]),import.meta.url)},{id:"codeowners",name:"CODEOWNERS",import:()=>f(()=>import("./assets/codeowners-Bp6g37R7.js"),[],import.meta.url)},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>f(()=>import("./assets/codeql-sacFqUAJ.js"),[],import.meta.url)},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>f(()=>import("./assets/coffee-dyiR41kL.js"),__vite__mapDeps([20,2]),import.meta.url)},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>f(()=>import("./assets/common-lisp-C7gG9l05.js"),[],import.meta.url)},{id:"coq",name:"Coq",import:()=>f(()=>import("./assets/coq-Dsg_Bt_b.js"),[],import.meta.url)},{id:"cpp",name:"C++",aliases:["c++"],import:()=>f(()=>import("./assets/cpp-BksuvNSY.js"),__vite__mapDeps([21,22,23,24,14]),import.meta.url)},{id:"crystal",name:"Crystal",import:()=>f(()=>import("./assets/crystal-DtDmRg-F.js"),__vite__mapDeps([25,1,2,3,14,24,26]),import.meta.url)},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>f(()=>import("./assets/csharp-D9R-vmeu.js"),[],import.meta.url)},{id:"css",name:"CSS",import:()=>f(()=>import("./assets/css-BPhBrDlE.js"),[],import.meta.url)},{id:"csv",name:"CSV",import:()=>f(()=>import("./assets/csv-B0qRVHPH.js"),[],import.meta.url)},{id:"cue",name:"CUE",import:()=>f(()=>import("./assets/cue-DtFQj3wx.js"),[],import.meta.url)},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>f(()=>import("./assets/cypher-m2LEI-9-.js"),[],import.meta.url)},{id:"d",name:"D",import:()=>f(()=>import("./assets/d-BoXegm-a.js"),[],import.meta.url)},{id:"dart",name:"Dart",import:()=>f(()=>import("./assets/dart-B9wLZaAG.js"),[],import.meta.url)},{id:"dax",name:"DAX",import:()=>f(()=>import("./assets/dax-ClGRhx96.js"),[],import.meta.url)},{id:"desktop",name:"Desktop",import:()=>f(()=>import("./assets/desktop-DEIpsLCJ.js"),[],import.meta.url)},{id:"diff",name:"Diff",import:()=>f(()=>import("./assets/diff-BgYniUM_.js"),[],import.meta.url)},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>f(()=>import("./assets/docker-COcR7UxN.js"),[],import.meta.url)},{id:"dotenv",name:"dotEnv",import:()=>f(()=>import("./assets/dotenv-BjQB5zDj.js"),[],import.meta.url)},{id:"dream-maker",name:"Dream Maker",import:()=>f(()=>import("./assets/dream-maker-C-nORZOA.js"),[],import.meta.url)},{id:"edge",name:"Edge",import:()=>f(()=>import("./assets/edge-D5gP-w-T.js"),__vite__mapDeps([27,11,1,2,3,28]),import.meta.url)},{id:"elixir",name:"Elixir",import:()=>f(()=>import("./assets/elixir-CLiX3zqd.js"),__vite__mapDeps([29,1,2,3]),import.meta.url)},{id:"elm",name:"Elm",import:()=>f(()=>import("./assets/elm-CmHSxxaM.js"),__vite__mapDeps([30,23,24]),import.meta.url)},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>f(()=>import("./assets/emacs-lisp-BX77sIaO.js"),[],import.meta.url)},{id:"erb",name:"ERB",import:()=>f(()=>import("./assets/erb-BYTLMnw6.js"),__vite__mapDeps([31,1,2,3,32,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>f(()=>import("./assets/erlang-B-DoSBHF.js"),[],import.meta.url)},{id:"fennel",name:"Fennel",import:()=>f(()=>import("./assets/fennel-bCA53EVm.js"),[],import.meta.url)},{id:"fish",name:"Fish",import:()=>f(()=>import("./assets/fish-w-ucz2PV.js"),[],import.meta.url)},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>f(()=>import("./assets/fluent-Dayu4EKP.js"),[],import.meta.url)},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>f(()=>import("./assets/fortran-fixed-form-TqA4NnZg.js"),__vite__mapDeps([39,40]),import.meta.url)},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>f(()=>import("./assets/fortran-free-form-DKXYxT9g.js"),[],import.meta.url)},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>f(()=>import("./assets/fsharp-XplgxFYe.js"),__vite__mapDeps([41,42]),import.meta.url)},{id:"gdresource",name:"GDResource",import:()=>f(()=>import("./assets/gdresource-BHYsBjWJ.js"),__vite__mapDeps([43,44,45]),import.meta.url)},{id:"gdscript",name:"GDScript",import:()=>f(()=>import("./assets/gdscript-DfxzS6Rs.js"),[],import.meta.url)},{id:"gdshader",name:"GDShader",import:()=>f(()=>import("./assets/gdshader-SKMF96pI.js"),[],import.meta.url)},{id:"genie",name:"Genie",import:()=>f(()=>import("./assets/genie-ajMbGru0.js"),[],import.meta.url)},{id:"gherkin",name:"Gherkin",import:()=>f(()=>import("./assets/gherkin--30QC5Em.js"),[],import.meta.url)},{id:"git-commit",name:"Git Commit Message",import:()=>f(()=>import("./assets/git-commit-i4q6IMui.js"),__vite__mapDeps([46,47]),import.meta.url)},{id:"git-rebase",name:"Git Rebase Message",import:()=>f(()=>import("./assets/git-rebase-B-v9cOL2.js"),__vite__mapDeps([48,26]),import.meta.url)},{id:"gleam",name:"Gleam",import:()=>f(()=>import("./assets/gleam-B430Bg39.js"),[],import.meta.url)},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>f(()=>import("./assets/glimmer-js-D-cwc0-E.js"),__vite__mapDeps([49,2,11,3,1]),import.meta.url)},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>f(()=>import("./assets/glimmer-ts-pgjy16dm.js"),__vite__mapDeps([50,11,3,2,1]),import.meta.url)},{id:"glsl",name:"GLSL",import:()=>f(()=>import("./assets/glsl-DBO2IWDn.js"),__vite__mapDeps([23,24]),import.meta.url)},{id:"gnuplot",name:"Gnuplot",import:()=>f(()=>import("./assets/gnuplot-CM8KxXT1.js"),[],import.meta.url)},{id:"go",name:"Go",import:()=>f(()=>import("./assets/go-B1SYOhNW.js"),[],import.meta.url)},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>f(()=>import("./assets/graphql-cDcHW_If.js"),__vite__mapDeps([34,2,11,35,36]),import.meta.url)},{id:"groovy",name:"Groovy",import:()=>f(()=>import("./assets/groovy-DkBy-JyN.js"),[],import.meta.url)},{id:"hack",name:"Hack",import:()=>f(()=>import("./assets/hack-D1yCygmZ.js"),__vite__mapDeps([51,1,2,3,14]),import.meta.url)},{id:"haml",name:"Ruby Haml",import:()=>f(()=>import("./assets/haml-B2EZWmdv.js"),__vite__mapDeps([33,2,3]),import.meta.url)},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>f(()=>import("./assets/handlebars-BQGss363.js"),__vite__mapDeps([52,1,2,3,38]),import.meta.url)},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>f(()=>import("./assets/haskell-BILxekzW.js"),[],import.meta.url)},{id:"haxe",name:"Haxe",import:()=>f(()=>import("./assets/haxe-C5wWYbrZ.js"),[],import.meta.url)},{id:"hcl",name:"HashiCorp HCL",import:()=>f(()=>import("./assets/hcl-HzYwdGDm.js"),[],import.meta.url)},{id:"hjson",name:"Hjson",import:()=>f(()=>import("./assets/hjson-T-Tgc4AT.js"),[],import.meta.url)},{id:"hlsl",name:"HLSL",import:()=>f(()=>import("./assets/hlsl-ifBTmRxC.js"),[],import.meta.url)},{id:"html",name:"HTML",import:()=>f(()=>import("./assets/html-C2L_23MC.js"),__vite__mapDeps([1,2,3]),import.meta.url)},{id:"html-derivative",name:"HTML (Derivative)",import:()=>f(()=>import("./assets/html-derivative-CSfWNPLT.js"),__vite__mapDeps([28,1,2,3]),import.meta.url)},{id:"http",name:"HTTP",import:()=>f(()=>import("./assets/http-FRrOvY1W.js"),__vite__mapDeps([53,26,9,7,8,34,2,11,35,36]),import.meta.url)},{id:"hxml",name:"HXML",import:()=>f(()=>import("./assets/hxml-TIA70rKU.js"),__vite__mapDeps([54,55]),import.meta.url)},{id:"hy",name:"Hy",import:()=>f(()=>import("./assets/hy-BMj5Y0dO.js"),[],import.meta.url)},{id:"imba",name:"Imba",import:()=>f(()=>import("./assets/imba-bv_oIlVt.js"),__vite__mapDeps([56,11]),import.meta.url)},{id:"ini",name:"INI",aliases:["properties"],import:()=>f(()=>import("./assets/ini-BjABl1g7.js"),[],import.meta.url)},{id:"java",name:"Java",import:()=>f(()=>import("./assets/java-xI-RfyKK.js"),[],import.meta.url)},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>f(()=>import("./assets/javascript-ySlJ1b_l.js"),[],import.meta.url)},{id:"jinja",name:"Jinja",import:()=>f(()=>import("./assets/jinja-DGy0s7-h.js"),__vite__mapDeps([57,1,2,3]),import.meta.url)},{id:"jison",name:"Jison",import:()=>f(()=>import("./assets/jison-BqZprYcd.js"),__vite__mapDeps([58,2]),import.meta.url)},{id:"json",name:"JSON",import:()=>f(()=>import("./assets/json-BQoSv7ci.js"),[],import.meta.url)},{id:"json5",name:"JSON5",import:()=>f(()=>import("./assets/json5-w8dY5SsB.js"),[],import.meta.url)},{id:"jsonc",name:"JSON with Comments",import:()=>f(()=>import("./assets/jsonc-TU54ms6u.js"),[],import.meta.url)},{id:"jsonl",name:"JSON Lines",import:()=>f(()=>import("./assets/jsonl-DREVFZK8.js"),[],import.meta.url)},{id:"jsonnet",name:"Jsonnet",import:()=>f(()=>import("./assets/jsonnet-BfivnA6A.js"),[],import.meta.url)},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>f(()=>import("./assets/jssm-P4WzXJd0.js"),[],import.meta.url)},{id:"jsx",name:"JSX",import:()=>f(()=>import("./assets/jsx-BAng5TT0.js"),[],import.meta.url)},{id:"julia",name:"Julia",aliases:["jl"],import:()=>f(()=>import("./assets/julia-BBuGR-5E.js"),__vite__mapDeps([59,21,22,23,24,14,18,2,60]),import.meta.url)},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>f(()=>import("./assets/kotlin-B5lbUyaz.js"),[],import.meta.url)},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>f(()=>import("./assets/kusto-mebxcVVE.js"),[],import.meta.url)},{id:"latex",name:"LaTeX",import:()=>f(()=>import("./assets/latex-C-cWTeAZ.js"),__vite__mapDeps([61,62,60]),import.meta.url)},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>f(()=>import("./assets/lean-XBlWyCtg.js"),[],import.meta.url)},{id:"less",name:"Less",import:()=>f(()=>import("./assets/less-BfCpw3nA.js"),[],import.meta.url)},{id:"liquid",name:"Liquid",import:()=>f(()=>import("./assets/liquid-D3W5UaiH.js"),__vite__mapDeps([63,1,2,3,9]),import.meta.url)},{id:"log",name:"Log file",import:()=>f(()=>import("./assets/log-Cc5clBb7.js"),[],import.meta.url)},{id:"logo",name:"Logo",import:()=>f(()=>import("./assets/logo-IuBKFhSY.js"),[],import.meta.url)},{id:"lua",name:"Lua",import:()=>f(()=>import("./assets/lua-CvWAzNxB.js"),__vite__mapDeps([37,24]),import.meta.url)},{id:"luau",name:"Luau",import:()=>f(()=>import("./assets/luau-Du5NY7AG.js"),[],import.meta.url)},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>f(()=>import("./assets/make-Bvotw-X0.js"),[],import.meta.url)},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>f(()=>import("./assets/markdown-UIAJJxZW.js"),[],import.meta.url)},{id:"marko",name:"Marko",import:()=>f(()=>import("./assets/marko-z0MBrx5-.js"),__vite__mapDeps([64,3,65,5,2]),import.meta.url)},{id:"matlab",name:"MATLAB",import:()=>f(()=>import("./assets/matlab-D9-PGadD.js"),[],import.meta.url)},{id:"mdc",name:"MDC",import:()=>f(()=>import("./assets/mdc-DB_EDNY_.js"),__vite__mapDeps([66,42,38,28,1,2,3]),import.meta.url)},{id:"mdx",name:"MDX",import:()=>f(()=>import("./assets/mdx-sdHcTMYB.js"),[],import.meta.url)},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>f(()=>import("./assets/mermaid-Ci6OQyBP.js"),[],import.meta.url)},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>f(()=>import("./assets/mipsasm-BC5c_5Pe.js"),[],import.meta.url)},{id:"mojo",name:"Mojo",import:()=>f(()=>import("./assets/mojo-Tz6hzZYG.js"),[],import.meta.url)},{id:"move",name:"Move",import:()=>f(()=>import("./assets/move-DB_GagMm.js"),[],import.meta.url)},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>f(()=>import("./assets/narrat-DLbgOhZU.js"),[],import.meta.url)},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>f(()=>import("./assets/nextflow-B0XVJmRM.js"),[],import.meta.url)},{id:"nginx",name:"Nginx",import:()=>f(()=>import("./assets/nginx-D_VnBJ67.js"),__vite__mapDeps([67,37,24]),import.meta.url)},{id:"nim",name:"Nim",import:()=>f(()=>import("./assets/nim-ZlGxZxc3.js"),__vite__mapDeps([68,24,1,2,3,7,8,23,42]),import.meta.url)},{id:"nix",name:"Nix",import:()=>f(()=>import("./assets/nix-shcSOmrb.js"),[],import.meta.url)},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>f(()=>import("./assets/nushell-D4Tzg5kh.js"),[],import.meta.url)},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>f(()=>import("./assets/objective-c-Deuh7S70.js"),[],import.meta.url)},{id:"objective-cpp",name:"Objective-C++",import:()=>f(()=>import("./assets/objective-cpp-BUEGK8hf.js"),[],import.meta.url)},{id:"ocaml",name:"OCaml",import:()=>f(()=>import("./assets/ocaml-BNioltXt.js"),[],import.meta.url)},{id:"pascal",name:"Pascal",import:()=>f(()=>import("./assets/pascal-JqZropPD.js"),[],import.meta.url)},{id:"perl",name:"Perl",import:()=>f(()=>import("./assets/perl-CHQXSrWU.js"),__vite__mapDeps([69,1,2,3,7,8,14]),import.meta.url)},{id:"php",name:"PHP",import:()=>f(()=>import("./assets/php-B5ebYQev.js"),__vite__mapDeps([70,1,2,3,7,8,14,9]),import.meta.url)},{id:"plsql",name:"PL/SQL",import:()=>f(()=>import("./assets/plsql-LKU2TuZ1.js"),[],import.meta.url)},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>f(()=>import("./assets/po-BFLt1xDp.js"),[],import.meta.url)},{id:"polar",name:"Polar",import:()=>f(()=>import("./assets/polar-DKykz6zU.js"),[],import.meta.url)},{id:"postcss",name:"PostCSS",import:()=>f(()=>import("./assets/postcss-B3ZDOciz.js"),[],import.meta.url)},{id:"powerquery",name:"PowerQuery",import:()=>f(()=>import("./assets/powerquery-CSHBycmS.js"),[],import.meta.url)},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>f(()=>import("./assets/powershell-BIEUsx6d.js"),[],import.meta.url)},{id:"prisma",name:"Prisma",import:()=>f(()=>import("./assets/prisma-B48N-Iqd.js"),[],import.meta.url)},{id:"prolog",name:"Prolog",import:()=>f(()=>import("./assets/prolog-BY-TUvya.js"),[],import.meta.url)},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>f(()=>import("./assets/proto-zocC4JxJ.js"),[],import.meta.url)},{id:"pug",name:"Pug",aliases:["jade"],import:()=>f(()=>import("./assets/pug-CM9l7STV.js"),__vite__mapDeps([71,2,3,1]),import.meta.url)},{id:"puppet",name:"Puppet",import:()=>f(()=>import("./assets/puppet-Cza_XSSt.js"),[],import.meta.url)},{id:"purescript",name:"PureScript",import:()=>f(()=>import("./assets/purescript-Bg-kzb6g.js"),[],import.meta.url)},{id:"python",name:"Python",aliases:["py"],import:()=>f(()=>import("./assets/python-DhUJRlN_.js"),[],import.meta.url)},{id:"qml",name:"QML",import:()=>f(()=>import("./assets/qml-D8XfuvdV.js"),__vite__mapDeps([72,2]),import.meta.url)},{id:"qmldir",name:"QML Directory",import:()=>f(()=>import("./assets/qmldir-C8lEn-DE.js"),[],import.meta.url)},{id:"qss",name:"Qt Style Sheets",import:()=>f(()=>import("./assets/qss-DhMKtDLN.js"),[],import.meta.url)},{id:"r",name:"R",import:()=>f(()=>import("./assets/r-CwjWoCRV.js"),[],import.meta.url)},{id:"racket",name:"Racket",import:()=>f(()=>import("./assets/racket-CzouJOBO.js"),[],import.meta.url)},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>f(()=>import("./assets/raku-B1bQXN8T.js"),[],import.meta.url)},{id:"razor",name:"ASP.NET Razor",import:()=>f(()=>import("./assets/razor-CNLDkMZG.js"),__vite__mapDeps([73,1,2,3,74]),import.meta.url)},{id:"reg",name:"Windows Registry Script",import:()=>f(()=>import("./assets/reg-5LuOXUq_.js"),[],import.meta.url)},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>f(()=>import("./assets/regexp-DWJ3fJO_.js"),[],import.meta.url)},{id:"rel",name:"Rel",import:()=>f(()=>import("./assets/rel-DJlmqQ1C.js"),[],import.meta.url)},{id:"riscv",name:"RISC-V",import:()=>f(()=>import("./assets/riscv-QhoSD0DR.js"),[],import.meta.url)},{id:"rst",name:"reStructuredText",import:()=>f(()=>import("./assets/rst-4NLicBqY.js"),__vite__mapDeps([75,28,1,2,3,21,22,23,24,14,18,26,38,76,32,33,7,8,34,11,35,36,37]),import.meta.url)},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>f(()=>import("./assets/ruby-DeZ3UC14.js"),__vite__mapDeps([32,1,2,3,33,7,8,14,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"rust",name:"Rust",aliases:["rs"],import:()=>f(()=>import("./assets/rust-Be6lgOlo.js"),[],import.meta.url)},{id:"sas",name:"SAS",import:()=>f(()=>import("./assets/sas-BmTFh92c.js"),__vite__mapDeps([77,14]),import.meta.url)},{id:"sass",name:"Sass",import:()=>f(()=>import("./assets/sass-BJ4Li9vH.js"),[],import.meta.url)},{id:"scala",name:"Scala",import:()=>f(()=>import("./assets/scala-DQVVAn-B.js"),[],import.meta.url)},{id:"scheme",name:"Scheme",import:()=>f(()=>import("./assets/scheme-BJGe-b2p.js"),[],import.meta.url)},{id:"scss",name:"SCSS",import:()=>f(()=>import("./assets/scss-C31hgJw-.js"),__vite__mapDeps([5,3]),import.meta.url)},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>f(()=>import("./assets/sdbl-BLhTXw86.js"),[],import.meta.url)},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>f(()=>import("./assets/shaderlab-B7qAK45m.js"),__vite__mapDeps([78,79]),import.meta.url)},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>f(()=>import("./assets/shellscript-atvbtKCR.js"),[],import.meta.url)},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>f(()=>import("./assets/shellsession-C_rIy8kc.js"),__vite__mapDeps([80,26]),import.meta.url)},{id:"smalltalk",name:"Smalltalk",import:()=>f(()=>import("./assets/smalltalk-DkLiglaE.js"),[],import.meta.url)},{id:"solidity",name:"Solidity",import:()=>f(()=>import("./assets/solidity-C1w2a3ep.js"),[],import.meta.url)},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>f(()=>import("./assets/soy-C-lX7w71.js"),__vite__mapDeps([81,1,2,3]),import.meta.url)},{id:"sparql",name:"SPARQL",import:()=>f(()=>import("./assets/sparql-bYkjHRlG.js"),__vite__mapDeps([82,83]),import.meta.url)},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>f(()=>import("./assets/splunk-Cf8iN4DR.js"),[],import.meta.url)},{id:"sql",name:"SQL",import:()=>f(()=>import("./assets/sql-COK4E0Yg.js"),[],import.meta.url)},{id:"ssh-config",name:"SSH Config",import:()=>f(()=>import("./assets/ssh-config-BknIz3MU.js"),[],import.meta.url)},{id:"stata",name:"Stata",import:()=>f(()=>import("./assets/stata-DorPZHa4.js"),__vite__mapDeps([84,14]),import.meta.url)},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>f(()=>import("./assets/stylus-BeQkCIfX.js"),[],import.meta.url)},{id:"svelte",name:"Svelte",import:()=>f(()=>import("./assets/svelte-MSaWC3Je.js"),__vite__mapDeps([85,2,11,3,12]),import.meta.url)},{id:"swift",name:"Swift",import:()=>f(()=>import("./assets/swift-BSxZ-RaX.js"),[],import.meta.url)},{id:"system-verilog",name:"SystemVerilog",import:()=>f(()=>import("./assets/system-verilog-C7L56vO4.js"),[],import.meta.url)},{id:"systemd",name:"Systemd Units",import:()=>f(()=>import("./assets/systemd-CUnW07Te.js"),[],import.meta.url)},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>f(()=>import("./assets/talonscript-C1XDQQGZ.js"),[],import.meta.url)},{id:"tasl",name:"Tasl",import:()=>f(()=>import("./assets/tasl-CQjiPCtT.js"),[],import.meta.url)},{id:"tcl",name:"Tcl",import:()=>f(()=>import("./assets/tcl-DQ1-QYvQ.js"),[],import.meta.url)},{id:"templ",name:"Templ",import:()=>f(()=>import("./assets/templ-dwX3ZSMB.js"),__vite__mapDeps([86,87,2,3]),import.meta.url)},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>f(()=>import("./assets/terraform-BbSNqyBO.js"),[],import.meta.url)},{id:"tex",name:"TeX",import:()=>f(()=>import("./assets/tex-rYs2v40G.js"),__vite__mapDeps([62,60]),import.meta.url)},{id:"toml",name:"TOML",import:()=>f(()=>import("./assets/toml-CB2ApiWb.js"),[],import.meta.url)},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>f(()=>import("./assets/ts-tags-CipyTH0X.js"),__vite__mapDeps([88,11,3,2,23,24,1,14,7,8]),import.meta.url)},{id:"tsv",name:"TSV",import:()=>f(()=>import("./assets/tsv-B_m7g4N7.js"),[],import.meta.url)},{id:"tsx",name:"TSX",import:()=>f(()=>import("./assets/tsx-B6W0miNI.js"),[],import.meta.url)},{id:"turtle",name:"Turtle",import:()=>f(()=>import("./assets/turtle-BMR_PYu6.js"),[],import.meta.url)},{id:"twig",name:"Twig",import:()=>f(()=>import("./assets/twig-NC5TFiHP.js"),__vite__mapDeps([89,3,2,5,70,1,7,8,14,9,18,32,33,34,11,35,36,21,22,23,24,26,37,38]),import.meta.url)},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>f(()=>import("./assets/typescript-Dj6nwHGl.js"),[],import.meta.url)},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>f(()=>import("./assets/typespec-BpWG_bgh.js"),[],import.meta.url)},{id:"typst",name:"Typst",aliases:["typ"],import:()=>f(()=>import("./assets/typst-BVUVsWT6.js"),[],import.meta.url)},{id:"v",name:"V",import:()=>f(()=>import("./assets/v-CAQ2eGtk.js"),[],import.meta.url)},{id:"vala",name:"Vala",import:()=>f(()=>import("./assets/vala-BFOHcciG.js"),[],import.meta.url)},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>f(()=>import("./assets/vb-CdO5JTpU.js"),[],import.meta.url)},{id:"verilog",name:"Verilog",import:()=>f(()=>import("./assets/verilog-CJaU5se_.js"),[],import.meta.url)},{id:"vhdl",name:"VHDL",import:()=>f(()=>import("./assets/vhdl-DYoNaHQp.js"),[],import.meta.url)},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>f(()=>import("./assets/viml-m4uW47V2.js"),[],import.meta.url)},{id:"vue",name:"Vue",import:()=>f(()=>import("./assets/vue-BuYVFjOK.js"),__vite__mapDeps([90,1,2,3,11,9,28]),import.meta.url)},{id:"vue-html",name:"Vue HTML",import:()=>f(()=>import("./assets/vue-html-xdeiXROB.js"),__vite__mapDeps([91,90,1,2,3,11,9,28]),import.meta.url)},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>f(()=>import("./assets/vyper-nyqBNV6O.js"),[],import.meta.url)},{id:"wasm",name:"WebAssembly",import:()=>f(()=>import("./assets/wasm-C6j12Q_x.js"),[],import.meta.url)},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>f(()=>import("./assets/wenyan-7A4Fjokl.js"),[],import.meta.url)},{id:"wgsl",name:"WGSL",import:()=>f(()=>import("./assets/wgsl-CB0Krxn9.js"),[],import.meta.url)},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>f(()=>import("./assets/wikitext-DCE3LsBG.js"),[],import.meta.url)},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>f(()=>import("./assets/wolfram-C3FkfJm5.js"),[],import.meta.url)},{id:"xml",name:"XML",import:()=>f(()=>import("./assets/xml-e3z08dGr.js"),__vite__mapDeps([7,8]),import.meta.url)},{id:"xsl",name:"XSL",import:()=>f(()=>import("./assets/xsl-Dd0NUgwM.js"),__vite__mapDeps([92,7,8]),import.meta.url)},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>f(()=>import("./assets/yaml-CVw76BM1.js"),[],import.meta.url)},{id:"zenscript",name:"ZenScript",import:()=>f(()=>import("./assets/zenscript-HnGAYVZD.js"),[],import.meta.url)},{id:"zig",name:"Zig",import:()=>f(()=>import("./assets/zig-BVz_zdnA.js"),[],import.meta.url)}],Gg=Object.fromEntries(Df.map(e=>[e.id,e.import])),Hg=Object.fromEntries(Df.flatMap(e=>{var t;return((t=e.aliases)==null?void 0:t.map(n=>[n,e.import]))||[]})),Wg={...Gg,...Hg},Qg=[{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>f(()=>import("./assets/andromeeda-C3khCPGq.js"),[],import.meta.url)},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>f(()=>import("./assets/aurora-x-D-2ljcwZ.js"),[],import.meta.url)},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>f(()=>import("./assets/ayu-dark-Cv9koXgw.js"),[],import.meta.url)},{id:"catppuccin-frappe",displayName:"Catppuccin Frappé",type:"dark",import:()=>f(()=>import("./assets/catppuccin-frappe-CD_QflpE.js"),[],import.meta.url)},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>f(()=>import("./assets/catppuccin-latte-DRW-0cLl.js"),[],import.meta.url)},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>f(()=>import("./assets/catppuccin-macchiato-C-_shW-Y.js"),[],import.meta.url)},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>f(()=>import("./assets/catppuccin-mocha-LGGdnPYs.js"),[],import.meta.url)},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>f(()=>import("./assets/dark-plus-C3mMm8J8.js"),[],import.meta.url)},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>f(()=>import("./assets/dracula-BzJJZx-M.js"),[],import.meta.url)},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>f(()=>import("./assets/dracula-soft-BXkSAIEj.js"),[],import.meta.url)},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>f(()=>import("./assets/everforest-dark-BgDCqdQA.js"),[],import.meta.url)},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>f(()=>import("./assets/everforest-light-C8M2exoo.js"),[],import.meta.url)},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>f(()=>import("./assets/github-dark-DHJKELXO.js"),[],import.meta.url)},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>f(()=>import("./assets/github-dark-default-Cuk6v7N8.js"),[],import.meta.url)},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>f(()=>import("./assets/github-dark-dimmed-DH5Ifo-i.js"),[],import.meta.url)},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>f(()=>import("./assets/github-dark-high-contrast-E3gJ1_iC.js"),[],import.meta.url)},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>f(()=>import("./assets/github-light-DAi9KRSo.js"),[],import.meta.url)},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>f(()=>import("./assets/github-light-default-D7oLnXFd.js"),[],import.meta.url)},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>f(()=>import("./assets/github-light-high-contrast-BfjtVDDH.js"),[],import.meta.url)},{id:"houston",displayName:"Houston",type:"dark",import:()=>f(()=>import("./assets/houston-DnULxvSX.js"),[],import.meta.url)},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>f(()=>import("./assets/kanagawa-dragon-CkXjmgJE.js"),[],import.meta.url)},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>f(()=>import("./assets/kanagawa-lotus-CfQXZHmo.js"),[],import.meta.url)},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>f(()=>import("./assets/kanagawa-wave-DWedfzmr.js"),[],import.meta.url)},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>f(()=>import("./assets/laserwave-DUszq2jm.js"),[],import.meta.url)},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>f(()=>import("./assets/light-plus-B7mTdjB0.js"),[],import.meta.url)},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>f(()=>import("./assets/material-theme-D5KoaKCx.js"),[],import.meta.url)},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>f(()=>import("./assets/material-theme-darker-BfHTSMKl.js"),[],import.meta.url)},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>f(()=>import("./assets/material-theme-lighter-B0m2ddpp.js"),[],import.meta.url)},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>f(()=>import("./assets/material-theme-ocean-CyktbL80.js"),[],import.meta.url)},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>f(()=>import("./assets/material-theme-palenight-Csfq5Kiy.js"),[],import.meta.url)},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>f(()=>import("./assets/min-dark-CafNBF8u.js"),[],import.meta.url)},{id:"min-light",displayName:"Min Light",type:"light",import:()=>f(()=>import("./assets/min-light-CTRr51gU.js"),[],import.meta.url)},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>f(()=>import("./assets/monokai-D4h5O-jR.js"),[],import.meta.url)},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>f(()=>import("./assets/night-owl-C39BiMTA.js"),[],import.meta.url)},{id:"nord",displayName:"Nord",type:"dark",import:()=>f(()=>import("./assets/nord-Ddv68eIx.js"),[],import.meta.url)},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>f(()=>import("./assets/one-dark-pro-GBQ2dnAY.js"),[],import.meta.url)},{id:"one-light",displayName:"One Light",type:"light",import:()=>f(()=>import("./assets/one-light-PoHY5YXO.js"),[],import.meta.url)},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>f(()=>import("./assets/plastic-3e1v2bzS.js"),[],import.meta.url)},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>f(()=>import("./assets/poimandres-CS3Unz2-.js"),[],import.meta.url)},{id:"red",displayName:"Red",type:"dark",import:()=>f(()=>import("./assets/red-bN70gL4F.js"),[],import.meta.url)},{id:"rose-pine",displayName:"Rosé Pine",type:"dark",import:()=>f(()=>import("./assets/rose-pine-CmCqftbK.js"),[],import.meta.url)},{id:"rose-pine-dawn",displayName:"Rosé Pine Dawn",type:"light",import:()=>f(()=>import("./assets/rose-pine-dawn-Ds-gbosJ.js"),[],import.meta.url)},{id:"rose-pine-moon",displayName:"Rosé Pine Moon",type:"dark",import:()=>f(()=>import("./assets/rose-pine-moon-CjDtw9vr.js"),[],import.meta.url)},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>f(()=>import("./assets/slack-dark-BthQWCQV.js"),[],import.meta.url)},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>f(()=>import("./assets/slack-ochin-DqwNpetd.js"),[],import.meta.url)},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>f(()=>import("./assets/snazzy-light-Bw305WKR.js"),[],import.meta.url)},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>f(()=>import("./assets/solarized-dark-DXbdFlpD.js"),[],import.meta.url)},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>f(()=>import("./assets/solarized-light-L9t79GZl.js"),[],import.meta.url)},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>f(()=>import("./assets/synthwave-84-CbfX1IO0.js"),[],import.meta.url)},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>f(()=>import("./assets/tokyo-night-DBQeEorK.js"),[],import.meta.url)},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>f(()=>import("./assets/vesper-BEBZ7ncR.js"),[],import.meta.url)},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>f(()=>import("./assets/vitesse-black-Bkuqu6BP.js"),[],import.meta.url)},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>f(()=>import("./assets/vitesse-dark-D0r3Knsf.js"),[],import.meta.url)},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>f(()=>import("./assets/vitesse-light-CVO1_9PV.js"),[],import.meta.url)}],Kg=Object.fromEntries(Qg.map(e=>[e.id,e.import]));let ht=class extends Error{constructor(t){super(t),this.name="ShikiError"}},xa=class extends Error{constructor(t){super(t),this.name="ShikiError"}};function qg(){return 2147483648}function Yg(){return typeof performance<"u"?performance.now():Date.now()}const Xg=(e,t)=>e+(t-e%t)%t;async function Jg(e){let t,n;const r={};function o(h){n=h,r.HEAPU8=new Uint8Array(h),r.HEAPU32=new Uint32Array(h)}function i(h,v,S){r.HEAPU8.copyWithin(h,v,v+S)}function l(h){try{return t.grow(h-n.byteLength+65535>>>16),o(t.buffer),1}catch{}}function s(h){const v=r.HEAPU8.length;h=h>>>0;const S=qg();if(h>S)return!1;for(let k=1;k<=4;k*=2){let g=v*(1+.2/k);g=Math.min(g,h+100663296);const m=Math.min(S,Xg(Math.max(h,g),65536));if(l(m))return!0}return!1}const a=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function u(h,v,S=1024){const k=v+S;let g=v;for(;h[g]&&!(g>=k);)++g;if(g-v>16&&h.buffer&&a)return a.decode(h.subarray(v,g));let m="";for(;v<g;){let y=h[v++];if(!(y&128)){m+=String.fromCharCode(y);continue}const x=h[v++]&63;if((y&224)===192){m+=String.fromCharCode((y&31)<<6|x);continue}const w=h[v++]&63;if((y&240)===224?y=(y&15)<<12|x<<6|w:y=(y&7)<<18|x<<12|w<<6|h[v++]&63,y<65536)m+=String.fromCharCode(y);else{const P=y-65536;m+=String.fromCharCode(55296|P>>10,56320|P&1023)}}return m}function c(h,v){return h?u(r.HEAPU8,h,v):""}const p={emscripten_get_now:Yg,emscripten_memcpy_big:i,emscripten_resize_heap:s,fd_write:()=>0};async function d(){const v=await e({env:p,wasi_snapshot_preview1:p});t=v.memory,o(t.buffer),Object.assign(r,v),r.UTF8ToString=c}return await d(),r}var Zg=Object.defineProperty,e0=(e,t,n)=>t in e?Zg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,oe=(e,t,n)=>(e0(e,typeof t!="symbol"?t+"":t,n),n);let ae=null;function t0(e){throw new xa(e.UTF8ToString(e.getLastOnigError()))}class Ii{constructor(t){oe(this,"utf16Length"),oe(this,"utf8Length"),oe(this,"utf16Value"),oe(this,"utf8Value"),oe(this,"utf16OffsetToUtf8"),oe(this,"utf8OffsetToUtf16");const n=t.length,r=Ii._utf8ByteLength(t),o=r!==n,i=o?new Uint32Array(n+1):null;o&&(i[n]=r);const l=o?new Uint32Array(r+1):null;o&&(l[r]=n);const s=new Uint8Array(r);let a=0;for(let u=0;u<n;u++){const c=t.charCodeAt(u);let p=c,d=!1;if(c>=55296&&c<=56319&&u+1<n){const h=t.charCodeAt(u+1);h>=56320&&h<=57343&&(p=(c-55296<<10)+65536|h-56320,d=!0)}o&&(i[u]=a,d&&(i[u+1]=a),p<=127?l[a+0]=u:p<=2047?(l[a+0]=u,l[a+1]=u):p<=65535?(l[a+0]=u,l[a+1]=u,l[a+2]=u):(l[a+0]=u,l[a+1]=u,l[a+2]=u,l[a+3]=u)),p<=127?s[a++]=p:p<=2047?(s[a++]=192|(p&1984)>>>6,s[a++]=128|(p&63)>>>0):p<=65535?(s[a++]=224|(p&61440)>>>12,s[a++]=128|(p&4032)>>>6,s[a++]=128|(p&63)>>>0):(s[a++]=240|(p&1835008)>>>18,s[a++]=128|(p&258048)>>>12,s[a++]=128|(p&4032)>>>6,s[a++]=128|(p&63)>>>0),d&&u++}this.utf16Length=n,this.utf8Length=r,this.utf16Value=t,this.utf8Value=s,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=l}static _utf8ByteLength(t){let n=0;for(let r=0,o=t.length;r<o;r++){const i=t.charCodeAt(r);let l=i,s=!1;if(i>=55296&&i<=56319&&r+1<o){const a=t.charCodeAt(r+1);a>=56320&&a<=57343&&(l=(i-55296<<10)+65536|a-56320,s=!0)}l<=127?n+=1:l<=2047?n+=2:l<=65535?n+=3:n+=4,s&&r++}return n}createString(t){const n=t.omalloc(this.utf8Length);return t.HEAPU8.set(this.utf8Value,n),n}}const tt=class{constructor(e){if(oe(this,"id",++tt.LAST_ID),oe(this,"_onigBinding"),oe(this,"content"),oe(this,"utf16Length"),oe(this,"utf8Length"),oe(this,"utf16OffsetToUtf8"),oe(this,"utf8OffsetToUtf16"),oe(this,"ptr"),!ae)throw new xa("Must invoke loadWasm first.");this._onigBinding=ae,this.content=e;const t=new Ii(e);this.utf16Length=t.utf16Length,this.utf8Length=t.utf8Length,this.utf16OffsetToUtf8=t.utf16OffsetToUtf8,this.utf8OffsetToUtf16=t.utf8OffsetToUtf16,this.utf8Length<1e4&&!tt._sharedPtrInUse?(tt._sharedPtr||(tt._sharedPtr=ae.omalloc(1e4)),tt._sharedPtrInUse=!0,ae.HEAPU8.set(t.utf8Value,tt._sharedPtr),this.ptr=tt._sharedPtr):this.ptr=t.createString(ae)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===tt._sharedPtr?tt._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};let Gr=tt;oe(Gr,"LAST_ID",0);oe(Gr,"_sharedPtr",0);oe(Gr,"_sharedPtrInUse",!1);class n0{constructor(t){if(oe(this,"_onigBinding"),oe(this,"_ptr"),!ae)throw new xa("Must invoke loadWasm first.");const n=[],r=[];for(let s=0,a=t.length;s<a;s++){const u=new Ii(t[s]);n[s]=u.createString(ae),r[s]=u.utf8Length}const o=ae.omalloc(4*t.length);ae.HEAPU32.set(n,o/4);const i=ae.omalloc(4*t.length);ae.HEAPU32.set(r,i/4);const l=ae.createOnigScanner(o,i,t.length);for(let s=0,a=t.length;s<a;s++)ae.ofree(n[s]);ae.ofree(i),ae.ofree(o),l===0&&t0(ae),this._onigBinding=ae,this._ptr=l}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(t,n,r){let o=0;if(typeof r=="number"&&(o=r),typeof t=="string"){t=new Gr(t);const i=this._findNextMatchSync(t,n,!1,o);return t.dispose(),i}return this._findNextMatchSync(t,n,!1,o)}_findNextMatchSync(t,n,r,o){const i=this._onigBinding,l=i.findNextOnigScannerMatch(this._ptr,t.id,t.ptr,t.utf8Length,t.convertUtf16OffsetToUtf8(n),o);if(l===0)return null;const s=i.HEAPU32;let a=l/4;const u=s[a++],c=s[a++],p=[];for(let d=0;d<c;d++){const h=t.convertUtf8OffsetToUtf16(s[a++]),v=t.convertUtf8OffsetToUtf16(s[a++]);p[d]={start:h,end:v,length:v-h}}return{index:u,captureIndices:p}}}function r0(e){return typeof e.instantiator=="function"}function o0(e){return typeof e.default=="function"}function i0(e){return typeof e.data<"u"}function l0(e){return typeof Response<"u"&&e instanceof Response}function s0(e){var t;return typeof ArrayBuffer<"u"&&(e instanceof ArrayBuffer||ArrayBuffer.isView(e))||typeof Buffer<"u"&&((t=Buffer.isBuffer)==null?void 0:t.call(Buffer,e))||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer||typeof Uint32Array<"u"&&e instanceof Uint32Array}let po;function a0(e){if(po)return po;async function t(){ae=await Jg(async n=>{let r=e;return r=await r,typeof r=="function"&&(r=await r(n)),typeof r=="function"&&(r=await r(n)),r0(r)?r=await r.instantiator(n):o0(r)?r=await r.default(n):(i0(r)&&(r=r.data),l0(r)?typeof WebAssembly.instantiateStreaming=="function"?r=await u0(r)(n):r=await c0(r)(n):s0(r)?r=await cl(r)(n):r instanceof WebAssembly.Module?r=await cl(r)(n):"default"in r&&r.default instanceof WebAssembly.Module&&(r=await cl(r.default)(n))),"instance"in r&&(r=r.instance),"exports"in r&&(r=r.exports),r})}return po=t(),po}function cl(e){return t=>WebAssembly.instantiate(e,t)}function u0(e){return t=>WebAssembly.instantiateStreaming(e,t)}function c0(e){return async t=>{const n=await e.arrayBuffer();return WebAssembly.instantiate(n,t)}}let d0;function f0(){return d0}async function jf(e){return e&&await a0(e),{createScanner(t){return new n0(t.map(n=>typeof n=="string"?n:n.source))},createString(t){return new Gr(t)}}}function p0(e){return Sa(e)}function Sa(e){return Array.isArray(e)?m0(e):e instanceof RegExp?e:typeof e=="object"?h0(e):e}function m0(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=Sa(e[n]);return t}function h0(e){let t={};for(let n in e)t[n]=Sa(e[n]);return t}function Mf(e,...t){return t.forEach(n=>{for(let r in n)e[r]=n[r]}),e}function Vf(e){const t=~e.lastIndexOf("/")||~e.lastIndexOf("\\");return t===0?e:~t===e.length-1?Vf(e.substring(0,e.length-1)):e.substr(~t+1)}var dl=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,mo=class{static hasCaptures(e){return e===null?!1:(dl.lastIndex=0,dl.test(e))}static replaceCaptures(e,t,n){return e.replace(dl,(r,o,i,l)=>{let s=n[parseInt(o||i,10)];if(s){let a=t.substring(s.start,s.end);for(;a[0]===".";)a=a.substring(1);switch(l){case"downcase":return a.toLowerCase();case"upcase":return a.toUpperCase();default:return a}}else return r})}};function zf(e,t){return e<t?-1:e>t?1:0}function Bf(e,t){if(e===null&&t===null)return 0;if(!e)return-1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let o=0;o<n;o++){let i=zf(e[o],t[o]);if(i!==0)return i}return 0}return n-r}function Uu(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function $f(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var Ff=class{constructor(e){E(this,"cache",new Map);this.fn=e}get(e){if(this.cache.has(e))return this.cache.get(e);const t=this.fn(e);return this.cache.set(e,t),t}},ti=class{constructor(e,t,n){E(this,"_cachedMatchRoot",new Ff(e=>this._root.match(e)));this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(y0(e),t)}static createFromParsedTheme(e,t){return x0(e,t)}getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(e===null)return this._defaults;const t=e.scopeName,r=this._cachedMatchRoot.get(t).find(o=>g0(e.parent,o.parentScopes));return r?new Uf(r.fontStyle,r.foreground,r.background):null}},fl=class To{constructor(t,n){this.parent=t,this.scopeName=n}static push(t,n){for(const r of n)t=new To(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new To(n,t[r]);return n}push(t){return new To(this,t)}getSegments(){let t=this;const n=[];for(;t;)n.push(t.scopeName),t=t.parent;return n.reverse(),n}toString(){return this.getSegments().join(" ")}extends(t){return this===t?!0:this.parent===null?!1:this.parent.extends(t)}getExtensionIfDefined(t){const n=[];let r=this;for(;r&&r!==t;)n.push(r.scopeName),r=r.parent;return r===t?n.reverse():void 0}};function g0(e,t){if(t.length===0)return!0;for(let n=0;n<t.length;n++){let r=t[n],o=!1;if(r===">"){if(n===t.length-1)return!1;r=t[++n],o=!0}for(;e&&!_0(e.scopeName,r);){if(o)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0}function _0(e,t){return t===e||e.startsWith(t)&&e[t.length]==="."}var Uf=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}};function y0(e){if(!e)return[];if(!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let o=0,i=t.length;o<i;o++){let l=t[o];if(!l.settings)continue;let s;if(typeof l.scope=="string"){let p=l.scope;p=p.replace(/^[,]+/,""),p=p.replace(/[,]+$/,""),s=p.split(",")}else Array.isArray(l.scope)?s=l.scope:s=[""];let a=-1;if(typeof l.settings.fontStyle=="string"){a=0;let p=l.settings.fontStyle.split(" ");for(let d=0,h=p.length;d<h;d++)switch(p[d]){case"italic":a=a|1;break;case"bold":a=a|2;break;case"underline":a=a|4;break;case"strikethrough":a=a|8;break}}let u=null;typeof l.settings.foreground=="string"&&Uu(l.settings.foreground)&&(u=l.settings.foreground);let c=null;typeof l.settings.background=="string"&&Uu(l.settings.background)&&(c=l.settings.background);for(let p=0,d=s.length;p<d;p++){let v=s[p].trim().split(" "),S=v[v.length-1],k=null;v.length>1&&(k=v.slice(0,v.length-1),k.reverse()),n[r++]=new v0(S,k,o,a,u,c)}}return n}var v0=class{constructor(e,t,n,r,o,i){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=o,this.background=i}},pt=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(pt||{});function x0(e,t){e.sort((a,u)=>{let c=zf(a.scope,u.scope);return c!==0||(c=Bf(a.parentScopes,u.parentScopes),c!==0)?c:a.index-u.index});let n=0,r="#000000",o="#ffffff";for(;e.length>=1&&e[0].scope==="";){let a=e.shift();a.fontStyle!==-1&&(n=a.fontStyle),a.foreground!==null&&(r=a.foreground),a.background!==null&&(o=a.background)}let i=new S0(t),l=new Uf(n,i.getId(r),i.getId(o)),s=new w0(new ms(0,null,-1,0,0),[]);for(let a=0,u=e.length;a<u;a++){let c=e[a];s.insert(0,c.scope,c.parentScopes,c.fontStyle,i.getId(c.foreground),i.getId(c.background))}return new ti(i,l,s)}var S0=class{constructor(e){E(this,"_isFrozen");E(this,"_lastColorId");E(this,"_id2color");E(this,"_color2id");if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(e===null)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw new Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},E0=Object.freeze([]),ms=class Gf{constructor(t,n,r,o,i){E(this,"scopeDepth");E(this,"parentScopes");E(this,"fontStyle");E(this,"foreground");E(this,"background");this.scopeDepth=t,this.parentScopes=n||E0,this.fontStyle=r,this.foreground=o,this.background=i}clone(){return new Gf(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(t){let n=[];for(let r=0,o=t.length;r<o;r++)n[r]=t[r].clone();return n}acceptOverwrite(t,n,r,o){this.scopeDepth>t?console.log("how did this happen?"):this.scopeDepth=t,n!==-1&&(this.fontStyle=n),r!==0&&(this.foreground=r),o!==0&&(this.background=o)}},w0=class hs{constructor(t,n=[],r={}){E(this,"_rulesWithParentScopes");this._mainRule=t,this._children=r,this._rulesWithParentScopes=n}static _cmpBySpecificity(t,n){if(t.scopeDepth!==n.scopeDepth)return n.scopeDepth-t.scopeDepth;let r=0,o=0;for(;t.parentScopes[r]===">"&&r++,n.parentScopes[o]===">"&&o++,!(r>=t.parentScopes.length||o>=n.parentScopes.length);){const i=n.parentScopes[o].length-t.parentScopes[r].length;if(i!==0)return i;r++,o++}return n.parentScopes.length-t.parentScopes.length}match(t){if(t!==""){let r=t.indexOf("."),o,i;if(r===-1?(o=t,i=""):(o=t.substring(0,r),i=t.substring(r+1)),this._children.hasOwnProperty(o))return this._children[o].match(i)}const n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(hs._cmpBySpecificity),n}insert(t,n,r,o,i,l){if(n===""){this._doInsertHere(t,r,o,i,l);return}let s=n.indexOf("."),a,u;s===-1?(a=n,u=""):(a=n.substring(0,s),u=n.substring(s+1));let c;this._children.hasOwnProperty(a)?c=this._children[a]:(c=new hs(this._mainRule.clone(),ms.cloneArr(this._rulesWithParentScopes)),this._children[a]=c),c.insert(t+1,u,r,o,i,l)}_doInsertHere(t,n,r,o,i){if(n===null){this._mainRule.acceptOverwrite(t,r,o,i);return}for(let l=0,s=this._rulesWithParentScopes.length;l<s;l++){let a=this._rulesWithParentScopes[l];if(Bf(a.parentScopes,n)===0){a.acceptOverwrite(t,r,o,i);return}}r===-1&&(r=this._mainRule.fontStyle),o===0&&(o=this._mainRule.foreground),i===0&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new ms(t,n,r,o,i))}},Bn=class ze{static toBinaryStr(t){return t.toString(2).padStart(32,"0")}static print(t){const n=ze.getLanguageId(t),r=ze.getTokenType(t),o=ze.getFontStyle(t),i=ze.getForeground(t),l=ze.getBackground(t);console.log({languageId:n,tokenType:r,fontStyle:o,foreground:i,background:l})}static getLanguageId(t){return(t&255)>>>0}static getTokenType(t){return(t&768)>>>8}static containsBalancedBrackets(t){return(t&1024)!==0}static getFontStyle(t){return(t&30720)>>>11}static getForeground(t){return(t&16744448)>>>15}static getBackground(t){return(t&4278190080)>>>24}static set(t,n,r,o,i,l,s){let a=ze.getLanguageId(t),u=ze.getTokenType(t),c=ze.containsBalancedBrackets(t)?1:0,p=ze.getFontStyle(t),d=ze.getForeground(t),h=ze.getBackground(t);return n!==0&&(a=n),r!==8&&(u=r),o!==null&&(c=o?1:0),i!==-1&&(p=i),l!==0&&(d=l),s!==0&&(h=s),(a<<0|u<<8|c<<10|p<<11|d<<15|h<<24)>>>0}};function ni(e,t){const n=[],r=k0(e);let o=r.next();for(;o!==null;){let a=0;if(o.length===2&&o.charAt(1)===":"){switch(o.charAt(0)){case"R":a=1;break;case"L":a=-1;break;default:console.log(`Unknown priority ${o} in scope selector`)}o=r.next()}let u=l();if(n.push({matcher:u,priority:a}),o!==",")break;o=r.next()}return n;function i(){if(o==="-"){o=r.next();const a=i();return u=>!!a&&!a(u)}if(o==="("){o=r.next();const a=s();return o===")"&&(o=r.next()),a}if(Gu(o)){const a=[];do a.push(o),o=r.next();while(Gu(o));return u=>t(a,u)}return null}function l(){const a=[];let u=i();for(;u;)a.push(u),u=i();return c=>a.every(p=>p(c))}function s(){const a=[];let u=l();for(;u&&(a.push(u),o==="|"||o===",");){do o=r.next();while(o==="|"||o===",");u=l()}return c=>a.some(p=>p(c))}}function Gu(e){return!!e&&!!e.match(/[\w\.:]+/)}function k0(e){let t=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g,n=t.exec(e);return{next:()=>{if(!n)return null;const r=n[0];return n=t.exec(e),r}}}function Hf(e){typeof e.dispose=="function"&&e.dispose()}var br=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},C0=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},L0=class{constructor(){E(this,"_references",[]);E(this,"_seenReferenceKeys",new Set);E(this,"visitedRule",new Set)}get references(){return this._references}add(e){const t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},P0=class{constructor(e,t){E(this,"seenFullScopeRequests",new Set);E(this,"seenPartialScopeRequests",new Set);E(this,"Q");this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new br(this.initialScopeName)]}processQueue(){const e=this.Q;this.Q=[];const t=new L0;for(const n of e)R0(n,this.initialScopeName,this.repo,t);for(const n of t.references)if(n instanceof br){if(this.seenFullScopeRequests.has(n.scopeName))continue;this.seenFullScopeRequests.add(n.scopeName),this.Q.push(n)}else{if(this.seenFullScopeRequests.has(n.scopeName)||this.seenPartialScopeRequests.has(n.toKey()))continue;this.seenPartialScopeRequests.add(n.toKey()),this.Q.push(n)}}};function R0(e,t,n,r){const o=n.lookup(e.scopeName);if(!o){if(e.scopeName===t)throw new Error(`No grammar provided for <${t}>`);return}const i=n.lookup(t);e instanceof br?No({baseGrammar:i,selfGrammar:o},r):gs(e.ruleName,{baseGrammar:i,selfGrammar:o,repository:o.repository},r);const l=n.injections(e.scopeName);if(l)for(const s of l)r.add(new br(s))}function gs(e,t,n){if(t.repository&&t.repository[e]){const r=t.repository[e];ri([r],t,n)}}function No(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&ri(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&ri(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function ri(e,t,n){for(const r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);const o=r.repository?Mf({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&ri(r.patterns,{...t,repository:o},n);const i=r.include;if(!i)continue;const l=Wf(i);switch(l.kind){case 0:No({...t,selfGrammar:t.baseGrammar},n);break;case 1:No(t,n);break;case 2:gs(l.ruleName,{...t,repository:o},n);break;case 3:case 4:const s=l.scopeName===t.selfGrammar.scopeName?t.selfGrammar:l.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(s){const a={baseGrammar:t.baseGrammar,selfGrammar:s,repository:o};l.kind===4?gs(l.ruleName,a,n):No(a,n)}else l.kind===4?n.add(new C0(l.scopeName,l.ruleName)):n.add(new br(l.scopeName));break}}}var T0=class{constructor(){E(this,"kind",0)}},N0=class{constructor(){E(this,"kind",1)}},I0=class{constructor(e){E(this,"kind",2);this.ruleName=e}},A0=class{constructor(e){E(this,"kind",3);this.scopeName=e}},O0=class{constructor(e,t){E(this,"kind",4);this.scopeName=e,this.ruleName=t}};function Wf(e){if(e==="$base")return new T0;if(e==="$self")return new N0;const t=e.indexOf("#");if(t===-1)return new A0(e);if(t===0)return new I0(e.substring(1));{const n=e.substring(0,t),r=e.substring(t+1);return new O0(n,r)}}var b0=/\\(\d+)/,Hu=/\\(\d+)/g,D0=-1,Qf=-2;var Hr=class{constructor(e,t,n,r){E(this,"$location");E(this,"id");E(this,"_nameIsCapturing");E(this,"_name");E(this,"_contentNameIsCapturing");E(this,"_contentName");this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=mo.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=mo.hasCaptures(this._contentName)}get debugName(){const e=this.$location?`${Vf(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return!this._nameIsCapturing||this._name===null||e===null||t===null?this._name:mo.replaceCaptures(this._name,e,t)}getContentName(e,t){return!this._contentNameIsCapturing||this._contentName===null?this._contentName:mo.replaceCaptures(this._contentName,e,t)}},j0=class extends Hr{constructor(t,n,r,o,i){super(t,n,r,o);E(this,"retokenizeCapturedWithRuleId");this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(t,n){throw new Error("Not supported!")}compile(t,n){throw new Error("Not supported!")}compileAG(t,n,r,o){throw new Error("Not supported!")}},M0=class extends Hr{constructor(t,n,r,o,i){super(t,n,r,null);E(this,"_match");E(this,"captures");E(this,"_cachedCompiledPatterns");this._match=new Dr(o,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(t,n){n.push(this._match)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new jr,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Wu=class extends Hr{constructor(t,n,r,o,i){super(t,n,r,o);E(this,"hasMissingPatterns");E(this,"patterns");E(this,"_cachedCompiledPatterns");this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(t,n){for(const r of this.patterns)t.getRule(r).collectPatterns(t,n)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new jr,this.collectPatterns(t,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},_s=class extends Hr{constructor(t,n,r,o,i,l,s,a,u,c){super(t,n,r,o);E(this,"_begin");E(this,"beginCaptures");E(this,"_end");E(this,"endHasBackReferences");E(this,"endCaptures");E(this,"applyEndPatternLast");E(this,"hasMissingPatterns");E(this,"patterns");E(this,"_cachedCompiledPatterns");this._begin=new Dr(i,this.id),this.beginCaptures=l,this._end=new Dr(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=a,this.applyEndPatternLast=u||!1,this.patterns=c.patterns,this.hasMissingPatterns=c.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(t,n){return this._end.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t,n).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t,n).compileAG(t,r,o)}_getCachedCompiledPatterns(t,n){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new jr;for(const r of this.patterns)t.getRule(r).collectPatterns(t,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,n):this._cachedCompiledPatterns.setSource(0,n)),this._cachedCompiledPatterns}},oi=class extends Hr{constructor(t,n,r,o,i,l,s,a,u){super(t,n,r,o);E(this,"_begin");E(this,"beginCaptures");E(this,"whileCaptures");E(this,"_while");E(this,"whileHasBackReferences");E(this,"hasMissingPatterns");E(this,"patterns");E(this,"_cachedCompiledPatterns");E(this,"_cachedCompiledWhilePatterns");this._begin=new Dr(i,this.id),this.beginCaptures=l,this.whileCaptures=a,this._while=new Dr(s,Qf),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(t,n){return this._while.resolveBackReferences(t,n)}collectPatterns(t,n){n.push(this._begin)}compile(t,n){return this._getCachedCompiledPatterns(t).compile(t)}compileAG(t,n,r,o){return this._getCachedCompiledPatterns(t).compileAG(t,r,o)}_getCachedCompiledPatterns(t){if(!this._cachedCompiledPatterns){this._cachedCompiledPatterns=new jr;for(const n of this.patterns)t.getRule(n).collectPatterns(t,this._cachedCompiledPatterns)}return this._cachedCompiledPatterns}compileWhile(t,n){return this._getCachedCompiledWhilePatterns(t,n).compile(t)}compileWhileAG(t,n,r,o){return this._getCachedCompiledWhilePatterns(t,n).compileAG(t,r,o)}_getCachedCompiledWhilePatterns(t,n){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new jr,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,n||"￿"),this._cachedCompiledWhilePatterns}},Kf=class ge{static createCaptureRule(t,n,r,o,i){return t.registerRule(l=>new j0(n,l,r,o,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(o=>{if(t.id=o,t.match)return new M0(t.$vscodeTextmateLocation,t.id,t.name,t.match,ge._compileCaptures(t.captures,n,r));if(typeof t.begin>"u"){t.repository&&(r=Mf({},r,t.repository));let i=t.patterns;return typeof i>"u"&&t.include&&(i=[{include:t.include}]),new Wu(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,ge._compilePatterns(i,n,r))}return t.while?new oi(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,ge._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,ge._compileCaptures(t.whileCaptures||t.captures,n,r),ge._compilePatterns(t.patterns,n,r)):new _s(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,ge._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,ge._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,ge._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let o=[];if(t){let i=0;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);s>i&&(i=s)}for(let l=0;l<=i;l++)o[l]=null;for(const l in t){if(l==="$vscodeTextmateLocation")continue;const s=parseInt(l,10);let a=0;t[l].patterns&&(a=ge.getCompiledRuleId(t[l],n,r)),o[s]=ge.createCaptureRule(n,t[l].$vscodeTextmateLocation,t[l].name,t[l].contentName,a)}}return o}static _compilePatterns(t,n,r){let o=[];if(t)for(let i=0,l=t.length;i<l;i++){const s=t[i];let a=-1;if(s.include){const u=Wf(s.include);switch(u.kind){case 0:case 1:a=ge.getCompiledRuleId(r[s.include],n,r);break;case 2:let c=r[u.ruleName];c&&(a=ge.getCompiledRuleId(c,n,r));break;case 3:case 4:const p=u.scopeName,d=u.kind===4?u.ruleName:null,h=n.getExternalGrammar(p,r);if(h)if(d){let v=h.repository[d];v&&(a=ge.getCompiledRuleId(v,n,h.repository))}else a=ge.getCompiledRuleId(h.repository.$self,n,h.repository);break}}else a=ge.getCompiledRuleId(s,n,r);if(a!==-1){const u=n.getRule(a);let c=!1;if((u instanceof Wu||u instanceof _s||u instanceof oi)&&u.hasMissingPatterns&&u.patterns.length===0&&(c=!0),c)continue;o.push(a)}}return{patterns:o,hasMissingPatterns:(t?t.length:0)!==o.length}}},Dr=class qf{constructor(t,n){E(this,"source");E(this,"ruleId");E(this,"hasAnchor");E(this,"hasBackReferences");E(this,"_anchorCache");if(t&&typeof t=="string"){const r=t.length;let o=0,i=[],l=!1;for(let s=0;s<r;s++)if(t.charAt(s)==="\\"&&s+1<r){const u=t.charAt(s+1);u==="z"?(i.push(t.substring(o,s)),i.push("$(?!\\n)(?<!\\n)"),o=s+2):(u==="A"||u==="G")&&(l=!0),s++}this.hasAnchor=l,o===0?this.source=t:(i.push(t.substring(o,r)),this.source=i.join(""))}else this.hasAnchor=!1,this.source=t;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=n,typeof this.source=="string"?this.hasBackReferences=b0.test(this.source):this.hasBackReferences=!1}clone(){return new qf(this.source,this.ruleId)}setSource(t){this.source!==t&&(this.source=t,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(t,n){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let r=n.map(o=>t.substring(o.start,o.end));return Hu.lastIndex=0,this.source.replace(Hu,(o,i)=>$f(r[parseInt(i,10)]||""))}_buildAnchorCache(){if(typeof this.source!="string")throw new Error("This method should only be called if the source is a string");let t=[],n=[],r=[],o=[],i,l,s,a;for(i=0,l=this.source.length;i<l;i++)s=this.source.charAt(i),t[i]=s,n[i]=s,r[i]=s,o[i]=s,s==="\\"&&i+1<l&&(a=this.source.charAt(i+1),a==="A"?(t[i+1]="￿",n[i+1]="￿",r[i+1]="A",o[i+1]="A"):a==="G"?(t[i+1]="￿",n[i+1]="G",r[i+1]="￿",o[i+1]="G"):(t[i+1]=a,n[i+1]=a,r[i+1]=a,o[i+1]=a),i++);return{A0_G0:t.join(""),A0_G1:n.join(""),A1_G0:r.join(""),A1_G1:o.join("")}}resolveAnchors(t,n){return!this.hasAnchor||!this._anchorCache||typeof this.source!="string"?this.source:t?n?this._anchorCache.A1_G1:this._anchorCache.A1_G0:n?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},jr=class{constructor(){E(this,"_items");E(this,"_hasAnchors");E(this,"_cached");E(this,"_anchorCache");this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(n=>n.source);this._cached=new Qu(e,t,this._items.map(n=>n.ruleId))}return this._cached}compileAG(e,t,n){return this._hasAnchors?t?n?(this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1):(this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0):n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0):this.compile(e)}_resolveAnchors(e,t,n){let r=this._items.map(o=>o.resolveAnchors(t,n));return new Qu(e,r,this._items.map(o=>o.ruleId))}},Qu=class{constructor(e,t,n){E(this,"scanner");this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}dispose(){typeof this.scanner.dispose=="function"&&this.scanner.dispose()}toString(){const e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join(`
`)}findNextMatchSync(e,t,n){const r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},pl=class{constructor(e,t){this.languageId=e,this.tokenType=t}},ut,V0=(ut=class{constructor(t,n){E(this,"_defaultAttributes");E(this,"_embeddedLanguagesMatcher");E(this,"_getBasicScopeAttributes",new Ff(t=>{const n=this._scopeToLanguage(t),r=this._toStandardTokenType(t);return new pl(n,r)}));this._defaultAttributes=new pl(t,8),this._embeddedLanguagesMatcher=new z0(Object.entries(n||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return t===null?ut._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}_scopeToLanguage(t){return this._embeddedLanguagesMatcher.match(t)||0}_toStandardTokenType(t){const n=t.match(ut.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw new Error("Unexpected match for standard token type!")}},E(ut,"_NULL_SCOPE_METADATA",new pl(0,0)),E(ut,"STANDARD_TOKEN_TYPE_REGEXP",/\b(comment|string|regex|meta\.embedded)\b/),ut),z0=class{constructor(e){E(this,"values");E(this,"scopesRegExp");if(e.length===0)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);const t=e.map(([n,r])=>$f(n));t.sort(),t.reverse(),this.scopesRegExp=new RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;const t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}},Ku=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function Yf(e,t,n,r,o,i,l,s){const a=t.content.length;let u=!1,c=-1;if(l){const h=B0(e,t,n,r,o,i);o=h.stack,r=h.linePos,n=h.isFirstLine,c=h.anchorPosition}const p=Date.now();for(;!u;){if(s!==0&&Date.now()-p>s)return new Ku(o,!0);d()}return new Ku(o,!1);function d(){const h=$0(e,t,n,r,o,c);if(!h){i.produce(o,a),u=!0;return}const v=h.captureIndices,S=h.matchedRuleId,k=v&&v.length>0?v[0].end>r:!1;if(S===D0){const g=o.getRule(e);i.produce(o,v[0].start),o=o.withContentNameScopesList(o.nameScopesList),ir(e,t,n,o,i,g.endCaptures,v),i.produce(o,v[0].end);const m=o;if(o=o.parent,c=m.getAnchorPos(),!k&&m.getEnterPos()===r){o=m,i.produce(o,a),u=!0;return}}else{const g=e.getRule(S);i.produce(o,v[0].start);const m=o,y=g.getName(t.content,v),x=o.contentNameScopesList.pushAttributed(y,e);if(o=o.push(S,r,c,v[0].end===a,null,x,x),g instanceof _s){const w=g;ir(e,t,n,o,i,w.beginCaptures,v),i.produce(o,v[0].end),c=v[0].end;const P=w.getContentName(t.content,v),R=x.pushAttributed(P,e);if(o=o.withContentNameScopesList(R),w.endHasBackReferences&&(o=o.withEndRule(w.getEndWithResolvedBackReferences(t.content,v))),!k&&m.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(g instanceof oi){const w=g;ir(e,t,n,o,i,w.beginCaptures,v),i.produce(o,v[0].end),c=v[0].end;const P=w.getContentName(t.content,v),R=x.pushAttributed(P,e);if(o=o.withContentNameScopesList(R),w.whileHasBackReferences&&(o=o.withEndRule(w.getWhileWithResolvedBackReferences(t.content,v))),!k&&m.hasSameRuleAs(o)){o=o.pop(),i.produce(o,a),u=!0;return}}else if(ir(e,t,n,o,i,g.captures,v),i.produce(o,v[0].end),o=o.pop(),!k){o=o.safePop(),i.produce(o,a),u=!0;return}}v[0].end>r&&(r=v[0].end,n=!1)}}function B0(e,t,n,r,o,i){let l=o.beginRuleCapturedEOL?0:-1;const s=[];for(let a=o;a;a=a.pop()){const u=a.getRule(e);u instanceof oi&&s.push({rule:u,stack:a})}for(let a=s.pop();a;a=s.pop()){const{ruleScanner:u,findOptions:c}=G0(a.rule,e,a.stack.endRule,n,r===l),p=u.findNextMatchSync(t,r,c);if(p){if(p.ruleId!==Qf){o=a.stack.pop();break}p.captureIndices&&p.captureIndices.length&&(i.produce(a.stack,p.captureIndices[0].start),ir(e,t,n,a.stack,i,a.rule.whileCaptures,p.captureIndices),i.produce(a.stack,p.captureIndices[0].end),l=p.captureIndices[0].end,p.captureIndices[0].end>r&&(r=p.captureIndices[0].end,n=!1))}else{o=a.stack.pop();break}}return{stack:o,linePos:r,anchorPosition:l,isFirstLine:n}}function $0(e,t,n,r,o,i){const l=F0(e,t,n,r,o,i),s=e.getInjections();if(s.length===0)return l;const a=U0(s,e,t,n,r,o,i);if(!a)return l;if(!l)return a;const u=l.captureIndices[0].start,c=a.captureIndices[0].start;return c<u||a.priorityMatch&&c===u?a:l}function F0(e,t,n,r,o,i){const l=o.getRule(e),{ruleScanner:s,findOptions:a}=Xf(l,e,o.endRule,n,r===i),u=s.findNextMatchSync(t,r,a);return u?{captureIndices:u.captureIndices,matchedRuleId:u.ruleId}:null}function U0(e,t,n,r,o,i,l){let s=Number.MAX_VALUE,a=null,u,c=0;const p=i.contentNameScopesList.getScopeNames();for(let d=0,h=e.length;d<h;d++){const v=e[d];if(!v.matcher(p))continue;const S=t.getRule(v.ruleId),{ruleScanner:k,findOptions:g}=Xf(S,t,null,r,o===l),m=k.findNextMatchSync(n,o,g);if(!m)continue;const y=m.captureIndices[0].start;if(!(y>=s)&&(s=y,a=m.captureIndices,u=m.ruleId,c=v.priority,s===o))break}return a?{priorityMatch:c===-1,captureIndices:a,matchedRuleId:u}:null}function Xf(e,t,n,r,o){return{ruleScanner:e.compileAG(t,n,r,o),findOptions:0}}function G0(e,t,n,r,o){return{ruleScanner:e.compileWhileAG(t,n,r,o),findOptions:0}}function ir(e,t,n,r,o,i,l){if(i.length===0)return;const s=t.content,a=Math.min(i.length,l.length),u=[],c=l[0].end;for(let p=0;p<a;p++){const d=i[p];if(d===null)continue;const h=l[p];if(h.length===0)continue;if(h.start>c)break;for(;u.length>0&&u[u.length-1].endPos<=h.start;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?o.produceFromScopes(u[u.length-1].scopes,h.start):o.produce(r,h.start),d.retokenizeCapturedWithRuleId){const S=d.getName(s,l),k=r.contentNameScopesList.pushAttributed(S,e),g=d.getContentName(s,l),m=k.pushAttributed(g,e),y=r.push(d.retokenizeCapturedWithRuleId,h.start,-1,!1,null,k,m),x=e.createOnigString(s.substring(0,h.end));Yf(e,x,n&&h.start===0,h.start,y,o,!1,0),Hf(x);continue}const v=d.getName(s,l);if(v!==null){const k=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(v,e);u.push(new H0(k,h.end))}}for(;u.length>0;)o.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var H0=class{constructor(e,t){E(this,"scopes");E(this,"endPos");this.scopes=e,this.endPos=t}};function W0(e,t,n,r,o,i,l,s){return new K0(e,t,n,r,o,i,l,s)}function qu(e,t,n,r,o){const i=ni(t,ii),l=Kf.getCompiledRuleId(n,r,o.repository);for(const s of i)e.push({debugSelector:t,matcher:s.matcher,ruleId:l,grammar:o,priority:s.priority})}function ii(e,t){if(t.length<e.length)return!1;let n=0;return e.every(r=>{for(let o=n;o<t.length;o++)if(Q0(t[o],r))return n=o+1,!0;return!1})}function Q0(e,t){if(!e)return!1;if(e===t)return!0;const n=t.length;return e.length>n&&e.substr(0,n)===t&&e[n]==="."}var K0=class{constructor(e,t,n,r,o,i,l,s){E(this,"_rootId");E(this,"_lastRuleId");E(this,"_ruleId2desc");E(this,"_includedGrammars");E(this,"_grammarRepository");E(this,"_grammar");E(this,"_injections");E(this,"_basicScopeAttributesProvider");E(this,"_tokenTypeMatchers");if(this._rootScopeName=e,this.balancedBracketSelectors=i,this._onigLib=s,this._basicScopeAttributesProvider=new V0(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=l,this._grammar=Yu(t,null),this._injections=null,this._tokenTypeMatchers=[],o)for(const a of Object.keys(o)){const u=ni(a,ii);for(const c of u)this._tokenTypeMatchers.push({matcher:c.matcher,type:o[a]})}}get themeProvider(){return this._grammarRepository}dispose(){for(const e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){const e={lookup:o=>o===this._rootScopeName?this._grammar:this.getExternalGrammar(o),injections:o=>this._grammarRepository.injections(o)},t=[],n=this._rootScopeName,r=e.lookup(n);if(r){const o=r.injections;if(o)for(let l in o)qu(t,l,o[l],this,r);const i=this._grammarRepository.injections(n);i&&i.forEach(l=>{const s=this.getExternalGrammar(l);if(s){const a=s.injectionSelector;a&&qu(t,a,s,this,s)}})}return t.sort((o,i)=>o.priority-i.priority),t}getInjections(){return this._injections===null&&(this._injections=this._collectInjections()),this._injections}registerRule(e){const t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){const n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=Yu(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){const r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){const r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){this._rootId===-1&&(this._rootId=Kf.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections());let o;if(!t||t===ys.NULL){o=!0;const u=this._basicScopeAttributesProvider.getDefaultAttributes(),c=this.themeProvider.getDefaults(),p=Bn.set(0,u.languageId,u.tokenType,null,c.fontStyle,c.foregroundId,c.backgroundId),d=this.getRule(this._rootId).getName(null,null);let h;d?h=hr.createRootAndLookUpScopeName(d,p,this):h=hr.createRoot("unknown",p),t=new ys(null,this._rootId,-1,-1,!1,null,h,h)}else o=!1,t.reset();e=e+`
`;const i=this.createOnigString(e),l=i.content.length,s=new Y0(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),a=Yf(this,i,o,0,t,s,!0,r);return Hf(i),{lineLength:l,lineTokens:s,ruleStack:a.stack,stoppedEarly:a.stoppedEarly}}};function Yu(e,t){return e=p0(e),e.repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var hr=class nt{constructor(t,n,r){this.parent=t,this.scopePath=n,this.tokenAttributes=r}static fromExtension(t,n){let r=t,o=(t==null?void 0:t.scopePath)??null;for(const i of n)o=fl.push(o,i.scopeNames),r=new nt(r,o,i.encodedTokenAttributes);return r}static createRoot(t,n){return new nt(null,new fl(null,t),n)}static createRootAndLookUpScopeName(t,n,r){const o=r.getMetadataForScope(t),i=new fl(null,t),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(n,o,l);return new nt(null,i,s)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return nt.equals(this,t)}static equals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.scopeName!==n.scopeName||t.tokenAttributes!==n.tokenAttributes)return!1;t=t.parent,n=n.parent}while(!0)}static mergeAttributes(t,n,r){let o=-1,i=0,l=0;return r!==null&&(o=r.fontStyle,i=r.foregroundId,l=r.backgroundId),Bn.set(t,n.languageId,n.tokenType,null,o,i,l)}pushAttributed(t,n){if(t===null)return this;if(t.indexOf(" ")===-1)return nt._pushAttributed(this,t,n);const r=t.split(/ /g);let o=this;for(const i of r)o=nt._pushAttributed(o,i,n);return o}static _pushAttributed(t,n,r){const o=r.getMetadataForScope(n),i=t.scopePath.push(n),l=r.themeProvider.themeMatch(i),s=nt.mergeAttributes(t.tokenAttributes,o,l);return new nt(t,i,s)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(t){var o;const n=[];let r=this;for(;r&&r!==t;)n.push({encodedTokenAttributes:r.tokenAttributes,scopeNames:r.scopePath.getExtensionIfDefined(((o=r.parent)==null?void 0:o.scopePath)??null)}),r=r.parent;return r===t?n.reverse():void 0}},Fe,ys=(Fe=class{constructor(t,n,r,o,i,l,s,a){E(this,"_stackElementBrand");E(this,"_enterPos");E(this,"_anchorPos");E(this,"depth");this.parent=t,this.ruleId=n,this.beginRuleCapturedEOL=i,this.endRule=l,this.nameScopesList=s,this.contentNameScopesList=a,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=r,this._anchorPos=o}equals(t){return t===null?!1:Fe._equals(this,t)}static _equals(t,n){return t===n?!0:this._structuralEquals(t,n)?hr.equals(t.contentNameScopesList,n.contentNameScopesList):!1}static _structuralEquals(t,n){do{if(t===n||!t&&!n)return!0;if(!t||!n||t.depth!==n.depth||t.ruleId!==n.ruleId||t.endRule!==n.endRule)return!1;t=t.parent,n=n.parent}while(!0)}clone(){return this}static _reset(t){for(;t;)t._enterPos=-1,t._anchorPos=-1,t=t.parent}reset(){Fe._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,o,i,l,s){return new Fe(this,t,n,r,o,i,l,s)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(t){return t.getRule(this.ruleId)}toString(){const t=[];return this._writeString(t,0),"["+t.join(",")+"]"}_writeString(t,n){var r,o;return this.parent&&(n=this.parent._writeString(t,n)),t[n++]=`(${this.ruleId}, ${(r=this.nameScopesList)==null?void 0:r.toString()}, ${(o=this.contentNameScopesList)==null?void 0:o.toString()})`,n}withContentNameScopesList(t){return this.contentNameScopesList===t?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,t)}withEndRule(t){return this.endRule===t?this:new Fe(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(t){let n=this;for(;n&&n._enterPos===t._enterPos;){if(n.ruleId===t.ruleId)return!0;n=n.parent}return!1}toStateStackFrame(){var t,n,r;return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:((n=this.nameScopesList)==null?void 0:n.getExtensionIfDefined(((t=this.parent)==null?void 0:t.nameScopesList)??null))??[],contentNameScopesList:((r=this.contentNameScopesList)==null?void 0:r.getExtensionIfDefined(this.nameScopesList))??[]}}static pushFrame(t,n){const r=hr.fromExtension((t==null?void 0:t.nameScopesList)??null,n.nameScopesList);return new Fe(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,hr.fromExtension(r,n.contentNameScopesList))}},E(Fe,"NULL",new Fe(null,0,0,0,!1,null,null,null)),Fe),q0=class{constructor(e,t){E(this,"balancedBracketScopes");E(this,"unbalancedBracketScopes");E(this,"allowAny",!1);this.balancedBracketScopes=e.flatMap(n=>n==="*"?(this.allowAny=!0,[]):ni(n,ii).map(r=>r.matcher)),this.unbalancedBracketScopes=t.flatMap(n=>ni(n,ii).map(r=>r.matcher))}get matchesAlways(){return this.allowAny&&this.unbalancedBracketScopes.length===0}get matchesNever(){return this.balancedBracketScopes.length===0&&!this.allowAny}match(e){for(const t of this.unbalancedBracketScopes)if(t(e))return!1;for(const t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},Y0=class{constructor(e,t,n,r){E(this,"_emitBinaryTokens");E(this,"_lineText");E(this,"_tokens");E(this,"_binaryTokens");E(this,"_lastTokenEndIndex");E(this,"_tokenTypeOverrides");this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){var r;if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let o=(e==null?void 0:e.tokenAttributes)??0,i=!1;if((r=this.balancedBracketSelectors)!=null&&r.matchesAlways&&(i=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){const l=(e==null?void 0:e.getScopeNames())??[];for(const s of this._tokenTypeOverrides)s.matcher(l)&&(o=Bn.set(o,0,s.type,null,-1,0,0));this.balancedBracketSelectors&&(i=this.balancedBracketSelectors.match(l))}if(i&&(o=Bn.set(o,0,8,i,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===o){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(o),this._lastTokenEndIndex=t;return}const n=(e==null?void 0:e.getScopeNames())??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),this._tokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),this._binaryTokens.length===0&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);const n=new Uint32Array(this._binaryTokens.length);for(let r=0,o=this._binaryTokens.length;r<o;r++)n[r]=this._binaryTokens[r];return n}},X0=class{constructor(e,t){E(this,"_grammars",new Map);E(this,"_rawGrammars",new Map);E(this,"_injectionGrammars",new Map);E(this,"_theme");this._onigLib=t,this._theme=e}dispose(){for(const e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,o){if(!this._grammars.has(e)){let i=this._rawGrammars.get(e);if(!i)return null;this._grammars.set(e,W0(e,i,t,n,r,o,this,this._onigLib))}return this._grammars.get(e)}},J0=class{constructor(t){E(this,"_options");E(this,"_syncRegistry");E(this,"_ensureGrammarCache");this._options=t,this._syncRegistry=new X0(ti.createFromRawTheme(t.theme,t.colorMap),t.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(t,n){this._syncRegistry.setTheme(ti.createFromRawTheme(t,n))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(t,n,r){return this.loadGrammarWithConfiguration(t,n,{embeddedLanguages:r})}loadGrammarWithConfiguration(t,n,r){return this._loadGrammar(t,n,r.embeddedLanguages,r.tokenTypes,new q0(r.balancedBracketSelectors||[],r.unbalancedBracketSelectors||[]))}loadGrammar(t){return this._loadGrammar(t,0,null,null,null)}_loadGrammar(t,n,r,o,i){const l=new P0(this._syncRegistry,t);for(;l.Q.length>0;)l.Q.map(s=>this._loadSingleGrammar(s.scopeName)),l.processQueue();return this._grammarForScopeName(t,n,r,o,i)}_loadSingleGrammar(t){this._ensureGrammarCache.has(t)||(this._doLoadSingleGrammar(t),this._ensureGrammarCache.set(t,!0))}_doLoadSingleGrammar(t){const n=this._options.loadGrammar(t);if(n){const r=typeof this._options.getInjections=="function"?this._options.getInjections(t):void 0;this._syncRegistry.addGrammar(n,r)}}addGrammar(t,n=[],r=0,o=null){return this._syncRegistry.addGrammar(t,n),this._grammarForScopeName(t.scopeName,r,o)}_grammarForScopeName(t,n=0,r=null,o=null,i=null){return this._syncRegistry.grammarForScopeName(t,n,r,o,i)}},vs=ys.NULL;const Z0=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class Wr{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}Wr.prototype.normal={};Wr.prototype.property={};Wr.prototype.space=void 0;function Jf(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new Wr(n,r,t)}function xs(e){return e.toLowerCase()}class Te{constructor(t,n){this.attribute=n,this.property=t}}Te.prototype.attribute="";Te.prototype.booleanish=!1;Te.prototype.boolean=!1;Te.prototype.commaOrSpaceSeparated=!1;Te.prototype.commaSeparated=!1;Te.prototype.defined=!1;Te.prototype.mustUseProperty=!1;Te.prototype.number=!1;Te.prototype.overloadedBoolean=!1;Te.prototype.property="";Te.prototype.spaceSeparated=!1;Te.prototype.space=void 0;let e_=0;const j=un(),ee=un(),Ss=un(),L=un(),F=un(),Nn=un(),Ne=un();function un(){return 2**++e_}const Es=Object.freeze(Object.defineProperty({__proto__:null,boolean:j,booleanish:ee,commaOrSpaceSeparated:Ne,commaSeparated:Nn,number:L,overloadedBoolean:Ss,spaceSeparated:F},Symbol.toStringTag,{value:"Module"})),ml=Object.keys(Es);class Ea extends Te{constructor(t,n,r,o){let i=-1;if(super(t,n),Xu(this,"space",o),typeof r=="number")for(;++i<ml.length;){const l=ml[i];Xu(this,ml[i],(r&Es[l])===Es[l])}}}Ea.prototype.defined=!0;function Xu(e,t,n){n&&(e[t]=n)}function Gn(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new Ea(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[xs(r)]=r,n[xs(i.attribute)]=r}return new Wr(t,n,e.space)}const Zf=Gn({properties:{ariaActiveDescendant:null,ariaAtomic:ee,ariaAutoComplete:null,ariaBusy:ee,ariaChecked:ee,ariaColCount:L,ariaColIndex:L,ariaColSpan:L,ariaControls:F,ariaCurrent:null,ariaDescribedBy:F,ariaDetails:null,ariaDisabled:ee,ariaDropEffect:F,ariaErrorMessage:null,ariaExpanded:ee,ariaFlowTo:F,ariaGrabbed:ee,ariaHasPopup:null,ariaHidden:ee,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:F,ariaLevel:L,ariaLive:null,ariaModal:ee,ariaMultiLine:ee,ariaMultiSelectable:ee,ariaOrientation:null,ariaOwns:F,ariaPlaceholder:null,ariaPosInSet:L,ariaPressed:ee,ariaReadOnly:ee,ariaRelevant:null,ariaRequired:ee,ariaRoleDescription:F,ariaRowCount:L,ariaRowIndex:L,ariaRowSpan:L,ariaSelected:ee,ariaSetSize:L,ariaSort:null,ariaValueMax:L,ariaValueMin:L,ariaValueNow:L,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function ep(e,t){return t in e?e[t]:t}function tp(e,t){return ep(e,t.toLowerCase())}const t_=Gn({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Nn,acceptCharset:F,accessKey:F,action:null,allow:null,allowFullScreen:j,allowPaymentRequest:j,allowUserMedia:j,alt:null,as:null,async:j,autoCapitalize:null,autoComplete:F,autoFocus:j,autoPlay:j,blocking:F,capture:null,charSet:null,checked:j,cite:null,className:F,cols:L,colSpan:null,content:null,contentEditable:ee,controls:j,controlsList:F,coords:L|Nn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:j,defer:j,dir:null,dirName:null,disabled:j,download:Ss,draggable:ee,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:j,formTarget:null,headers:F,height:L,hidden:Ss,high:L,href:null,hrefLang:null,htmlFor:F,httpEquiv:F,id:null,imageSizes:null,imageSrcSet:null,inert:j,inputMode:null,integrity:null,is:null,isMap:j,itemId:null,itemProp:F,itemRef:F,itemScope:j,itemType:F,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:j,low:L,manifest:null,max:null,maxLength:L,media:null,method:null,min:null,minLength:L,multiple:j,muted:j,name:null,nonce:null,noModule:j,noValidate:j,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:j,optimum:L,pattern:null,ping:F,placeholder:null,playsInline:j,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:j,referrerPolicy:null,rel:F,required:j,reversed:j,rows:L,rowSpan:L,sandbox:F,scope:null,scoped:j,seamless:j,selected:j,shadowRootClonable:j,shadowRootDelegatesFocus:j,shadowRootMode:null,shape:null,size:L,sizes:null,slot:null,span:L,spellCheck:ee,src:null,srcDoc:null,srcLang:null,srcSet:null,start:L,step:null,style:null,tabIndex:L,target:null,title:null,translate:null,type:null,typeMustMatch:j,useMap:null,value:ee,width:L,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:F,axis:null,background:null,bgColor:null,border:L,borderColor:null,bottomMargin:L,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:j,declare:j,event:null,face:null,frame:null,frameBorder:null,hSpace:L,leftMargin:L,link:null,longDesc:null,lowSrc:null,marginHeight:L,marginWidth:L,noResize:j,noHref:j,noShade:j,noWrap:j,object:null,profile:null,prompt:null,rev:null,rightMargin:L,rules:null,scheme:null,scrolling:ee,standby:null,summary:null,text:null,topMargin:L,valueType:null,version:null,vAlign:null,vLink:null,vSpace:L,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:j,disableRemotePlayback:j,prefix:null,property:null,results:L,security:null,unselectable:null},space:"html",transform:tp}),n_=Gn({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:Ne,accentHeight:L,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:L,amplitude:L,arabicForm:null,ascent:L,attributeName:null,attributeType:null,azimuth:L,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:L,by:null,calcMode:null,capHeight:L,className:F,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:L,diffuseConstant:L,direction:null,display:null,dur:null,divisor:L,dominantBaseline:null,download:j,dx:null,dy:null,edgeMode:null,editable:null,elevation:L,enableBackground:null,end:null,event:null,exponent:L,externalResourcesRequired:null,fill:null,fillOpacity:L,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Nn,g2:Nn,glyphName:Nn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:L,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:L,horizOriginX:L,horizOriginY:L,id:null,ideographic:L,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:L,k:L,k1:L,k2:L,k3:L,k4:L,kernelMatrix:Ne,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:L,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:L,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:L,overlineThickness:L,paintOrder:null,panose1:null,path:null,pathLength:L,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:F,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:L,pointsAtY:L,pointsAtZ:L,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:Ne,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:Ne,rev:Ne,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:Ne,requiredFeatures:Ne,requiredFonts:Ne,requiredFormats:Ne,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:L,specularExponent:L,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:L,strikethroughThickness:L,string:null,stroke:null,strokeDashArray:Ne,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:L,strokeOpacity:L,strokeWidth:null,style:null,surfaceScale:L,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:Ne,tabIndex:L,tableValues:null,target:null,targetX:L,targetY:L,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:Ne,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:L,underlineThickness:L,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:L,values:null,vAlphabetic:L,vMathematical:L,vectorEffect:null,vHanging:L,vIdeographic:L,version:null,vertAdvY:L,vertOriginX:L,vertOriginY:L,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:L,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:ep}),np=Gn({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),rp=Gn({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:tp}),op=Gn({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),r_=/[A-Z]/g,Ju=/-[a-z]/g,o_=/^data[-\w.:]+$/i;function i_(e,t){const n=xs(t);let r=t,o=Te;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&o_.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(Ju,s_);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!Ju.test(i)){let l=i.replace(r_,l_);l.charAt(0)!=="-"&&(l="-"+l),t="data"+l}}o=Ea}return new o(r,t)}function l_(e){return"-"+e.toLowerCase()}function s_(e){return e.charAt(1).toUpperCase()}const a_=Jf([Zf,t_,np,rp,op],"html"),ip=Jf([Zf,n_,np,rp,op],"svg"),Zu={}.hasOwnProperty;function u_(e,t){const n=t||{};function r(o,...i){let l=r.invalid;const s=r.handlers;if(o&&Zu.call(o,e)){const a=String(o[e]);l=Zu.call(s,a)?s[a]:r.unknown}if(l)return l.call(this,o,...i)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}const c_=/["&'<>`]/g,d_=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,f_=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,p_=/[|\\{}()[\]^$+*?.]/g,ec=new WeakMap;function m_(e,t){if(e=e.replace(t.subset?h_(t.subset):c_,r),t.subset||t.escapeOnly)return e;return e.replace(d_,n).replace(f_,r);function n(o,i,l){return t.format((o.charCodeAt(0)-55296)*1024+o.charCodeAt(1)-56320+65536,l.charCodeAt(i+2),t)}function r(o,i,l){return t.format(o.charCodeAt(0),l.charCodeAt(i+1),t)}}function h_(e){let t=ec.get(e);return t||(t=g_(e),ec.set(e,t)),t}function g_(e){const t=[];let n=-1;for(;++n<e.length;)t.push(e[n].replace(p_,"\\$&"));return new RegExp("(?:"+t.join("|")+")","g")}const __=/[\dA-Fa-f]/;function y_(e,t,n){const r="&#x"+e.toString(16).toUpperCase();return n&&t&&!__.test(String.fromCharCode(t))?r:r+";"}const v_=/\d/;function x_(e,t,n){const r="&#"+String(e);return n&&t&&!v_.test(String.fromCharCode(t))?r:r+";"}const S_=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],hl={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},E_=["cent","copy","divide","gt","lt","not","para","times"],lp={}.hasOwnProperty,ws={};let ho;for(ho in hl)lp.call(hl,ho)&&(ws[hl[ho]]=ho);const w_=/[^\dA-Za-z]/;function k_(e,t,n,r){const o=String.fromCharCode(e);if(lp.call(ws,o)){const i=ws[o],l="&"+i;return n&&S_.includes(i)&&!E_.includes(i)&&(!r||t&&t!==61&&w_.test(String.fromCharCode(t)))?l:l+";"}return""}function C_(e,t,n){let r=y_(e,t,n.omitOptionalSemicolons),o;if((n.useNamedReferences||n.useShortestReferences)&&(o=k_(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!o)&&n.useShortestReferences){const i=x_(e,t,n.omitOptionalSemicolons);i.length<r.length&&(r=i)}return o&&(!n.useShortestReferences||o.length<r.length)?o:r}function In(e,t){return m_(e,Object.assign({format:C_},t))}const L_=/^>|^->|<!--|-->|--!>|<!-$/g,P_=[">"],R_=["<",">"];function T_(e,t,n,r){return r.settings.bogusComments?"<?"+In(e.value,Object.assign({},r.settings.characterReferences,{subset:P_}))+">":"<!--"+e.value.replace(L_,o)+"-->";function o(i){return In(i,Object.assign({},r.settings.characterReferences,{subset:R_}))}}function N_(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"}function tc(e,t){const n=String(e);if(typeof t!="string")throw new TypeError("Expected character");let r=0,o=n.indexOf(t);for(;o!==-1;)r++,o=n.indexOf(t,o+t.length);return r}function I_(e,t){const n=t||{};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}function A_(e){return e.join(" ").trim()}const O_=/[ \t\n\f\r]/g;function wa(e){return typeof e=="object"?e.type==="text"?nc(e.value):!1:nc(e)}function nc(e){return e.replace(O_,"")===""}const le=ap(1),sp=ap(-1),b_=[];function ap(e){return t;function t(n,r,o){const i=n?n.children:b_;let l=(r||0)+e,s=i[l];if(!o)for(;s&&wa(s);)l+=e,s=i[l];return s}}const D_={}.hasOwnProperty;function up(e){return t;function t(n,r,o){return D_.call(e,n.tagName)&&e[n.tagName](n,r,o)}}const ka=up({body:M_,caption:gl,colgroup:gl,dd:$_,dt:B_,head:gl,html:j_,li:z_,optgroup:F_,option:U_,p:V_,rp:rc,rt:rc,tbody:H_,td:oc,tfoot:W_,th:oc,thead:G_,tr:Q_});function gl(e,t,n){const r=le(n,t,!0);return!r||r.type!=="comment"&&!(r.type==="text"&&wa(r.value.charAt(0)))}function j_(e,t,n){const r=le(n,t);return!r||r.type!=="comment"}function M_(e,t,n){const r=le(n,t);return!r||r.type!=="comment"}function V_(e,t,n){const r=le(n,t);return r?r.type==="element"&&(r.tagName==="address"||r.tagName==="article"||r.tagName==="aside"||r.tagName==="blockquote"||r.tagName==="details"||r.tagName==="div"||r.tagName==="dl"||r.tagName==="fieldset"||r.tagName==="figcaption"||r.tagName==="figure"||r.tagName==="footer"||r.tagName==="form"||r.tagName==="h1"||r.tagName==="h2"||r.tagName==="h3"||r.tagName==="h4"||r.tagName==="h5"||r.tagName==="h6"||r.tagName==="header"||r.tagName==="hgroup"||r.tagName==="hr"||r.tagName==="main"||r.tagName==="menu"||r.tagName==="nav"||r.tagName==="ol"||r.tagName==="p"||r.tagName==="pre"||r.tagName==="section"||r.tagName==="table"||r.tagName==="ul"):!n||!(n.type==="element"&&(n.tagName==="a"||n.tagName==="audio"||n.tagName==="del"||n.tagName==="ins"||n.tagName==="map"||n.tagName==="noscript"||n.tagName==="video"))}function z_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="li"}function B_(e,t,n){const r=le(n,t);return!!(r&&r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd"))}function $_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="dt"||r.tagName==="dd")}function rc(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="rp"||r.tagName==="rt")}function F_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="optgroup"}function U_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="option"||r.tagName==="optgroup")}function G_(e,t,n){const r=le(n,t);return!!(r&&r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot"))}function H_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="tbody"||r.tagName==="tfoot")}function W_(e,t,n){return!le(n,t)}function Q_(e,t,n){const r=le(n,t);return!r||r.type==="element"&&r.tagName==="tr"}function oc(e,t,n){const r=le(n,t);return!r||r.type==="element"&&(r.tagName==="td"||r.tagName==="th")}const K_=up({body:X_,colgroup:J_,head:Y_,html:q_,tbody:Z_});function q_(e){const t=le(e,-1);return!t||t.type!=="comment"}function Y_(e){const t=new Set;for(const r of e.children)if(r.type==="element"&&(r.tagName==="base"||r.tagName==="title")){if(t.has(r.tagName))return!1;t.add(r.tagName)}const n=e.children[0];return!n||n.type==="element"}function X_(e){const t=le(e,-1,!0);return!t||t.type!=="comment"&&!(t.type==="text"&&wa(t.value.charAt(0)))&&!(t.type==="element"&&(t.tagName==="meta"||t.tagName==="link"||t.tagName==="script"||t.tagName==="style"||t.tagName==="template"))}function J_(e,t,n){const r=sp(n,t),o=le(e,-1,!0);return n&&r&&r.type==="element"&&r.tagName==="colgroup"&&ka(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="col")}function Z_(e,t,n){const r=sp(n,t),o=le(e,-1);return n&&r&&r.type==="element"&&(r.tagName==="thead"||r.tagName==="tbody")&&ka(r,n.children.indexOf(r),n)?!1:!!(o&&o.type==="element"&&o.tagName==="tr")}const go={name:[[`	
\f\r &/=>`.split(""),`	
\f\r "&'/=>\``.split("")],[`\0	
\f\r "&'/<=>`.split(""),`\0	
\f\r "&'/<=>\``.split("")]],unquoted:[[`	
\f\r &>`.split(""),`\0	
\f\r "&'<=>\``.split("")],[`\0	
\f\r "&'<=>\``.split(""),`\0	
\f\r "&'<=>\``.split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]};function ey(e,t,n,r){const o=r.schema,i=o.space==="svg"?!1:r.settings.omitOptionalTags;let l=o.space==="svg"?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase());const s=[];let a;o.space==="html"&&e.tagName==="svg"&&(r.schema=ip);const u=ty(r,e.properties),c=r.all(o.space==="html"&&e.tagName==="template"?e.content:e);return r.schema=o,c&&(l=!1),(u||!i||!K_(e,t,n))&&(s.push("<",e.tagName,u?" "+u:""),l&&(o.space==="svg"||r.settings.closeSelfClosing)&&(a=u.charAt(u.length-1),(!r.settings.tightSelfClosing||a==="/"||a&&a!=='"'&&a!=="'")&&s.push(" "),s.push("/")),s.push(">")),s.push(c),!l&&(!i||!ka(e,t,n))&&s.push("</"+e.tagName+">"),s.join("")}function ty(e,t){const n=[];let r=-1,o;if(t){for(o in t)if(t[o]!==null&&t[o]!==void 0){const i=ny(e,o,t[o]);i&&n.push(i)}}for(;++r<n.length;){const i=e.settings.tightAttributes?n[r].charAt(n[r].length-1):void 0;r!==n.length-1&&i!=='"'&&i!=="'"&&(n[r]+=" ")}return n.join("")}function ny(e,t,n){const r=i_(e.schema,t),o=e.settings.allowParseErrors&&e.schema.space==="html"?0:1,i=e.settings.allowDangerousCharacters?0:1;let l=e.quote,s;if(r.overloadedBoolean&&(n===r.attribute||n==="")?n=!0:(r.boolean||r.overloadedBoolean)&&(typeof n!="string"||n===r.attribute||n==="")&&(n=!!n),n==null||n===!1||typeof n=="number"&&Number.isNaN(n))return"";const a=In(r.attribute,Object.assign({},e.settings.characterReferences,{subset:go.name[o][i]}));return n===!0||(n=Array.isArray(n)?(r.commaSeparated?I_:A_)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?a:(e.settings.preferUnquoted&&(s=In(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:go.unquoted[o][i]}))),s!==n&&(e.settings.quoteSmart&&tc(n,l)>tc(n,e.alternative)&&(l=e.alternative),s=l+In(n,Object.assign({},e.settings.characterReferences,{subset:(l==="'"?go.single:go.double)[o][i],attribute:!0}))+l),a+(s&&"="+s))}const ry=["<","&"];function cp(e,t,n,r){return n&&n.type==="element"&&(n.tagName==="script"||n.tagName==="style")?e.value:In(e.value,Object.assign({},r.settings.characterReferences,{subset:ry}))}function oy(e,t,n,r){return r.settings.allowDangerousHtml?e.value:cp(e,t,n,r)}function iy(e,t,n,r){return r.all(e)}const ly=u_("type",{invalid:sy,unknown:ay,handlers:{comment:T_,doctype:N_,element:ey,raw:oy,root:iy,text:cp}});function sy(e){throw new Error("Expected node, not `"+e+"`")}function ay(e){const t=e;throw new Error("Cannot compile unknown node `"+t.type+"`")}const uy={},cy={},dy=[];function fy(e,t){const n=uy,r=n.quote||'"',o=r==='"'?"'":'"';if(r!=='"'&&r!=="'")throw new Error("Invalid quote `"+r+"`, expected `'` or `\"`");return{one:py,all:my,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||Z0,characterReferences:n.characterReferences||cy,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:n.space==="svg"?ip:a_,quote:r,alternative:o}.one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}function py(e,t,n){return ly(e,t,n,this)}function my(e){const t=[],n=e&&e.children||dy;let r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}function hy(e){return Array.isArray(e)?e:[e]}function Ai(e,t=!1){var i;const n=e.split(/(\r?\n)/g);let r=0;const o=[];for(let l=0;l<n.length;l+=2){const s=t?n[l]+(n[l+1]||""):n[l];o.push([s,r]),r+=n[l].length,r+=((i=n[l+1])==null?void 0:i.length)||0}return o}function Ca(e){return!e||["plaintext","txt","text","plain"].includes(e)}function dp(e){return e==="ansi"||Ca(e)}function La(e){return e==="none"}function fp(e){return La(e)}function pp(e,t){var r;if(!t)return e;e.properties||(e.properties={}),(r=e.properties).class||(r.class=[]),typeof e.properties.class=="string"&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]);const n=Array.isArray(t)?t:t.split(/\s+/g);for(const o of n)o&&!e.properties.class.includes(o)&&e.properties.class.push(o);return e}function gy(e,t){let n=0;const r=[];for(const o of t)o>n&&r.push({...e,content:e.content.slice(n,o),offset:e.offset+n}),n=o;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r}function _y(e,t){const n=Array.from(t instanceof Set?t:new Set(t)).sort((r,o)=>r-o);return n.length?e.map(r=>r.flatMap(o=>{const i=n.filter(l=>o.offset<l&&l<o.offset+o.content.length).map(l=>l-o.offset).sort((l,s)=>l-s);return i.length?gy(o,i):o})):e}async function mp(e){return Promise.resolve(typeof e=="function"?e():e).then(t=>t.default||t)}function li(e,t){const n=typeof e=="string"?{}:{...e.colorReplacements},r=typeof e=="string"?e:e.name;for(const[o,i]of Object.entries((t==null?void 0:t.colorReplacements)||{}))typeof i=="string"?n[o]=i:o===r&&Object.assign(n,i);return n}function Xt(e,t){return e&&((t==null?void 0:t[e==null?void 0:e.toLowerCase()])||e)}function hp(e){const t={};return e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle&&(e.fontStyle&pt.Italic&&(t["font-style"]="italic"),e.fontStyle&pt.Bold&&(t["font-weight"]="bold"),e.fontStyle&pt.Underline&&(t["text-decoration"]="underline")),t}function yy(e){return typeof e=="string"?e:Object.entries(e).map(([t,n])=>`${t}:${n}`).join(";")}function vy(e){const t=Ai(e,!0).map(([o])=>o);function n(o){if(o===e.length)return{line:t.length-1,character:t[t.length-1].length};let i=o,l=0;for(const s of t){if(i<s.length)break;i-=s.length,l++}return{line:l,character:i}}function r(o,i){let l=0;for(let s=0;s<o;s++)l+=t[s].length;return l+=i,l}return{lines:t,indexToPos:n,posToIndex:r}}class xe extends Error{constructor(t){super(t),this.name="ShikiError"}}const gp=new WeakMap;function Oi(e,t){gp.set(e,t)}function Mr(e){return gp.get(e)}class Hn{constructor(...t){E(this,"_stacks",{});E(this,"lang");if(t.length===2){const[n,r]=t;this.lang=r,this._stacks=n}else{const[n,r,o]=t;this.lang=r,this._stacks={[o]:n}}}get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(t,n){return new Hn(Object.fromEntries(hy(n).map(r=>[r,vs])),t)}getInternalStack(t=this.theme){return this._stacks[t]}get scopes(){return ic(this._stacks[this.theme])}getScopes(t=this.theme){return ic(this._stacks[t])}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.scopes}}}function ic(e){const t=[],n=new Set;function r(o){var l;if(n.has(o))return;n.add(o);const i=(l=o==null?void 0:o.nameScopesList)==null?void 0:l.scopeName;i&&t.push(i),o.parent&&r(o.parent)}return r(e),t}function xy(e,t){if(!(e instanceof Hn))throw new xe("Invalid grammar state");return e.getInternalStack(t)}function Sy(){const e=new WeakMap;function t(n){if(!e.has(n.meta)){let r=function(l){if(typeof l=="number"){if(l<0||l>n.source.length)throw new xe(`Invalid decoration offset: ${l}. Code length: ${n.source.length}`);return{...o.indexToPos(l),offset:l}}else{const s=o.lines[l.line];if(s===void 0)throw new xe(`Invalid decoration position ${JSON.stringify(l)}. Lines length: ${o.lines.length}`);if(l.character<0||l.character>s.length)throw new xe(`Invalid decoration position ${JSON.stringify(l)}. Line ${l.line} length: ${s.length}`);return{...l,offset:o.posToIndex(l.line,l.character)}}};const o=vy(n.source),i=(n.options.decorations||[]).map(l=>({...l,start:r(l.start),end:r(l.end)}));Ey(i),e.set(n.meta,{decorations:i,converter:o,source:n.source})}return e.get(n.meta)}return{name:"shiki:decorations",tokens(n){var l;if(!((l=this.options.decorations)!=null&&l.length))return;const o=t(this).decorations.flatMap(s=>[s.start.offset,s.end.offset]);return _y(n,o)},code(n){var c;if(!((c=this.options.decorations)!=null&&c.length))return;const r=t(this),o=Array.from(n.children).filter(p=>p.type==="element"&&p.tagName==="span");if(o.length!==r.converter.lines.length)throw new xe(`Number of lines in code element (${o.length}) does not match the number of lines in the source (${r.converter.lines.length}). Failed to apply decorations.`);function i(p,d,h,v){const S=o[p];let k="",g=-1,m=-1;if(d===0&&(g=0),h===0&&(m=0),h===Number.POSITIVE_INFINITY&&(m=S.children.length),g===-1||m===-1)for(let x=0;x<S.children.length;x++)k+=_p(S.children[x]),g===-1&&k.length===d&&(g=x+1),m===-1&&k.length===h&&(m=x+1);if(g===-1)throw new xe(`Failed to find start index for decoration ${JSON.stringify(v.start)}`);if(m===-1)throw new xe(`Failed to find end index for decoration ${JSON.stringify(v.end)}`);const y=S.children.slice(g,m);if(!v.alwaysWrap&&y.length===S.children.length)s(S,v,"line");else if(!v.alwaysWrap&&y.length===1&&y[0].type==="element")s(y[0],v,"token");else{const x={type:"element",tagName:"span",properties:{},children:y};s(x,v,"wrapper"),S.children.splice(g,y.length,x)}}function l(p,d){o[p]=s(o[p],d,"line")}function s(p,d,h){var k;const v=d.properties||{},S=d.transform||(g=>g);return p.tagName=d.tagName||"span",p.properties={...p.properties,...v,class:p.properties.class},(k=d.properties)!=null&&k.class&&pp(p,d.properties.class),p=S(p,h)||p,p}const a=[],u=r.decorations.sort((p,d)=>d.start.offset-p.start.offset);for(const p of u){const{start:d,end:h}=p;if(d.line===h.line)i(d.line,d.character,h.character,p);else if(d.line<h.line){i(d.line,d.character,Number.POSITIVE_INFINITY,p);for(let v=d.line+1;v<h.line;v++)a.unshift(()=>l(v,p));i(h.line,0,h.character,p)}}a.forEach(p=>p())}}}function Ey(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.start.offset>n.end.offset)throw new xe(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){const o=e[r],i=n.start.offset<o.start.offset&&o.start.offset<n.end.offset,l=n.start.offset<o.end.offset&&o.end.offset<n.end.offset,s=o.start.offset<n.start.offset&&n.start.offset<o.end.offset,a=o.start.offset<n.end.offset&&n.end.offset<o.end.offset;if(i||l||s||a){if(l&&l||s&&a)continue;throw new xe(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(o.start)} intersect.`)}}}}function _p(e){return e.type==="text"?e.value:e.type==="element"?e.children.map(_p).join(""):""}const wy=[Sy()];function si(e){return[...e.transformers||[],...wy]}var Jt=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],_l={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",9:"strikethrough"};function ky(e,t){const n=e.indexOf("\x1B[",t);if(n!==-1){const r=e.indexOf("m",n);return{sequence:e.substring(n+2,r).split(";"),startPosition:n,position:r+1}}return{position:e.length}}function lc(e,t){let n=1;const r=e[t+n++];let o;if(r==="2"){const i=[e[t+n++],e[t+n++],e[t+n]].map(l=>Number.parseInt(l));i.length===3&&!i.some(l=>Number.isNaN(l))&&(o={type:"rgb",rgb:i})}else if(r==="5"){const i=Number.parseInt(e[t+n]);Number.isNaN(i)||(o={type:"table",index:Number(i)})}return[n,o]}function Cy(e){const t=[];for(let n=0;n<e.length;n++){const r=e[n],o=Number.parseInt(r);if(!Number.isNaN(o))if(o===0)t.push({type:"resetAll"});else if(o<=9)_l[o]&&t.push({type:"setDecoration",value:_l[o]});else if(o<=29){const i=_l[o-20];i&&t.push({type:"resetDecoration",value:i})}else if(o<=37)t.push({type:"setForegroundColor",value:{type:"named",name:Jt[o-30]}});else if(o===38){const[i,l]=lc(e,n);l&&t.push({type:"setForegroundColor",value:l}),n+=i}else if(o===39)t.push({type:"resetForegroundColor"});else if(o<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:Jt[o-40]}});else if(o===48){const[i,l]=lc(e,n);l&&t.push({type:"setBackgroundColor",value:l}),n+=i}else o===49?t.push({type:"resetBackgroundColor"}):o>=90&&o<=97?t.push({type:"setForegroundColor",value:{type:"named",name:Jt[o-90+8]}}):o>=100&&o<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:Jt[o-100+8]}})}return t}function Ly(){let e=null,t=null,n=new Set;return{parse(r){const o=[];let i=0;do{const l=ky(r,i),s=l.sequence?r.substring(i,l.startPosition):r.substring(i);if(s.length>0&&o.push({value:s,foreground:e,background:t,decorations:new Set(n)}),l.sequence){const a=Cy(l.sequence);for(const u of a)u.type==="resetAll"?(e=null,t=null,n.clear()):u.type==="resetForegroundColor"?e=null:u.type==="resetBackgroundColor"?t=null:u.type==="resetDecoration"&&n.delete(u.value);for(const u of a)u.type==="setForegroundColor"?e=u.value:u.type==="setBackgroundColor"?t=u.value:u.type==="setDecoration"&&n.add(u.value)}i=l.position}while(i<r.length);return o}}}var Py={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function Ry(e=Py){function t(s){return e[s]}function n(s){return`#${s.map(a=>Math.max(0,Math.min(a,255)).toString(16).padStart(2,"0")).join("")}`}let r;function o(){if(r)return r;r=[];for(let u=0;u<Jt.length;u++)r.push(t(Jt[u]));let s=[0,95,135,175,215,255];for(let u=0;u<6;u++)for(let c=0;c<6;c++)for(let p=0;p<6;p++)r.push(n([s[u],s[c],s[p]]));let a=8;for(let u=0;u<24;u++,a+=10)r.push(n([a,a,a]));return r}function i(s){return o()[s]}function l(s){switch(s.type){case"named":return t(s.name);case"rgb":return n(s.rgb);case"table":return i(s.index)}}return{value:l}}function Ty(e,t,n){const r=li(e,n),o=Ai(t),i=Ry(Object.fromEntries(Jt.map(s=>{var a;return[s,(a=e.colors)==null?void 0:a[`terminal.ansi${s[0].toUpperCase()}${s.substring(1)}`]]}))),l=Ly();return o.map(s=>l.parse(s[0]).map(a=>{let u,c;a.decorations.has("reverse")?(u=a.background?i.value(a.background):e.bg,c=a.foreground?i.value(a.foreground):e.fg):(u=a.foreground?i.value(a.foreground):e.fg,c=a.background?i.value(a.background):void 0),u=Xt(u,r),c=Xt(c,r),a.decorations.has("dim")&&(u=Ny(u));let p=pt.None;return a.decorations.has("bold")&&(p|=pt.Bold),a.decorations.has("italic")&&(p|=pt.Italic),a.decorations.has("underline")&&(p|=pt.Underline),{content:a.value,offset:s[1],color:u,bgColor:c,fontStyle:p}}))}function Ny(e){const t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){const r=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${r}`}else return t[2]?`#${t[1]}${t[2]}80`:`#${Array.from(t[1]).map(r=>`${r}${r}`).join("")}80`;const n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}function Pa(e,t,n={}){const{lang:r="text",theme:o=e.getLoadedThemes()[0]}=n;if(Ca(r)||La(o))return Ai(t).map(a=>[{content:a[0],offset:a[1]}]);const{theme:i,colorMap:l}=e.setTheme(o);if(r==="ansi")return Ty(i,t,n);const s=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==s.name)throw new ht(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${s.name}"`);if(!n.grammarState.themes.includes(i.name))throw new ht(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${i.name}"`)}return Ay(t,s,i,l,n)}function Iy(...e){if(e.length===2)return Mr(e[1]);const[t,n,r={}]=e,{lang:o="text",theme:i=t.getLoadedThemes()[0]}=r;if(Ca(o)||La(i))throw new ht("Plain language does not have grammar state");if(o==="ansi")throw new ht("ANSI language does not have grammar state");const{theme:l,colorMap:s}=t.setTheme(i),a=t.getLanguage(o);return new Hn(ai(n,a,l,s,r).stateStack,a.name,l.name)}function Ay(e,t,n,r,o){const i=ai(e,t,n,r,o),l=new Hn(ai(e,t,n,r,o).stateStack,t.name,n.name);return Oi(i.tokens,l),i.tokens}function ai(e,t,n,r,o){const i=li(n,o),{tokenizeMaxLineLength:l=0,tokenizeTimeLimit:s=500}=o,a=Ai(e);let u=o.grammarState?xy(o.grammarState,n.name)??vs:o.grammarContextCode!=null?ai(o.grammarContextCode,t,n,r,{...o,grammarState:void 0,grammarContextCode:void 0}).stateStack:vs,c=[];const p=[];for(let d=0,h=a.length;d<h;d++){const[v,S]=a[d];if(v===""){c=[],p.push([]);continue}if(l>0&&v.length>=l){c=[],p.push([{content:v,offset:S,color:"",fontStyle:0}]);continue}let k,g,m;o.includeExplanation&&(k=t.tokenizeLine(v,u),g=k.tokens,m=0);const y=t.tokenizeLine2(v,u,s),x=y.tokens.length/2;for(let w=0;w<x;w++){const P=y.tokens[2*w],R=w+1<x?y.tokens[2*w+2]:v.length;if(P===R)continue;const N=y.tokens[2*w+1],$=Xt(r[Bn.getForeground(N)],i),b=Bn.getFontStyle(N),A={content:v.substring(P,R),offset:S+P,color:$,fontStyle:b};if(o.includeExplanation){const Z=[];if(o.includeExplanation!=="scopeName")for(const Ve of n.settings){let St;switch(typeof Ve.scope){case"string":St=Ve.scope.split(/,/).map(Ut=>Ut.trim());break;case"object":St=Ve.scope;break;default:continue}Z.push({settings:Ve,selectors:St.map(Ut=>Ut.split(/ /))})}A.explanation=[];let Me=0;for(;P+Me<R;){const Ve=g[m],St=v.substring(Ve.startIndex,Ve.endIndex);Me+=St.length,A.explanation.push({content:St,scopes:o.includeExplanation==="scopeName"?Oy(Ve.scopes):by(Z,Ve.scopes)}),m+=1}}c.push(A)}p.push(c),c=[],u=y.ruleStack}return{tokens:p,stateStack:u}}function Oy(e){return e.map(t=>({scopeName:t}))}function by(e,t){const n=[];for(let r=0,o=t.length;r<o;r++){const i=t[r];n[r]={scopeName:i,themeMatches:jy(e,i,t.slice(0,r))}}return n}function sc(e,t){return e===t||t.substring(0,e.length)===e&&t[e.length]==="."}function Dy(e,t,n){if(!sc(e[e.length-1],t))return!1;let r=e.length-2,o=n.length-1;for(;r>=0&&o>=0;)sc(e[r],n[o])&&(r-=1),o-=1;return r===-1}function jy(e,t,n){const r=[];for(const{selectors:o,settings:i}of e)for(const l of o)if(Dy(l,t,n)){r.push(i);break}return r}function yp(e,t,n){const r=Object.entries(n.themes).filter(a=>a[1]).map(a=>({color:a[0],theme:a[1]})),o=r.map(a=>{const u=Pa(e,t,{...n,theme:a.theme}),c=Mr(u),p=typeof a.theme=="string"?a.theme:a.theme.name;return{tokens:u,state:c,theme:p}}),i=My(...o.map(a=>a.tokens)),l=i[0].map((a,u)=>a.map((c,p)=>{const d={content:c.content,variants:{},offset:c.offset};return"includeExplanation"in n&&n.includeExplanation&&(d.explanation=c.explanation),i.forEach((h,v)=>{const{content:S,explanation:k,offset:g,...m}=h[u][p];d.variants[r[v].color]=m}),d})),s=o[0].state?new Hn(Object.fromEntries(o.map(a=>{var u;return[a.theme,(u=a.state)==null?void 0:u.getInternalStack(a.theme)]})),o[0].state.lang):void 0;return s&&Oi(l,s),l}function My(...e){const t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){const o=e.map(a=>a[r]),i=t.map(()=>[]);t.forEach((a,u)=>a.push(i[u]));const l=o.map(()=>0),s=o.map(a=>a[0]);for(;s.every(a=>a);){const a=Math.min(...s.map(u=>u.content.length));for(let u=0;u<n;u++){const c=s[u];c.content.length===a?(i[u].push(c),l[u]+=1,s[u]=o[u][l[u]]):(i[u].push({...c,content:c.content.slice(0,a)}),s[u]={...c,content:c.content.slice(a),offset:c.offset+a})}}}return t}function ui(e,t,n){let r,o,i,l,s,a;if("themes"in n){const{defaultColor:u="light",cssVariablePrefix:c="--shiki-"}=n,p=Object.entries(n.themes).filter(k=>k[1]).map(k=>({color:k[0],theme:k[1]})).sort((k,g)=>k.color===u?-1:g.color===u?1:0);if(p.length===0)throw new ht("`themes` option must not be empty");const d=yp(e,t,n);if(a=Mr(d),u&&!p.find(k=>k.color===u))throw new ht(`\`themes\` option must contain the defaultColor key \`${u}\``);const h=p.map(k=>e.getTheme(k.theme)),v=p.map(k=>k.color);i=d.map(k=>k.map(g=>Vy(g,v,c,u))),a&&Oi(i,a);const S=p.map(k=>li(k.theme,n));o=p.map((k,g)=>(g===0&&u?"":`${c+k.color}:`)+(Xt(h[g].fg,S[g])||"inherit")).join(";"),r=p.map((k,g)=>(g===0&&u?"":`${c+k.color}-bg:`)+(Xt(h[g].bg,S[g])||"inherit")).join(";"),l=`shiki-themes ${h.map(k=>k.name).join(" ")}`,s=u?void 0:[o,r].join(";")}else if("theme"in n){const u=li(n.theme,n);i=Pa(e,t,n);const c=e.getTheme(n.theme);r=Xt(c.bg,u),o=Xt(c.fg,u),l=c.name,a=Mr(i)}else throw new ht("Invalid options, either `theme` or `themes` must be provided");return{tokens:i,fg:o,bg:r,themeName:l,rootStyle:s,grammarState:a}}function Vy(e,t,n,r){const o={content:e.content,explanation:e.explanation,offset:e.offset},i=t.map(a=>hp(e.variants[a])),l=new Set(i.flatMap(a=>Object.keys(a))),s={};return i.forEach((a,u)=>{for(const c of l){const p=a[c]||"inherit";if(u===0&&r)s[c]=p;else{const d=c==="color"?"":c==="background-color"?"-bg":`-${c}`,h=n+t[u]+(c==="color"?"":d);s[h]=p}}}),o.htmlStyle=s,o}function ci(e,t,n,r={meta:{},options:n,codeToHast:(o,i)=>ci(e,o,i),codeToTokens:(o,i)=>ui(e,o,i)}){var h,v;let o=t;for(const S of si(n))o=((h=S.preprocess)==null?void 0:h.call(r,o,n))||o;let{tokens:i,fg:l,bg:s,themeName:a,rootStyle:u,grammarState:c}=ui(e,o,n);const{mergeWhitespaces:p=!0}=n;p===!0?i=By(i):p==="never"&&(i=$y(i));const d={...r,get source(){return o}};for(const S of si(n))i=((v=S.tokens)==null?void 0:v.call(d,i))||i;return zy(i,{...n,fg:l,bg:s,themeName:a,rootStyle:u},d,c)}function zy(e,t,n,r=Mr(e)){var v,S,k;const o=si(t),i=[],l={type:"root",children:[]},{structure:s="classic",tabindex:a="0"}=t;let u={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...a!==!1&&a!=null?{tabindex:a.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([g])=>!g.startsWith("_")))},children:[]},c={type:"element",tagName:"code",properties:{},children:i};const p=[],d={...n,structure:s,addClassToHast:pp,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return l},get pre(){return u},get code(){return c},get lines(){return p}};if(e.forEach((g,m)=>{var w,P;m&&(s==="inline"?l.children.push({type:"element",tagName:"br",properties:{},children:[]}):s==="classic"&&i.push({type:"text",value:`
`}));let y={type:"element",tagName:"span",properties:{class:"line"},children:[]},x=0;for(const R of g){let N={type:"element",tagName:"span",properties:{...R.htmlAttrs},children:[{type:"text",value:R.content}]};R.htmlStyle;const $=yy(R.htmlStyle||hp(R));$&&(N.properties.style=$);for(const b of o)N=((w=b==null?void 0:b.span)==null?void 0:w.call(d,N,m+1,x,y,R))||N;s==="inline"?l.children.push(N):s==="classic"&&y.children.push(N),x+=R.content.length}if(s==="classic"){for(const R of o)y=((P=R==null?void 0:R.line)==null?void 0:P.call(d,y,m+1))||y;p.push(y),i.push(y)}}),s==="classic"){for(const g of o)c=((v=g==null?void 0:g.code)==null?void 0:v.call(d,c))||c;u.children.push(c);for(const g of o)u=((S=g==null?void 0:g.pre)==null?void 0:S.call(d,u))||u;l.children.push(u)}let h=l;for(const g of o)h=((k=g==null?void 0:g.root)==null?void 0:k.call(d,h))||h;return r&&Oi(h,r),h}function By(e){return e.map(t=>{const n=[];let r="",o=0;return t.forEach((i,l)=>{const a=!(i.fontStyle&&i.fontStyle&pt.Underline);a&&i.content.match(/^\s+$/)&&t[l+1]?(o||(o=i.offset),r+=i.content):r?(a?n.push({...i,offset:o,content:r+i.content}):n.push({content:r,offset:o},i),o=0,r=""):n.push(i)}),n})}function $y(e){return e.map(t=>t.flatMap(n=>{if(n.content.match(/^\s+$/))return n;const r=n.content.match(/^(\s*)(.*?)(\s*)$/);if(!r)return n;const[,o,i,l]=r;if(!o&&!l)return n;const s=[{...n,offset:n.offset+o.length,content:i}];return o&&s.unshift({content:o,offset:n.offset}),l&&s.push({content:l,offset:n.offset+o.length+i.length}),s}))}function Fy(e,t,n){var i;const r={meta:{},options:n,codeToHast:(l,s)=>ci(e,l,s),codeToTokens:(l,s)=>ui(e,l,s)};let o=fy(ci(e,t,n,r));for(const l of si(n))o=((i=l.postprocess)==null?void 0:i.call(r,o,n))||o;return o}const ac={light:"#333333",dark:"#bbbbbb"},uc={light:"#fffffe",dark:"#1e1e1e"},cc="__shiki_resolved";function Ra(e){var s,a,u,c,p;if(e!=null&&e[cc])return e;const t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||(t.type="dark"),t.colorReplacements={...t.colorReplacements},t.settings||(t.settings=[]);let{bg:n,fg:r}=t;if(!n||!r){const d=t.settings?t.settings.find(h=>!h.name&&!h.scope):void 0;(s=d==null?void 0:d.settings)!=null&&s.foreground&&(r=d.settings.foreground),(a=d==null?void 0:d.settings)!=null&&a.background&&(n=d.settings.background),!r&&((u=t==null?void 0:t.colors)!=null&&u["editor.foreground"])&&(r=t.colors["editor.foreground"]),!n&&((c=t==null?void 0:t.colors)!=null&&c["editor.background"])&&(n=t.colors["editor.background"]),r||(r=t.type==="light"?ac.light:ac.dark),n||(n=t.type==="light"?uc.light:uc.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let o=0;const i=new Map;function l(d){var v;if(i.has(d))return i.get(d);o+=1;const h=`#${o.toString(16).padStart(8,"0").toLowerCase()}`;return(v=t.colorReplacements)!=null&&v[`#${h}`]?l(d):(i.set(d,h),h)}t.settings=t.settings.map(d=>{var k,g;const h=((k=d.settings)==null?void 0:k.foreground)&&!d.settings.foreground.startsWith("#"),v=((g=d.settings)==null?void 0:g.background)&&!d.settings.background.startsWith("#");if(!h&&!v)return d;const S={...d,settings:{...d.settings}};if(h){const m=l(d.settings.foreground);t.colorReplacements[m]=d.settings.foreground,S.settings.foreground=m}if(v){const m=l(d.settings.background);t.colorReplacements[m]=d.settings.background,S.settings.background=m}return S});for(const d of Object.keys(t.colors||{}))if((d==="editor.foreground"||d==="editor.background"||d.startsWith("terminal.ansi"))&&!((p=t.colors[d])!=null&&p.startsWith("#"))){const h=l(t.colors[d]);t.colorReplacements[h]=t.colors[d],t.colors[d]=h}return Object.defineProperty(t,cc,{enumerable:!1,writable:!1,value:!0}),t}async function vp(e){return Array.from(new Set((await Promise.all(e.filter(t=>!dp(t)).map(async t=>await mp(t).then(n=>Array.isArray(n)?n:[n])))).flat()))}async function xp(e){return(await Promise.all(e.map(async n=>fp(n)?null:Ra(await mp(n))))).filter(n=>!!n)}class Uy extends J0{constructor(n,r,o,i={}){super(n);E(this,"_resolvedThemes",new Map);E(this,"_resolvedGrammars",new Map);E(this,"_langMap",new Map);E(this,"_langGraph",new Map);E(this,"_textmateThemeCache",new WeakMap);E(this,"_loadedThemesCache",null);E(this,"_loadedLanguagesCache",null);this._resolver=n,this._themes=r,this._langs=o,this._alias=i,this._themes.map(l=>this.loadTheme(l)),this.loadLanguages(this._langs)}getTheme(n){return typeof n=="string"?this._resolvedThemes.get(n):this.loadTheme(n)}loadTheme(n){const r=Ra(n);return r.name&&(this._resolvedThemes.set(r.name,r),this._loadedThemesCache=null),r}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(n){let r=this._textmateThemeCache.get(n);r||(r=ti.createFromRawTheme(n),this._textmateThemeCache.set(n,r)),this._syncRegistry.setTheme(r)}getGrammar(n){if(this._alias[n]){const r=new Set([n]);for(;this._alias[n];){if(n=this._alias[n],r.has(n))throw new xe(`Circular alias \`${Array.from(r).join(" -> ")} -> ${n}\``);r.add(n)}}return this._resolvedGrammars.get(n)}loadLanguage(n){var l,s,a,u;if(this.getGrammar(n.name))return;const r=new Set([...this._langMap.values()].filter(c=>{var p;return(p=c.embeddedLangsLazy)==null?void 0:p.includes(n.name)}));this._resolver.addLanguage(n);const o={balancedBracketSelectors:n.balancedBracketSelectors||["*"],unbalancedBracketSelectors:n.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(n.scopeName,n);const i=this.loadGrammarWithConfiguration(n.scopeName,1,o);if(i.name=n.name,this._resolvedGrammars.set(n.name,i),n.aliases&&n.aliases.forEach(c=>{this._alias[c]=n.name}),this._loadedLanguagesCache=null,r.size)for(const c of r)this._resolvedGrammars.delete(c.name),this._loadedLanguagesCache=null,(s=(l=this._syncRegistry)==null?void 0:l._injectionGrammars)==null||s.delete(c.scopeName),(u=(a=this._syncRegistry)==null?void 0:a._grammars)==null||u.delete(c.scopeName),this.loadLanguage(this._langMap.get(c.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(n){for(const i of n)this.resolveEmbeddedLanguages(i);const r=Array.from(this._langGraph.entries()),o=r.filter(([i,l])=>!l);if(o.length){const i=r.filter(([l,s])=>{var a;return s&&((a=s.embeddedLangs)==null?void 0:a.some(u=>o.map(([c])=>c).includes(u)))}).filter(l=>!o.includes(l));throw new xe(`Missing languages ${o.map(([l])=>`\`${l}\``).join(", ")}, required by ${i.map(([l])=>`\`${l}\``).join(", ")}`)}for(const[i,l]of r)this._resolver.addLanguage(l);for(const[i,l]of r)this.loadLanguage(l)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(n){if(this._langMap.set(n.name,n),this._langGraph.set(n.name,n),n.embeddedLangs)for(const r of n.embeddedLangs)this._langGraph.set(r,this._langMap.get(r))}}class Gy{constructor(t,n){E(this,"_langs",new Map);E(this,"_scopeToLang",new Map);E(this,"_injections",new Map);E(this,"_onigLib");this._onigLib={createOnigScanner:r=>t.createScanner(r),createOnigString:r=>t.createString(r)},n.forEach(r=>this.addLanguage(r))}get onigLib(){return this._onigLib}getLangRegistration(t){return this._langs.get(t)}loadGrammar(t){return this._scopeToLang.get(t)}addLanguage(t){this._langs.set(t.name,t),t.aliases&&t.aliases.forEach(n=>{this._langs.set(n,t)}),this._scopeToLang.set(t.scopeName,t),t.injectTo&&t.injectTo.forEach(n=>{this._injections.get(n)||this._injections.set(n,[]),this._injections.get(n).push(t.scopeName)})}getInjections(t){const n=t.split(".");let r=[];for(let o=1;o<=n.length;o++){const i=n.slice(0,o).join(".");r=[...r,...this._injections.get(i)||[]]}return r}}let er=0;function Hy(e){er+=1,e.warnings!==!1&&er>=10&&er%10===0&&console.warn(`[Shiki] ${er} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let t=!1;if(!e.engine)throw new xe("`engine` option is required for synchronous mode");const n=(e.langs||[]).flat(1),r=(e.themes||[]).flat(1).map(Ra),o=new Gy(e.engine,n),i=new Uy(o,r,n,e.langAlias);let l;function s(m){k();const y=i.getGrammar(typeof m=="string"?m:m.name);if(!y)throw new xe(`Language \`${m}\` not found, you may need to load it first`);return y}function a(m){if(m==="none")return{bg:"",fg:"",name:"none",settings:[],type:"dark"};k();const y=i.getTheme(m);if(!y)throw new xe(`Theme \`${m}\` not found, you may need to load it first`);return y}function u(m){k();const y=a(m);l!==m&&(i.setTheme(y),l=m);const x=i.getColorMap();return{theme:y,colorMap:x}}function c(){return k(),i.getLoadedThemes()}function p(){return k(),i.getLoadedLanguages()}function d(...m){k(),i.loadLanguages(m.flat(1))}async function h(...m){return d(await vp(m))}function v(...m){k();for(const y of m.flat(1))i.loadTheme(y)}async function S(...m){return k(),v(await xp(m))}function k(){if(t)throw new xe("Shiki instance has been disposed")}function g(){t||(t=!0,i.dispose(),er-=1)}return{setTheme:u,getTheme:a,getLanguage:s,getLoadedThemes:c,getLoadedLanguages:p,loadLanguage:h,loadLanguageSync:d,loadTheme:S,loadThemeSync:v,dispose:g,[Symbol.dispose]:g}}async function Wy(e={}){e.loadWasm;const[t,n,r]=await Promise.all([xp(e.themes||[]),vp(e.langs||[]),e.engine||jf(e.loadWasm||f0())]);return Hy({...e,themes:t,langs:n,engine:r})}async function Qy(e={}){const t=await Wy(e);return{getLastGrammarState:(...n)=>Iy(t,...n),codeToTokensBase:(n,r)=>Pa(t,n,r),codeToTokensWithThemes:(n,r)=>yp(t,n,r),codeToTokens:(n,r)=>ui(t,n,r),codeToHast:(n,r)=>ci(t,n,r),codeToHtml:(n,r)=>Fy(t,n,r),...t,getInternalContext:()=>t}}function Ky(e,t,n){let r,o,i;{const s=e;r=s.langs,o=s.themes,i=s.engine}async function l(s){function a(h){if(typeof h=="string"){if(dp(h))return[];const v=r[h];if(!v)throw new ht(`Language \`${h}\` is not included in this bundle. You may want to load it from external source.`);return v}return h}function u(h){if(fp(h))return"none";if(typeof h=="string"){const v=o[h];if(!v)throw new ht(`Theme \`${h}\` is not included in this bundle. You may want to load it from external source.`);return v}return h}const c=(s.themes??[]).map(h=>u(h)),p=(s.langs??[]).map(h=>a(h)),d=await Qy({engine:s.engine??i(),...s,themes:c,langs:p});return{...d,loadLanguage(...h){return d.loadLanguage(...h.map(a))},loadTheme(...h){return d.loadTheme(...h.map(u))}}}return l}const qy=Ky({langs:Wg,themes:Kg,engine:()=>jf(f(()=>import("./assets/wasm-CG6Dc4jp.js"),[],import.meta.url))}),Yy=({content:e})=>{const[t,n]=O.useState(null);if(O.useEffect(()=>{qy({themes:["github-dark"],langs:["javascript","typescript","python","bash","html","css"]}).then(n)},[]),!t)return _.jsx("pre",{className:"whitespace-pre-wrap",children:e});const r=e.split(/(```[\s\S]*?```)/g);return _.jsx(_.Fragment,{children:r.map((o,i)=>{if(o.startsWith("```")){const l=o.split(`
`),s=l[0].replace("```","").trim(),a=l.slice(1,-1).join(`
`);return _.jsxs("div",{className:"relative",children:[_.jsxs("div",{className:"absolute top-1 right-1 flex gap-1",children:[_.jsx("button",{title:"Copy",onClick:()=>navigator.clipboard.writeText(a),className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:_.jsx(Ig,{size:14})}),_.jsx("button",{title:"Save",className:"p-1 bg-adobe-bg-primary/80 rounded text-xs",children:_.jsx(bf,{size:14})})]}),_.jsx("div",{dangerouslySetInnerHTML:{__html:t.codeToHtml(a,{lang:s,theme:"github-dark"})}})]},i)}return _.jsx("div",{children:o},i)})})},Xy=({message:e})=>{const t=e.role==="user";return _.jsx("div",{className:`flex gap-3 ${t?"justify-end":"justify-start"} mb-4`,children:_.jsx("div",{className:`max-w-[85%] rounded-2xl px-4 py-3 text-sm leading-relaxed shadow-sm ${t?"bg-[#404040] text-adobe-text-primary ml-8 rounded-br-md":"bg-[#2a2a2a] text-adobe-text-primary mr-8 rounded-bl-md border border-[#3a3a3a]"}`,children:_.jsx("div",{className:"whitespace-pre-wrap",children:_.jsx(Yy,{content:e.content})})})})},Jy="data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%201024%201024'%20fill='%23121212'%3e%3cpath%20d='M534.3%20164c-.1%202.5%201.1%203.7%203.6%203.6%202.2%200%204.3.1%206.5.1%2044.5%200%2089%20.2%20133.5%200%2048-.2%2088.8%2034.2%2097.3%2082.9%201.7%209.7%201.1%2019.6%201.1%2029.4%200%203%201%204.5%204%205.2%2021.8%204.5%2036%2019.1%2047.6%2036.7%2010.3%2015.5%2015.2%2033%2015.1%2051.4-.1%2022.4-7.2%2042.7-21.4%2060.4-7.8%209.7-17%2017.7-27.9%2023.7-3.7%202-7.5%203.6-11.7%204-4.4.4-5.7%202.5-5.7%206.9.2%2038%20.1%2076%20.1%20114%200%2011.3-4.7%2020-14.5%2025.5-9.5%205.4-19.2%204.7-28.4-1.1-12.9-8.2-25.2-17.4-38.5-25-14-8-28.7-12.4-45-12.4-100.3.2-200.6.4-301%200-34.5-.1-61.5-15.9-81.3-44-12-17-17-36.3-17-56.9%200-4.5-1.4-6.3-6-7.2-13.3-2.6-23.4-11.1-32.5-20.3-13.3-13.4-22.2-29.5-25.7-48.2-6.5-34.3%202.1-64.2%2027.2-88.9%209.3-9.1%2019.6-16.5%2032.9-18.4%203.3-.5%204-2.8%204-5.8-.1-10.8-.1-21.5%201.8-32.3%206.6-37.4%2038.2-70%2075.7-77.6%206.7-1.3%2013.4-2.1%2020.3-2%2045.8.1%2091.7.1%20137.5%200%206.7%200%206.7%200%206.7-6.5%200-8.7-.1-17.3.1-26%200-1.7-.2-3-.8-4.1-.6-1.1-1.6-2.1-3.2-2.9-19.6-10.6-29.3-27.7-28.9-49.4.6-26.8%2018.4-48.5%2046.5-52%2035.6-4.5%2063.9%2024%2060.8%2058.5-1.7%2019.1-11.5%2033.8-28.6%2042.9-1.7.9-2.8%201.9-3.5%203.1-.6%201.2-.8%202.6-.8%204.4M720%20244.4c-8.8-15.8-22.1-25.8-40.4-25.9-110.7-.3-221.3-.2-332-.1-23.9%200-45.3%2021.6-45.3%2045.5%200%2068.7%200%20137.3.1%20206%200%2025.8%2022%2047.7%2047.8%2047.7%2096.2%200%20192.3%200%20288.5.1%2010.1%200%2020.3-.5%2030.4.9%2013.8%201.9%2027.1%204.8%2039.4%2011.5%205.1%202.8%2010.1%205.6%2015.2%208.5%201.6-2.9.9-5.6.9-8.2%200-88.2%200-176.3.1-264.5%200-7.3-1.3-14.2-4.7-21.4M497.9%2071.5c-1.5%203.2-2.9%206.4-2.4%2010%201%208.6%208.4%2015.8%2016.4%2016.1%2010%20.5%2017.1-4.9%2019.5-14.8%201.7-7.4-3.3-15.9-11.4-19.4-7.6-3.2-16.7-.1-22%208.1z'/%3e%3cpath%20d='M665.2%20863.6c.6%208.2.5%2018-6.9%2023.4-10.8%208.2-25.2%203.9-37.6%204.9-64.3.1-128.7.3-193%200-11.4.4-21.8-9.8-20.3-21.4.3-15.5-.5-31.1.4-46.6%202.2-12.4%2015.9-16.4%2026.8-14.8%2071.3%200%20142.5-.2%20213.8.1%2011.1%201%2018.3%2012.4%2016.8%2022.9%200%2010.5%200%2020.9%200%2031.4zM426.3%20705.1c73.4.1%20146.8%200%20220.1%200%2011.7.4%2020.7%2012%2018.8%2023.4-.6%2014.4.5%2028.8-.4%2043.1-2%2013.2-16.4%2017.4-27.8%2015.7-70.7%200-141.5.2-212.2-.1-12-.8-19.1-13.4-17.3-24.5.2-13.8-.4-27.7.3-41.6%201.2-9.2%209.5-15.5%2018.5-16zM426.3%20600c73.4.1%20146.8%200%20220.1%200%2011.7.4%2020.7%2012%2018.8%2023.4-.6%2014.4.5%2028.8-.4%2043.1-2%2013.2-16.4%2017.4-27.8%2015.7-70.7%200-141.5.2-212.2-.1-12-.8-19.1-13.4-17.3-24.5.2-13.8-.4-27.7.3-41.6%201.2-9.2%209.5-15.5%2018.5-16zM309%20669.8c-9.1-10.4-14-25-10.2-38.6%204-14.8%2016.1-28%2031.6-30.9%2012.5-2.1%2026.5.5%2035.9%209.6%2012.5%2011.5%2017.9%2030.9%2011.2%2046.8-5.8%2014.9-20.7%2025.8-36.8%2026.2-11.9.6-23.6-4.4-31.7-13.1zM319.8%20887.5c-10.8-6-19.6-16.3-21.5-28.8-2.6-14.5%202.6-30.3%2014.1-39.8%2011.9-10.2%2029.8-13.1%2044-6%2016.5%207.8%2026.9%2027.1%2023.4%2045.2-3.1%2017.7-18.8%2033-37.1%2033.8-7.8.4-15.9-.7-22.9-4.4zM350.9%20706.6c16.2%204.9%2028.5%2020.3%2029.4%2037.2%201.2%2014.6-6.3%2029.4-18.7%2037.2-13.7%209.1-32.8%208.9-46.2-.5-14-9.2-20.5-27.4-16.6-43.5%203.5-15.9%2017.2-29%2033.3-31.6%206.3-1.2%2012.7-.5%2018.8%201.2z'/%3e%3cpath%20d='M547.7%20474c-23.5%208.4-50.3%207.8-72.7-3.4-21.2-9.9-38.2-28-47.8-49.2-5.2-7.6%204-18.2%2012-12.6%2014.2%207.7%2029%2014.7%2045%2017.8%2031.5%206.9%2065.4%202.6%2093.9-12.6%205.7-3.4%2013.6-7%2019.6-2.2%206.4%206.2-.8%2014.8-3.5%2021-10.4%2018.2-26.6%2033.6-46.5%2041.2z'/%3e%3cpath%20d='M595.3%20330.6c7.2-14.9%2024.4-23.9%2040.4-20.6%2016.3%203%2030.1%2017.9%2030.9%2035.1%201.1%2012.7-4.3%2026-14.4%2033.6-11.6%209.2-28.5%2010.7-41.3%203.1-12.6-6.9-20.6-21.5-19.6-36.1.3-5.2%201.7-10.4%204-15z'/%3e%3cpath%20d='M383.7%20376.7c-5.5%205-13.8%206.6-20.5%203.3-6.6-2.5-10-9.6-10.4-16.4-1.3-12.6%201.6-25.9%209.7-35.8%2010.5-13.5%2029.1-20.2%2045.7-16%2015%203.8%2028.8%2014.7%2033.3%2029.9%202.6%208.7%202.9%2018.1%201.1%2026.9-2.2%208.6-11.9%2014.1-20.5%2012-6.6-.9-12-6.3-13-12.8-1.1-5.3-.1-11.3-3.1-16.2-4.2-5-13.8-4.4-16.3%202.1-2.9%205.4.1%2011.6-2.5%2017.1-.8%202.1-2.1%204.1-3.5%205.9z'/%3e%3c/svg%3e",Zy=()=>{const{messages:e,isLoading:t,currentSession:n}=Ni(),r=O.useRef(null),o=O.useRef(null),[i,l]=O.useState(!1),s=O.useRef();O.useEffect(()=>{var u;(u=r.current)==null||u.scrollIntoView({behavior:"smooth"})},[e,t]),O.useEffect(()=>{const u=o.current;if(!u)return;const c=()=>{clearTimeout(s.current);const{scrollTop:p,scrollHeight:d,clientHeight:h}=u,v=d-(p+h)<100;l(!v),s.current=setTimeout(()=>{l(!1)},2e3)};return u.addEventListener("scroll",c),()=>{u.removeEventListener("scroll",c),clearTimeout(s.current)}},[]);const a=()=>{var u;(u=r.current)==null||u.scrollIntoView({behavior:"smooth"})};return _.jsxs("div",{ref:o,className:`flex-1 overflow-y-auto px-3 py-2 space-y-4
                chat-messages-scrollbar
                relative`,children:[(!n||e.length===0)&&_.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-adobe-text-secondary gap-3",children:[_.jsx("div",{className:"w-12 h-12 mb-2 flex items-center justify-center",children:_.jsx("img",{src:Jy,alt:"SahAI Logo",className:"w-10 h-10 brightness-0 invert"})}),_.jsx("h3",{className:"text-lg font-medium text-adobe-text-primary",children:"Start a conversation"}),_.jsx("p",{className:"text-sm text-center max-w-md",children:"Type a message below to begin chatting with SahAI"})]}),e.map(u=>_.jsx(Xy,{message:u},u.id)),t&&_.jsx("div",{className:"flex items-center gap-2 text-adobe-text-secondary text-sm",children:_.jsx("span",{children:"AI is thinking..."})}),_.jsx("div",{ref:r}),i&&_.jsx("button",{onClick:a,className:"absolute right-4 bottom-4 p-2 rounded-full bg-adobe-bg-tertiary border border-adobe-border text-adobe-text-primary hover:bg-adobe-bg-secondary transition-all duration-300 shadow-md","aria-label":"Scroll to bottom",children:_.jsx(Pg,{size:18})})]})},ev=Rs.memo(({onAttachFile:e,onVoiceInput:t})=>{const[n,r]=O.useState(""),[o,i]=O.useState(!1),l=O.useRef(null),{addMessage:s,isLoading:a,setLoading:u,currentSession:c,createNewSession:p}=Ni(),d=4e3,h=!n.trim(),v=n.length>d*.9;O.useEffect(()=>{const y=l.current;y&&(y.style.height="72px")},[]);const S=O.useCallback(()=>{const y=l.current;y&&(y.style.height="auto",y.style.height=`${Math.min(Math.max(y.scrollHeight,72),200)}px`)},[]),k=O.useCallback(y=>{r(y.target.value),S()},[S]),g=O.useCallback(async()=>{const y=n.trim();if(!(!y||a)){r(""),l.current&&(l.current.style.height="72px");try{u(!0),c||p(),s({content:y,role:"user"}),setTimeout(()=>{s({content:`Echo: ${y}`,role:"assistant"}),u(!1)},1e3)}catch{r(y),u(!1)}}},[n,a,c,s,u,p]),m=O.useCallback(y=>{y.key==="Enter"&&!y.shiftKey&&!o&&(y.preventDefault(),g())},[g,o]);return _.jsxs("div",{className:"px-4 pb-3 pt-2 bg-adobe-bg-secondary border-t border-adobe-border",children:[_.jsxs("div",{className:"relative flex items-center bg-transparent rounded-lg border-0 focus-within:border-adobe-accent focus-within:ring-1 focus-within:ring-adobe-accent transition-colors",children:[_.jsx("div",{className:"flex items-center pl-3",children:_.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-accent transition p-1.5 rounded",title:"Attach file",disabled:a,children:_.jsx(bg,{size:18})})}),_.jsx("textarea",{ref:l,rows:3,maxLength:d,value:n,onChange:k,onKeyDown:m,onCompositionStart:()=>i(!0),onCompositionEnd:()=>i(!1),placeholder:"Type a message...",className:`flex-1 resize-none bg-transparent text-adobe-text-primary text-sm p-3 outline-none placeholder:text-adobe-text-secondary/80
            min-h-[72px] max-h-[200px] leading-relaxed overflow-y-auto chat-messages-scrollbar`}),_.jsxs("div",{className:"flex items-center pr-3 space-x-1",children:[_.jsx("button",{onClick:t,className:"text-adobe-text-secondary hover:text-adobe-warning transition p-1.5 rounded disabled:opacity-40",title:"Voice input",disabled:a,children:_.jsx(Og,{size:18})}),_.jsx("button",{onClick:g,disabled:h||a,className:"text-adobe-accent hover:text-adobe-accent-hover transition p-1.5 rounded disabled:text-adobe-text-secondary/50 disabled:hover:text-adobe-text-secondary/50",title:"Send",children:a?_.jsx(Or,{size:18,className:"animate-spin"}):_.jsx(Mg,{size:18})})]})]}),_.jsxs("div",{className:"flex justify-between items-center mt-1 px-1",children:[_.jsxs("span",{className:`text-xs ${v?"text-adobe-warning":"text-adobe-text-secondary"}`,children:[n.length,"/",d]}),_.jsx("span",{className:"text-xs text-adobe-text-secondary",children:"Enter to send, Shift+Enter for new line"})]})]})}),tv=({provider:e,size:t=16,className:n="",...r})=>{const o={width:t,height:t,viewBox:"0 0 24 24",fill:"currentColor",className:`provider-logo ${n}`,...r};switch(e){case"openai":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142-.0852 4.783-2.7582a.7712.7712 0 0 0 .7806 0l5.8428 3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zm-2.4569-16.2971a4.4755 4.4755 0 0 1 2.3445-1.9275L5.943 7.1778a.7663.7663 0 0 0 .3717.6388l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0L4.2446 9.8211a4.504 4.504 0 0 1-.7876-8.4285zm16.5618 3.8558l-5.8428-3.3685V4.4444a.0804.0804 0 0 1 .0332-.0615l4.8645-2.8077a4.4992 4.4992 0 0 1 6.6802 4.66l-.1465.0804-4.7806 2.7582a.7712.7712 0 0 0-.7806 0zm2.0107-3.0231l-.142.0852-4.7806 2.7582a.7663.7663 0 0 0-.3717.6388L9.74 4.1818l2.0201-1.1686a.0757.0757 0 0 1 .071 0l4.8076 2.7748a4.504 4.504 0 0 1 .7876 8.4285z"})});case"anthropic":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2L2 22h4l2-5h8l2 5h4L12 2zm0 6l2.5 6h-5L12 8z"})});case"gemini":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"groq":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})});case"deepseek":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2l10 18H2L12 2zm0 3.5L5.5 18h13L12 5.5z"})});case"mistral":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"moonshot":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 1L9 9l-8 3 8 3 3 8 3-8 8-3-8-3-3-8z"})});case"openrouter":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2L2 12l10 10 10-10L12 2zm0 3.41L18.59 12 12 18.59 5.41 12 12 5.41z"})});case"perplexity":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6H9l3-3 3 3h-2v6z"})});case"qwen":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})});case"together":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm-6 0c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2z"})});case"vertex":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.18L19.82 8 12 11.82 4.18 8 12 4.18zM4 9.48l7 3.5v7.84l-7-3.5V9.48zm16 0v7.84l-7 3.5v-7.84l7-3.5z"})});case"xai":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M18.36 5.64L12 12l6.36 6.36-1.41 1.41L12 14.83l-4.95 4.94-1.41-1.41L12 12 5.64 5.64l1.41-1.41L12 9.17l4.95-4.94 1.41 1.41z"})});case"ollama":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"})});case"lmstudio":return _.jsx("svg",{...o,children:_.jsx("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})});default:return _.jsx("svg",{...o,children:_.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}},yl=[{value:"openai",label:"OpenAI"},{value:"anthropic",label:"Anthropic"},{value:"gemini",label:"Google Gemini"},{value:"groq",label:"Groq"},{value:"deepseek",label:"DeepSeek"},{value:"mistral",label:"Mistral"},{value:"moonshot",label:"Moonshot AI"},{value:"openrouter",label:"OpenRouter"},{value:"perplexity",label:"Perplexity"},{value:"qwen",label:"Alibaba Qwen"},{value:"together",label:"Together AI"},{value:"vertex",label:"Google Vertex AI"},{value:"xai",label:"xAI"},{value:"ollama",label:"Ollama"},{value:"lmstudio",label:"LM Studio"}],nv=()=>{const{closeModal:e}=Ur(),{providers:t,saveProviderSelection:n,loadModelsForProvider:r}=Ti(),[o,i]=O.useState(""),[l,s]=O.useState(""),[a,u]=O.useState(""),[c,p]=O.useState(""),[d,h]=O.useState(""),[v,S]=O.useState(!1),[k,g]=O.useState(!1),m=O.useRef(null),y=O.useRef(null),x=yl.filter(A=>A.label.toLowerCase().includes(c.toLowerCase())),w=t.find(A=>A.id===o),P=(w==null?void 0:w.models)||[],R=P.filter(A=>A.name.toLowerCase().includes(d.toLowerCase()));yl.find(A=>A.value===o),O.useEffect(()=>{o&&a&&t.find(Z=>Z.id===o)&&(n(o,{apiKey:a}),r(o))},[o,a]);const N=A=>{var Z;i(A),s(""),h(""),p(((Z=yl.find(Me=>Me.value===A))==null?void 0:Z.label)||""),S(!1),a&&r(A)},$=A=>{s(A);const Z=P.find(Me=>Me.id===A);h((Z==null?void 0:Z.name)||""),g(!1)},b=()=>{o&&l&&a&&(n(o,{apiKey:a,selectedModelId:l}),e())};return O.useEffect(()=>{const A=Z=>{m.current&&!m.current.contains(Z.target)&&S(!1),y.current&&!y.current.contains(Z.target)&&g(!1)};return document.addEventListener("mousedown",A),()=>document.removeEventListener("mousedown",A)},[]),_.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:_.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl",children:[_.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"AI Provider Configuration"}),_.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]})}),_.jsxs("div",{className:"p-6 space-y-6",children:[_.jsxs("div",{className:"relative",ref:m,children:[_.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Provider"}),_.jsxs("div",{className:"relative",children:[_.jsx("input",{type:"text",value:c,onChange:A=>{p(A.target.value),S(!0)},onFocus:()=>S(!0),placeholder:"Search providers...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10"}),_.jsx(fs,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18})]}),v&&_.jsx("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:x.map(A=>_.jsxs("div",{className:"px-4 py-3 cursor-pointer flex items-center space-x-3 hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>N(A.value),children:[_.jsx(tv,{provider:A.value,size:16}),_.jsx("span",{className:"font-medium",children:A.label})]},A.value))})]}),_.jsxs("div",{className:"relative",ref:y,children:[_.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"Model"}),_.jsxs("div",{className:"relative",children:[_.jsx("input",{type:"text",value:d,onChange:A=>{h(A.target.value),g(!0)},onFocus:()=>o&&g(!0),placeholder:o?"Search models...":"Select a provider first",disabled:!o,className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none pr-10 disabled:opacity-50"}),_.jsx(fs,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:18}),(w==null?void 0:w.isLoading)&&_.jsx(Or,{className:"absolute right-10 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary animate-spin",size:16})]}),k&&o&&_.jsxs("div",{className:"absolute z-20 mt-2 w-full bg-adobe-bg-secondary border border-adobe-border rounded-md shadow-xl max-h-60 overflow-auto",children:[(w==null?void 0:w.isLoading)&&P.length===0&&_.jsxs("div",{className:"px-4 py-3 text-adobe-text-secondary flex items-center space-x-2",children:[_.jsx(Or,{className:"animate-spin",size:16}),_.jsx("span",{children:"Loading models..."})]}),(w==null?void 0:w.error)&&P.length===0&&_.jsx("div",{className:"px-4 py-3 text-adobe-text-secondary",children:w.error}),R.map(A=>_.jsxs("div",{className:"px-4 py-3 cursor-pointer hover:bg-adobe-bg-tertiary text-adobe-text-primary",onClick:()=>$(A.id),children:[_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsx("span",{className:"font-medium",children:A.name}),A.isRecommended&&_.jsx("span",{className:"text-xs bg-adobe-accent/20 text-adobe-accent px-2 py-1 rounded",children:"Recommended"})]}),A.description&&_.jsx("p",{className:"text-xs text-adobe-text-secondary mt-1",children:A.description})]},A.id))]})]}),_.jsxs("div",{children:[_.jsx("label",{className:"block text-sm font-medium text-adobe-text-primary mb-2",children:"API Key"}),_.jsx("input",{type:"password",value:a,onChange:A=>u(A.target.value),placeholder:"Enter your API key",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-3 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"})]})]}),_.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[_.jsx("button",{onClick:e,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),_.jsx("button",{onClick:b,disabled:!o||!l||!a,className:"px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:"Save & Close"})]})]})})},rv=()=>{const{closeModal:e}=Ur(),[t,n]=O.useState({theme:"auto",autoSave:!0,showNotifications:!0,maxHistoryItems:100,debugMode:!1}),[r,o]=O.useState(!1),[i,l]=O.useState(!1),[s,a]=O.useState(!1),u=async()=>{a(!0);try{await Tt.save({appSettings:t}),e()}catch(h){console.error("Failed to save settings:",h)}finally{a(!1)}},c=async()=>{o(!0);try{const h=await Tt.exportSettings(),v=new Blob([h],{type:"application/json"}),S=URL.createObjectURL(v),k=document.createElement("a");k.href=S,k.download=`sahai-settings-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(k),k.click(),document.body.removeChild(k),URL.revokeObjectURL(S)}catch(h){console.error("Failed to export settings:",h)}finally{o(!1)}},p=()=>{const h=document.createElement("input");h.type="file",h.accept=".json",h.onchange=async v=>{var k;const S=(k=v.target.files)==null?void 0:k[0];if(S){l(!0);try{const g=await S.text();await Tt.importSettings(g),window.location.reload()}catch(g){console.error("Failed to import settings:",g),alert("Failed to import settings. Please check the file format.")}finally{l(!1)}}},h.click()},d=async()=>{if(confirm("Are you sure you want to clear all data? This action cannot be undone."))try{await Tt.clearSettings(),window.location.reload()}catch(h){console.error("Failed to clear settings:",h)}};return _.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:_.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[500px] shadow-2xl max-h-[80vh] overflow-hidden",children:[_.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Settings"}),_.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]})}),_.jsxs("div",{className:"p-6 space-y-6 overflow-y-auto max-h-[60vh]",children:[_.jsxs("div",{children:[_.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Appearance"}),_.jsx("div",{className:"space-y-3",children:_.jsxs("div",{children:[_.jsx("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:"Theme"}),_.jsxs("select",{value:t.theme,onChange:h=>n(v=>({...v,theme:h.target.value})),className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-3 py-2 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none",children:[_.jsx("option",{value:"auto",children:"Auto (System)"}),_.jsx("option",{value:"light",children:"Light"}),_.jsx("option",{value:"dark",children:"Dark"})]})]})})]}),_.jsxs("div",{children:[_.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"General"}),_.jsxs("div",{className:"space-y-3",children:[_.jsxs("label",{className:"flex items-center space-x-3",children:[_.jsx("input",{type:"checkbox",checked:t.autoSave,onChange:h=>n(v=>({...v,autoSave:h.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),_.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Auto-save conversations"})]}),_.jsxs("label",{className:"flex items-center space-x-3",children:[_.jsx("input",{type:"checkbox",checked:t.showNotifications,onChange:h=>n(v=>({...v,showNotifications:h.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),_.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Show notifications"})]}),_.jsxs("div",{children:[_.jsxs("label",{className:"block text-sm text-adobe-text-secondary mb-2",children:["Max history items (",t.maxHistoryItems,")"]}),_.jsx("input",{type:"range",min:"10",max:"500",step:"10",value:t.maxHistoryItems,onChange:h=>n(v=>({...v,maxHistoryItems:parseInt(h.target.value)})),className:"w-full h-2 bg-adobe-bg-secondary rounded-lg appearance-none cursor-pointer"})]})]})]}),_.jsxs("div",{children:[_.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Advanced"}),_.jsx("div",{className:"space-y-3",children:_.jsxs("label",{className:"flex items-center space-x-3",children:[_.jsx("input",{type:"checkbox",checked:t.debugMode,onChange:h=>n(v=>({...v,debugMode:h.target.checked})),className:"w-4 h-4 text-adobe-accent bg-adobe-bg-secondary border-adobe-border rounded focus:ring-adobe-accent focus:ring-2"}),_.jsx("span",{className:"text-sm text-adobe-text-primary",children:"Debug mode"})]})})]}),_.jsxs("div",{children:[_.jsx("h3",{className:"text-sm font-medium text-adobe-text-primary mb-3",children:"Data Management"}),_.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[_.jsxs("button",{onClick:c,disabled:r,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-adobe-bg-secondary border border-adobe-border rounded-md text-adobe-text-primary hover:bg-adobe-bg-tertiary transition-colors disabled:opacity-50",children:[_.jsx(bf,{size:16}),_.jsx("span",{children:r?"Exporting...":"Export"})]}),_.jsxs("button",{onClick:p,disabled:i,className:"flex items-center justify-center space-x-2 px-4 py-2 bg-adobe-bg-secondary border border-adobe-border rounded-md text-adobe-text-primary hover:bg-adobe-bg-tertiary transition-colors disabled:opacity-50",children:[_.jsx(zg,{size:16}),_.jsx("span",{children:i?"Importing...":"Import"})]}),_.jsxs("button",{onClick:d,className:"col-span-2 flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 border border-red-500 rounded-md text-white hover:bg-red-700 transition-colors",children:[_.jsx(ps,{size:16}),_.jsx("span",{children:"Clear All Data"})]})]})]})]}),_.jsxs("div",{className:"bg-adobe-bg-secondary border-t border-adobe-border p-4 flex justify-between",children:[_.jsx("button",{onClick:e,className:"px-4 py-2 text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"Cancel"}),_.jsxs("button",{onClick:u,disabled:s,className:"flex items-center space-x-2 px-6 py-2 bg-adobe-accent text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-adobe-accent/90 transition-colors",children:[_.jsx(jg,{size:16}),_.jsx("span",{children:s?"Saving...":"Save Settings"})]})]})]})})},ov=Ri((e,t)=>({sessions:[],currentSessionId:null,isLoading:!1,error:null,loadHistory:async()=>{e({isLoading:!0,error:null});try{const n=await ft("loadHistory()");if(n.success&&n.data){const r=Array.isArray(n.data)?n.data:[];e({sessions:r,isLoading:!1})}else e({sessions:[],isLoading:!1})}catch(n){console.error("Failed to load history:",n),e({error:n.message||"Failed to load chat history",isLoading:!1,sessions:[]})}},saveSession:async n=>{try{e(o=>({sessions:o.sessions.some(i=>i.id===n.id)?o.sessions.map(i=>i.id===n.id?n:i):[...o.sessions,n]}));const r=t().sessions;await ft(`saveHistory(${JSON.stringify(r)})`)}catch(r){console.error("Failed to save session:",r),e({error:r.message||"Failed to save session"})}},deleteSession:async n=>{try{e(o=>({sessions:o.sessions.filter(i=>i.id!==n),currentSessionId:o.currentSessionId===n?null:o.currentSessionId}));const r=t().sessions;await ft(`saveHistory(${JSON.stringify(r)})`)}catch(r){console.error("Failed to delete session:",r),e({error:r.message||"Failed to delete session"})}},clearHistory:async()=>{try{e({sessions:[],currentSessionId:null}),await ft("saveHistory([])")}catch(n){console.error("Failed to clear history:",n),e({error:n.message||"Failed to clear history"})}},createSession:n=>{const r={id:crypto.randomUUID(),title:n||`Chat ${new Date().toLocaleDateString()}`,messages:[],createdAt:Date.now(),updatedAt:Date.now()};return e(o=>({sessions:[r,...o.sessions],currentSessionId:r.id})),r},updateSession:(n,r)=>{e(o=>({sessions:o.sessions.map(i=>i.id===n?{...i,...r,updatedAt:Date.now()}:i)}))},setCurrentSession:n=>{e({currentSessionId:n})},getCurrentSession:()=>{const{sessions:n,currentSessionId:r}=t();return n.find(o=>o.id===r)||null},getSessionById:n=>{const{sessions:r}=t();return r.find(o=>o.id===n)||null},getSortedSessions:()=>{const{sessions:n}=t();return[...n].sort((r,o)=>o.updatedAt-r.updatedAt)}})),iv=()=>{const{closeModal:e}=Ur(),{sessions:t,isLoading:n,error:r,loadHistory:o,deleteSession:i,clearHistory:l,setCurrentSession:s,getSortedSessions:a}=ov(),{messages:u}=Ni(),[c,p]=O.useState(""),[d,h]=O.useState(null);O.useEffect(()=>{o()},[o]);const v=a().filter(x=>x.title.toLowerCase().includes(c.toLowerCase())||x.messages.some(w=>w.content.toLowerCase().includes(c.toLowerCase()))),S=x=>{s(x.id),e()},k=async(x,w)=>{w.stopPropagation(),confirm("Are you sure you want to delete this chat session?")&&await i(x)},g=async()=>{confirm("Are you sure you want to delete all chat history? This action cannot be undone.")&&await l()},m=x=>{const w=new Date(x),R=(new Date().getTime()-w.getTime())/(1e3*60*60);return R<24?w.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):R<24*7?w.toLocaleDateString([],{weekday:"short",hour:"2-digit",minute:"2-digit"}):w.toLocaleDateString([],{month:"short",day:"numeric",year:"numeric"})},y=x=>{const w=x.messages[x.messages.length-1];if(!w)return"No messages";const P=w.content.slice(0,100);return P.length<w.content.length?`${P}...`:P};return _.jsx("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-sm",children:_.jsxs("div",{className:"bg-adobe-bg-primary border border-adobe-border rounded-lg w-[700px] h-[600px] shadow-2xl flex flex-col",children:[_.jsx("div",{className:"bg-adobe-bg-secondary border-b border-adobe-border p-4",children:_.jsxs("div",{className:"flex items-center justify-between",children:[_.jsx("h2",{className:"text-lg font-semibold text-adobe-text-primary",children:"Chat History"}),_.jsx("button",{onClick:e,className:"text-adobe-text-secondary hover:text-adobe-text-primary transition-colors",children:"✕"})]})}),_.jsx("div",{className:"p-4 border-b border-adobe-border",children:_.jsxs("div",{className:"flex items-center space-x-3",children:[_.jsxs("div",{className:"relative flex-1",children:[_.jsx("input",{type:"text",value:c,onChange:x=>p(x.target.value),placeholder:"Search chat history...",className:"w-full bg-adobe-bg-secondary border border-adobe-border rounded-md px-4 py-2 pl-10 text-adobe-text-primary focus:border-adobe-accent focus:ring-1 focus:ring-adobe-accent outline-none"}),_.jsx(fs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-adobe-text-secondary",size:16})]}),_.jsxs("button",{onClick:g,disabled:t.length===0,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[_.jsx(ps,{size:16}),_.jsx("span",{children:"Clear All"})]})]})}),_.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[_.jsx("div",{className:"w-1/2 border-r border-adobe-border overflow-y-auto",children:n?_.jsx("div",{className:"flex items-center justify-center h-32",children:_.jsx(Or,{className:"animate-spin text-adobe-text-secondary",size:24})}):r?_.jsxs("div",{className:"p-4 text-center text-red-500",children:[_.jsx("p",{children:"Error loading history:"}),_.jsx("p",{className:"text-sm",children:r})]}):v.length===0?_.jsx("div",{className:"p-4 text-center text-adobe-text-secondary",children:c?"No matching sessions found":"No chat history yet"}):_.jsx("div",{className:"divide-y divide-adobe-border",children:v.map(x=>_.jsx("div",{className:`p-4 cursor-pointer hover:bg-adobe-bg-tertiary transition-colors ${(d==null?void 0:d.id)===x.id?"bg-adobe-bg-tertiary":""}`,onClick:()=>h(x),children:_.jsxs("div",{className:"flex items-start justify-between",children:[_.jsxs("div",{className:"flex-1 min-w-0",children:[_.jsx("h3",{className:"font-medium text-adobe-text-primary truncate",children:x.title}),_.jsx("p",{className:"text-sm text-adobe-text-secondary mt-1 line-clamp-2",children:y(x)}),_.jsxs("div",{className:"flex items-center space-x-3 mt-2 text-xs text-adobe-text-secondary",children:[_.jsxs("div",{className:"flex items-center space-x-1",children:[_.jsx(Fu,{size:12}),_.jsxs("span",{children:[x.messages.length," messages"]})]}),_.jsxs("div",{className:"flex items-center space-x-1",children:[_.jsx(Rg,{size:12}),_.jsx("span",{children:m(x.updatedAt)})]})]})]}),_.jsx("button",{onClick:w=>k(x.id,w),className:"ml-2 p-1 text-adobe-text-secondary hover:text-red-500 transition-colors",title:"Delete session",children:_.jsx(ps,{size:14})})]})},x.id))})}),_.jsx("div",{className:"w-1/2 flex flex-col",children:d?_.jsxs(_.Fragment,{children:[_.jsxs("div",{className:"p-4 border-b border-adobe-border",children:[_.jsx("h3",{className:"font-medium text-adobe-text-primary",children:d.title}),_.jsxs("p",{className:"text-sm text-adobe-text-secondary mt-1",children:[d.messages.length," messages • ",m(d.createdAt)]}),d.provider&&_.jsxs("p",{className:"text-xs text-adobe-text-secondary mt-1",children:[d.provider," • ",d.model]})]}),_.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:d.messages.map(x=>_.jsxs("div",{className:`p-3 rounded-lg ${x.role==="user"?"bg-adobe-accent/10 ml-8":"bg-adobe-bg-secondary mr-8"}`,children:[_.jsxs("div",{className:"flex items-center justify-between mb-2",children:[_.jsx("span",{className:"text-xs font-medium text-adobe-text-secondary uppercase",children:x.role}),_.jsx("span",{className:"text-xs text-adobe-text-secondary",children:m(x.timestamp)})]}),_.jsx("p",{className:"text-sm text-adobe-text-primary whitespace-pre-wrap",children:x.content})]},x.id))}),_.jsx("div",{className:"p-4 border-t border-adobe-border",children:_.jsx("button",{onClick:()=>S(d),className:"w-full px-4 py-2 bg-adobe-accent text-white rounded-md hover:bg-adobe-accent/90 transition-colors",children:"Load This Conversation"})})]}):_.jsx("div",{className:"flex-1 flex items-center justify-center text-adobe-text-secondary",children:_.jsxs("div",{className:"text-center",children:[_.jsx(Fu,{size:48,className:"mx-auto mb-4 opacity-50"}),_.jsx("p",{children:"Select a session to preview"})]})})})]})]})})},lv=()=>{const{modal:e}=Ur();if(!e)return null;switch(e){case"provider":return _.jsx(nv,{});case"settings":return _.jsx(rv,{});case"chat-history":return _.jsx(iv,{});default:return null}},sv=()=>_.jsxs("div",{className:"flex flex-col h-screen bg-adobe-bg text-adobe-text font-sans",children:[_.jsx(Ug,{}),_.jsx(Zy,{}),_.jsx(ev,{}),_.jsx(lv,{})]});Nf();Ti.getState().loadSettings();vl.createRoot(document.getElementById("root")).render(_.jsx(Rs.StrictMode,{children:_.jsx(sv,{})}));
